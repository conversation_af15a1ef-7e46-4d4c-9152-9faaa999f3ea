﻿using SqlSugar;

namespace BPM.Domain.Entity.refund;

/// <summary>
/// 退款明细细表
/// </summary>
[SugarTable("REFUND_ITEM")]
[Tenant("IPOS-ORDER")]
public class refundItemEntity
{
    /// <summary>
    /// 明细序号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string order_seq { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string order_no { get; set; }

    /// <summary>
    /// 订单明细id
    /// </summary>
    public string oid { get; set; }

    /// <summary>
    /// 订单数量
    /// </summary>
    public int item_num { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal refund_fee { get; set; }

}
