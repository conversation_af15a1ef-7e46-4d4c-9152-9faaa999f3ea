﻿@if(Model.IsUploading)
{
@:using BPM.Common.Models;
}
using BPM.JsonSerialization;
using Newtonsoft.Json;

namespace BPM.@(Model.NameSpace).<EMAIL>;
 
/// <summary>
/// @(Model.BusName)修改输入参数.
/// </summary>
public class @(Model.ClassName)CrInput
{
@foreach (var column in Model.TableField)
{
@if (column.bpmKey != null)
{
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<List<string>> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
break;
case "switch":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:[JsonConverter(typeof(BoolJsonConverter))]
    @:public bool bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
case "checkbox":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
case "radio":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<FileControlsModel> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
case "createTime":
case "modifyTime":
    
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
}
}
}
}