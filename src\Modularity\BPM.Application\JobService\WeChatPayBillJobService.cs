using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;
using BPM.Application.Bill;

namespace BPM.Application;

/// <summary>
/// 本地任务-下载微信支付账单.
/// </summary>
[JobDetail("job_download_wechatpay_bill", Description = "下载微信支付账单", GroupName = "BuiltIn", Concurrent = true)]
public class WeChatPayBillJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 微信支付账单服务.
    /// </summary>
    private readonly WeChatPayBillService _weChatPayBillService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public WeChatPayBillJobService(IServiceScopeFactory serviceScopeFactory, WeChatPayBillService weChatPayBillService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _weChatPayBillService = weChatPayBillService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _weChatPayBillService.DownloadWechatPayBill();
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 