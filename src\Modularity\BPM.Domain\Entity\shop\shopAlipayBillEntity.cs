﻿using BPM.Common.Const;
using SqlSugar;

namespace BPM.Domain.Entity.shop;

/// <summary>
/// 门店支付宝对账单
/// 版 本：V3.2
/// 版 权：中畅源科技开发有限公司（https://www.szclouds.com）
/// 作 者：Aarons
/// 日 期：2023-01-05.
/// </summary>
[SugarTable("SHOP_BILL_ALIPAY")]
[Tenant(ClaimConst.TENANTID)]
public class shopAlipayBillEntity
{
    /// <summary>
    /// 编号
    /// </summary>
    [SugarColumn(ColumnName = "F_id")]
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    [SugarColumn(ColumnName = "F_ShopId")]
    public string shop_id { get; set; }

    /// <summary>
    /// 支付宝交易单号
    /// </summary>
    [SugarColumn(ColumnName = "F_TransactionId")]
    public string transaction_id { get; set; }

    /// <summary>
    /// 商户订单号
    /// </summary>
    [SugarColumn(ColumnName = "F_OutTradeNo")]
    public string out_trade_no { get; set; }

    /// <summary>
    /// 终端号
    /// </summary>
    [SugarColumn(ColumnName = "F_TerminalId")]
    public string terminal_id { get; set; }

    /// <summary>
    /// 业务员编号
    /// </summary>
    [SugarColumn(ColumnName = "F_OperatorId")]
    public string operator_id { get; set; }

    /// <summary>
    /// 交易金额
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeFee")]
    public decimal trade_fee { get; set; }

    /// <summary>
    /// 手续费
    /// </summary>
    [SugarColumn(ColumnName = "F_Charge")]
    public decimal charge { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeType")]
    public string trade_type { get; set; }

    /// <summary>
    /// 交易时间
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeTime")]
    public DateTime trade_time { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [SugarColumn(ColumnName = "F_CompleteDate")]
    public DateTime finish_time { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    [SugarColumn(ColumnName = "F_Body")]
    public string body { get; set; }
    /// <summary>
    /// 对账日期
    /// </summary>
    [SugarColumn(ColumnName = "F_BillDate")]
    public string bill_date { get; set; }

    /// <summary>
    /// 商户退款交易号
    /// </summary>
    [SugarColumn(ColumnName = "F_OutRefundNo")]
    public string out_refund_no { get; set; }

}

