﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 会员等级模板
/// </summary>
[SugarTable("MEMBER_GRADE_TEMPLATE")]
public class memberGradeTemplateEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 等级编号
    /// </summary>
    public string grade_code { get; set; }

    /// <summary>
    /// 来源等级编号
    /// </summary>
    public string source_grade_code { get; set; }

    /// <summary>
    /// 等级名称
    /// </summary>
    public string grade_name { get; set; }

    /// <summary>
    /// 生日折扣
    /// </summary>
    public decimal? birthday_disc { get; set; }

    /// <summary>
    /// 折扣
    /// </summary>
    public decimal? disc { get; set; }

    /// <summary>
    /// 等级类型：1.免费类型2.付费类型
    /// </summary>
    public int type { get; set; }


    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_date { get; set; }

    /// <summary>
    /// 创建用户id
    /// </summary>
    public string created_user_id { get; set; }

    /// <summary>
    /// 修改用户
    /// </summary>
    public string modify_user_id { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime modify_date { get; set; }

}

