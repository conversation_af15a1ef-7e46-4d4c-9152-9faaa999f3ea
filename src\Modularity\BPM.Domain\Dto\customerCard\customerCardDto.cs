﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.customerCard;

/// <summary>
/// 客户会员卡信息.
/// </summary>
[SuppressSniffer]
public class customerCardDto
{
    /// <summary>
    /// 会员卡别名
    /// </summary>
    public string card_alias { get; set; }

    /// <summary>
    /// 用户领取到的会员卡卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 事件发生时间
    /// </summary>
    public string event_time { get; set; }

    /// <summary>
    /// 用户粉丝id
    /// </summary>
    public int fans_id { get; set; }

    /// <summary>
    /// 用户粉丝类型
    /// </summary>
    public int fans_type { get; set; }

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string mobile { get; set; }

    /// <summary>
    /// 用户的有赞userId
    /// </summary>
    public long user_id { get; set; }

    /// <summary>
    /// 有赞openId
    /// </summary>
    public string yz_open_id { get; set; }
}
