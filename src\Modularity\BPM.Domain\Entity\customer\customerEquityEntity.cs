﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 客户权益实体
/// </summary>
[SugarTable("CUSTOMER_EQUITY")]
[Tenant("IPOS-CRM")]
public class customerEquityEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    ///  客户编号
    /// </summary>
    public string customer_id { get; set; }

    /// <summary>
    ///  客户来源编号
    /// </summary>
    public string customer_request_id { get; set; }

    /// <summary>
    ///  有赞id
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    ///  手机号
    /// </summary>
    public string phone { get; set; }

    /// <summary>
    ///  状态 -1 删除,0-禁用,1-启用
    /// </summary>
    public int status { get; set; }

    /// <summary>
    ///  权益卡id
    /// </summary>
    public string equity_card_alias_id { get; set; }

    /// <summary>
    ///  权益卡号(有赞分配权益卡返回)
    /// </summary>
    public string equity_card_no { get; set; }
    /// <summary>
    ///  ERP卡号
    /// </summary>
    public string member_card_no { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime? created_date { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? start_time { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? end_time { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime modify_date { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }

    /// <summary>
    /// 同步内容
    /// </summary>
    public string tag_body { get; set; }

    /// <summary>
    /// mall同步标记
    /// </summary>
    public string tag_mall_status { get; set; }

    /// <summary>
    /// 有赞版本号
    /// </summary>
    public long? yzVersion { get; set; }
}

