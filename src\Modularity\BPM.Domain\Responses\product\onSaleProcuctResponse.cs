﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.product;

/// <summary>
/// 在售商品响应
/// </summary>
[SuppressSniffer]
public class onSaleProcuctResponse
{
    /// <summary>
    /// 商品集合
    /// </summary>
    public List<Items> items { get; set; }

    /// <summary>
    /// 每页条数。默认30条
    /// </summary>
    public int page_size { get; set; }

    /// <summary>
    /// 分页
    /// </summary>
    public int page { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int count { get; set; }

    /// <summary>
    ///  商品明细
    /// </summary>
    public class Items
    {
        /// <summary>
        /// 商品编码，商家可以自定义参数，支持英文和数据组合
        /// </summary>
        public string item_no { get; set; }

        /// <summary>
        /// 商品SPU维度的条形码，用于扫码快速搜索商品，使用场景如：扫码购、扫码搜索商品（仅支持实物商品）
        /// </summary>
        public string barcode { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string title { get; set; }

        /// <summary>
        /// 商品Id，微商城店铺下商品唯一标识
        /// </summary>
        public string item_id { get; set; }

        /// <summary>
        /// 商品价格，单位：分
        /// </summary>
        public long price { get; set; }

        /// <summary>
        /// 划线价，单位：元
        /// </summary>
        public string origin { get; set; }

        /// <summary>
        /// 商品类型：0—普通商品 3—UMP降价拍 5—外卖商品 10—分销商品 20—会员卡商品 21—礼品卡商品 22—团购券 25—批发商品 30—收银台商品 31—知识付费商品 35—酒店商品 40—美业商品 60—虚拟商品 61—电子卡券
        /// </summary>
        public int item_type { get; set; }


        /// <summary>
        /// 最后更新时间 格式"yyyy-MM-dd HH:mm:ss"
        /// </summary>
        public DateTime update_time { get; set; }

        /// <summary>
        ///  创建时间，格式"yyyy-MM-dd HH:mm:ss"
        /// </summary>
        public DateTime created_time { get; set; }

        /// <summary>
        /// 有赞连锁总部店铺id，仅供有赞连锁场景下使用。有赞平台生成，在有赞平台唯一，用于判断信息属于哪一个总部。
        /// </summary>
        public string root_kdt_id { get; set; }

        /// <summary>
        /// 总部商品id
        /// </summary>
        public string root_item_id { get; set; }

        /// <summary>
        /// 店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店
        /// </summary>
        public int channel { get; set; }

    }
}
