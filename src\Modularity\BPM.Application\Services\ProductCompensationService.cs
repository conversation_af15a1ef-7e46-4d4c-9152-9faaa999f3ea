using BPM.Application.Configuration;
using BPM.Common.Core;
using BPM.DependencyInjection;
using BPM.Domain.Entity.product;
using BPM.Domain.Requests.product;
using Microsoft.Extensions.Options;
using SqlSugar;

namespace BPM.Application.Services;

/// <summary>
/// 商品补偿服务
/// </summary>
[SuppressSniffer]
public class ProductCompensationService
{
    private readonly ISqlSugarClient _repository;
    private readonly ProductSyncConfiguration _config;
    private readonly ProductBatchProcessor _batchProcessor;
    private readonly ProductSyncResultHandler _resultHandler;

    public ProductCompensationService(
        ISqlSugarClient repository,
        IOptions<ProductSyncConfiguration> configOptions,
        ProductBatchProcessor batchProcessor,
        ProductSyncResultHandler resultHandler)
    {
        _repository = repository.AsTenant().GetConnectionWithAttr<productEntity>();
        _config = configOptions.Value;
        _batchProcessor = batchProcessor;
        _resultHandler = resultHandler;
    }

    /// <summary>
    /// 执行补偿逻辑
    /// </summary>
    /// <param name="processProductsFunc">处理商品的函数</param>
    /// <returns>是否成功</returns>
    public async Task<bool> ExecuteCompensationAsync(Func<getOnSaleProductRequest, Task<dynamic>> processProductsFunc)
    {
        var success = true;

        // 1. 重新同步被删除的门店商品
        if (_config.EnableStoreProductResync && _resultHandler.DeletedStoreBarcodes.Count > 0)
        {
            Console.WriteLine("开始执行门店商品重新同步补偿逻辑");
            var resyncSuccess = await _batchProcessor.BatchResyncStoreProductsAsync(
                _resultHandler.DeletedStoreBarcodes,
                processProductsFunc);
            success &= resyncSuccess;
        }

        // 2. 补全只有单条记录的商品
        if (_config.EnableProductCompletion)
        {
            Console.WriteLine("开始执行商品补全补偿逻辑");
            var completeSuccess = await ExecuteProductCompletionAsync(processProductsFunc);
            success &= completeSuccess;
        }

        return success;
    }

    /// <summary>
    /// 执行商品补全
    /// </summary>
    /// <param name="processProductsFunc">处理商品的函数</param>
    /// <returns>是否成功</returns>
    public async Task<bool> ExecuteProductCompletionAsync(Func<getOnSaleProductRequest, Task<dynamic>> processProductsFunc)
    {
        try
        {
            // 查询只有1条记录的商品条码
            var incompleteProducts = await GetIncompleteProductsAsync(_config.CompleteMaxProcessCount);
            
            if (incompleteProducts.Count == 0)
            {
                Console.WriteLine("没有发现需要补全的商品");
                return true;
            }

            Console.WriteLine($"发现 {incompleteProducts.Count} 个只有单条记录的商品条码");

            // 获取这些商品的详细信息
            var barCodes = incompleteProducts.Select(x => (string)x.bar_code).ToList();
            var existingProducts = await GetExistingProductsAsync(barCodes);

            if (existingProducts.Count == 0)
            {
                Console.WriteLine("未找到对应的商品记录");
                return false;
            }

            // 批量处理商品补全
            var processedBarcodes = new List<string>();
            foreach (var product in existingProducts)
            {
                // 确定需要补全的渠道
                int targetChannel = product.channel == 0 ? 1 : 0;
                string channelName = targetChannel == 0 ? "网店" : "门店";

                Console.WriteLine($"商品 {product.bar_code} 当前渠道: {product.channel}, 需要补全渠道: {targetChannel}({channelName})");
                processedBarcodes.Add(product.bar_code);
            }

            // 使用批量处理器处理
            return await _batchProcessor.BatchCompleteProductsAsync(processedBarcodes, processProductsFunc);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"执行商品补全时发生错误: {ex.Message}");
            _resultHandler.AddError($"商品补全失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取不完整的商品列表
    /// </summary>
    /// <param name="maxCount">最大数量</param>
    /// <returns>不完整的商品列表</returns>
    private async Task<List<dynamic>> GetIncompleteProductsAsync(int maxCount)
    {
        return await _resultHandler.SafeExecuteAsync(async () =>
        {
            var result = await _repository.Queryable<productEntity>()
                .GroupBy(x => x.bar_code)
                .Having(x => SqlFunc.AggregateCount(x.bar_code) == 1)
                .Select(x => new {
                    bar_code = x.bar_code,
                    count = SqlFunc.AggregateCount(x.bar_code)
                })
                .Take(maxCount)
                .ToListAsync();
            return result.Cast<dynamic>().ToList();
        }, "查询不完整商品", new List<dynamic>());
    }

    /// <summary>
    /// 获取现有商品列表
    /// </summary>
    /// <param name="barCodes">条码列表</param>
    /// <returns>现有商品列表</returns>
    private async Task<List<productEntity>> GetExistingProductsAsync(List<string> barCodes)
    {
        return await _resultHandler.SafeExecuteAsync(async () =>
        {
            return await _repository.Queryable<productEntity>()
                .Where(x => barCodes.Contains(x.bar_code))
                .ToListAsync();
        }, "查询现有商品", new List<productEntity>());
    }

    /// <summary>
    /// 检查是否需要执行补偿逻辑
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>是否需要执行</returns>
    public bool ShouldExecuteCompensation(getOnSaleProductRequest request)
    {
        // 如果是查询特定商品（有q参数），则不执行补偿逻辑
        if (!string.IsNullOrEmpty(request.q))
        {
            Console.WriteLine($"检测到查询特定商品(q={request.q})，跳过补偿逻辑");
            return false;
        }

        // 如果是小范围时间查询，也可以跳过补偿逻辑
        if (request.update_time_start.HasValue && request.update_time_end.HasValue)
        {
            var timeSpan = request.update_time_end.Value - request.update_time_start.Value;
            var hours = timeSpan / (1000 * 60 * 60); // 转换为小时

            if (hours < 24) // 小于24小时的查询
            {
                Console.WriteLine($"检测到小范围时间查询({hours:F1}小时)，跳过补偿逻辑");
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 获取补偿统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public object GetCompensationStatistics()
    {
        return new
        {
            DeletedStoreBarcodes = _resultHandler.DeletedStoreBarcodes.Count,
            EnableStoreProductResync = _config.EnableStoreProductResync,
            EnableProductCompletion = _config.EnableProductCompletion,
            CompleteMaxProcessCount = _config.CompleteMaxProcessCount,
            CompleteBatchSize = _config.CompleteBatchSize
        };
    }
}
