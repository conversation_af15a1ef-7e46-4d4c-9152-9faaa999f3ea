{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"BPM.API.Entry/3.6.0": {"dependencies": {"BPM.Application": "3.6.0", "BPM.Message": "5.0.1", "BPM.OAuth": "5.0.1", "BPM.Systems": "5.0.1", "BPM.TaskScheduler": "5.0.1", "BPM.WorkFlow": "5.0.1", "IGeekFan.AspNetCore.Knife4jUI": "0.0.13", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.19.6", "Roslynator.Analyzers": "4.12.4", "StyleCop.Analyzers": "1.2.0-beta.406"}, "runtime": {"BPM.API.Entry.dll": {}}}, "AdvancedStringBuilder/0.1.1": {"runtime": {"lib/netstandard2.0/AdvancedStringBuilder.dll": {"assemblyVersion": "0.1.1.0", "fileVersion": "0.1.1.0"}}}, "AlibabaCloud.EndpointUtil/0.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.EndpointUtil.dll": {"assemblyVersion": "0.1.1.0", "fileVersion": "0.1.1.0"}}}, "AlibabaCloud.GatewaySpi/0.0.2": {"dependencies": {"Aliyun.Credentials": "1.3.2", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.GatewaySpi.dll": {"assemblyVersion": "0.0.1.0", "fileVersion": "0.0.1.0"}}}, "AlibabaCloud.OpenApiClient/0.1.10": {"dependencies": {"AlibabaCloud.GatewaySpi": "0.0.2", "AlibabaCloud.OpenApiUtil": "1.1.1", "AlibabaCloud.TeaUtil": "0.1.18", "AlibabaCloud.TeaXML": "0.0.5", "Aliyun.Credentials": "1.3.2", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiClient.dll": {"assemblyVersion": "0.1.9.0", "fileVersion": "0.1.9.0"}}}, "AlibabaCloud.OpenApiUtil/1.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.OpenApiUtil.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "AlibabaCloud.SDK.Dysmsapi20170525/3.0.0": {"dependencies": {"AlibabaCloud.EndpointUtil": "0.1.1", "AlibabaCloud.OpenApiClient": "0.1.10", "AlibabaCloud.OpenApiUtil": "1.1.1", "AlibabaCloud.TeaUtil": "0.1.18", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.SDK.Dysmsapi20170525.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AlibabaCloud.TeaUtil/0.1.18": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaUtil.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AlibabaCloud.TeaXML/0.0.5": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/AlibabaCloud.TeaXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AlipaySDKNet.Standard/4.6.442": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Portable.BouncyCastle": "1.8.9", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/AlipaySDKNet.Standard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26419.2"}}}, "Aliyun.Credentials/1.3.2": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3", "Tea": "1.1.2"}, "runtime": {"lib/netstandard2.0/Aliyun.Credentials.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Aliyun.OSS.SDK.NetCore/2.13.0": {"runtime": {"lib/netstandard2.0/Aliyun.OSS.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Aspose.Cells/21.8.0": {"dependencies": {"System.Drawing.Common": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net50/Aspose.Cells.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Aspose.Words/21.8.0": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "SkiaSharp": "2.88.6", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/Aspose.Words.Pdf2Word.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netstandard2.0/Aspose.Words.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "BceSdkDotNetCore/1.0.2.911": {"dependencies": {"Newtonsoft.Json": "13.0.3", "log4net": "2.0.8"}, "runtime": {"lib/netstandard2.0/BceSdkDotNetCore.dll": {"assemblyVersion": "1.0.2.911", "fileVersion": "1.0.2.911"}}}, "Ben.Demystifier/0.4.1": {"dependencies": {"System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BouncyCastle.Cryptography/2.4.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.4.0.33771"}}}, "BPM/5.0.1": {"dependencies": {"BPM.Extras.DependencyModel.CodeAnalysis": "5.0.1", "CSRedisCore": "3.8.670", "MiniProfiler.AspNetCore.Mvc": "4.5.4", "Swashbuckle.AspNetCore": "8.1.1"}, "runtime": {"lib/net8.0/BPM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Apps.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Apps.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Common/5.0.1": {"dependencies": {"Aspose.Cells": "21.8.0", "Aspose.Words": "21.8.0", "BPM.Extras.Authentication.JwtBearer": "5.0.1", "BPM.Extras.DatabaseAccessor.SqlSugar": "5.0.1", "BPM.Extras.ObjectMapper.Mapster": "5.0.1", "FreeSpire.Office": "8.2.0", "IPTools.China": "1.6.0", "NPOI": "2.5.5", "SixLabors.ImageSharp": "3.0.2", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.6", "System.Diagnostics.PerformanceCounter": "6.0.1", "System.Management": "6.0.2", "UAParser": "3.1.47", "Yitter.IdGenerator": "1.0.14"}, "runtime": {"lib/net8.0/BPM.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Common.CodeGen/5.0.1": {"dependencies": {"BPM.VisualDev": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Common.CodeGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Common.Core/5.0.1": {"dependencies": {"BPM.Engine.Entity": "5.0.1", "BPM.Extras.Authentication.JwtBearer": "5.0.1", "BPM.Extras.EventBus.RabbitMQ": "5.0.1", "BPM.Extras.WebSockets": "5.0.1", "BPM.InteAssistant.Entitys": "5.0.1", "BPM.Message.Entitys": "5.0.1", "BPM.Systems.Entitys": "5.0.1", "BPM.TaskScheduler.Entitys": "5.0.1", "BPM.VisualDev.Entitys": "5.0.1", "DotNetCore.Natasha.CSharp": "*******", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.6"}, "runtime": {"lib/net8.0/BPM.Common.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Engine.Entity/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Engine.Entity.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extend.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Extend.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extend.Interfaces/5.0.1": {"dependencies": {"BPM.Extend.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Extend.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.Authentication.JwtBearer/5.0.1": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.11"}, "runtime": {"lib/net8.0/BPM.Extras.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.CollectiveOAuth/5.0.1": {"dependencies": {"AlipaySDKNet.Standard": "4.6.442", "BPM.Common": "5.0.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.1", "System.Configuration.ConfigurationManager": "8.0.0", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/net8.0/BPM.Extras.CollectiveOAuth.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.DatabaseAccessor.SqlSugar/5.0.1": {"dependencies": {"BPM": "5.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "SqlSugarCore": "*********"}, "runtime": {"lib/net8.0/BPM.Extras.DatabaseAccessor.SqlSugar.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.DependencyModel.CodeAnalysis/5.0.1": {"dependencies": {"Ben.Demystifier": "0.4.1", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.11", "Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.11.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.11", "Microsoft.Extensions.DependencyModel": "8.0.2", "System.Text.Json": "8.0.5", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/net8.0/BPM.Extras.DependencyModel.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.EventBus.RabbitMQ/5.0.1": {"dependencies": {"RabbitMQ.Client": "6.4.0"}, "runtime": {"lib/net8.0/BPM.Extras.EventBus.RabbitMQ.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.ObjectMapper.Mapster/5.0.1": {"dependencies": {"Mapster": "7.4.0", "Mapster.DependencyInjection": "1.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/BPM.Extras.ObjectMapper.Mapster.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.Thirdparty/5.0.1": {"dependencies": {"AlibabaCloud.SDK.Dysmsapi20170525": "3.0.0", "BPM.Common": "5.0.1", "DingDing.SDK.NetCore": "2021.1.7.1", "JavaScriptEngineSwitcher.ChakraCore": "3.26.0", "JavaScriptEngineSwitcher.ChakraCore.Native.win-x64": "3.26.0", "JavaScriptEngineSwitcher.V8": "3.24.2", "JavaScriptEngineSwitcher.V8.Native.linux-x64": "3.24.2", "JavaScriptEngineSwitcher.V8.Native.win-x64": "3.24.2", "MailKit": "4.7.0", "OnceMi.AspNetCore.OSS": "1.2.0", "Senparc.Weixin.MP": "16.21.2", "Senparc.Weixin.Work": "3.21.2", "TencentCloudSDK.Sms": "3.0.1045"}, "runtime": {"lib/net8.0/BPM.Extras.Thirdparty.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.WebSockets/5.0.1": {"dependencies": {"BPM.Common": "5.0.1", "BPM.Extras.Authentication.JwtBearer": "5.0.1", "Microsoft.AspNetCore.WebSockets": "2.2.1"}, "runtime": {"lib/net8.0/BPM.Extras.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.InteAssistant.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.InteAssistant.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Message/5.0.1": {"dependencies": {"BPM.Common.Core": "5.0.1", "BPM.Message.Interfaces": "5.0.1", "BPM.Systems.Interfaces": "5.0.1", "BPM.WorkFlow.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Message.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Message.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Message.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Message.Interfaces/5.0.1": {"dependencies": {"BPM.Message.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Message.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.OAuth/5.0.1": {"dependencies": {"BPM.Common.Core": "5.0.1", "BPM.Message.Interfaces": "5.0.1", "BPM.Systems.Interfaces": "5.0.1"}, "runtime": {"lib/net8.0/BPM.OAuth.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Systems/5.0.1": {"dependencies": {"BPM.Extras.CollectiveOAuth": "5.0.1", "BPM.Message.Interfaces": "5.0.1", "BPM.Systems.Interfaces": "5.0.1", "BPM.TaskScheduler.Interfaces": "5.0.1", "BPM.VisualDev.Engine": "5.0.1", "BPM.WorkFlow.Interfaces": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Systems.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Systems.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1", "BPM.Extras.CollectiveOAuth": "5.0.1", "BPM.Extras.Thirdparty": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Systems.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Systems.Interfaces/5.0.1": {"dependencies": {"BPM.Apps.Entitys": "5.0.1", "BPM.Systems.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.Systems.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.TaskScheduler/5.0.1": {"dependencies": {"BPM.Common.Core": "5.0.1", "BPM.Systems.Interfaces": "5.0.1", "BPM.TaskScheduler.Interfaces": "5.0.1"}, "runtime": {"lib/net8.0/BPM.TaskScheduler.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.TaskScheduler.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.TaskScheduler.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.TaskScheduler.Interfaces/5.0.1": {"dependencies": {"BPM.TaskScheduler.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.TaskScheduler.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.VisualDev/5.0.1": {"dependencies": {"BPM.Common.Core": "5.0.1", "BPM.Extend.Interfaces": "5.0.1", "BPM.Message.Interfaces": "5.0.1", "BPM.Systems.Interfaces": "5.0.1", "BPM.VisualDev.Engine": "5.0.1", "BPM.VisualDev.Interfaces": "5.0.1", "BPM.WorkFlow.Interfaces": "5.0.1"}, "runtime": {"lib/net8.0/BPM.VisualDev.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.VisualDev.Engine/5.0.1": {"dependencies": {"BPM.Common.Core": "5.0.1", "BPM.Engine.Entity": "5.0.1", "BPM.Systems.Interfaces": "5.0.1", "BPM.VisualDev.Interfaces": "5.0.1", "BPM.WorkFlow.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.VisualDev.Engine.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.VisualDev.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.VisualDev.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.VisualDev.Interfaces/5.0.1": {"dependencies": {"BPM.Systems.Entitys": "5.0.1", "BPM.VisualDev.Entitys": "5.0.1", "BPM.WorkFlow.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.VisualDev.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.WorkFlow/5.0.1": {"dependencies": {"BPM.Common.Core": "5.0.1", "BPM.Message.Interfaces": "5.0.1", "BPM.VisualDev.Engine": "5.0.1", "BPM.WorkFlow.Interfaces": "5.0.1"}, "runtime": {"lib/net8.0/BPM.WorkFlow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.WorkFlow.Entitys/5.0.1": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"lib/net8.0/BPM.WorkFlow.Entitys.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.WorkFlow.Interfaces/5.0.1": {"dependencies": {"BPM.Systems.Entitys": "5.0.1", "BPM.VisualDev.Entitys": "5.0.1", "BPM.WorkFlow.Entitys": "5.0.1"}, "runtime": {"lib/net8.0/BPM.WorkFlow.Interfaces.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.HighPerformance/8.1.0": {"runtime": {"lib/net7.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.0.1"}}}, "CSRedisCore/3.8.670": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/CSRedisCore.dll": {"assemblyVersion": "3.8.670.0", "fileVersion": "3.8.670.0"}}}, "CsvHelper/33.0.1": {"runtime": {"lib/net8.0/CsvHelper.dll": {"assemblyVersion": "3*******", "fileVersion": "33.0.1.24"}}}, "DingDing.SDK.NetCore/2021.1.7.1": {"runtime": {"lib/netcoreapp2.1/topsdk-net-core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.Natasha.CSharp/*******": {"dependencies": {"DotNetCore.Natasha.Domain": "*******", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.11.0", "Microsoft.Extensions.DependencyModel": "8.0.2", "Microsoft.SourceLink.GitHub": "1.1.0", "System.Reflection.MetadataLoadContext": "7.0.0"}, "runtime": {"lib/net7.0/Natasha.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.Natasha.Domain/*******": {"dependencies": {"Microsoft.SourceLink.GitHub": "1.1.0"}, "runtime": {"lib/net7.0/Natasha.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Essensoft.Paylink.Alipay/4.1.9": {"dependencies": {"Essensoft.Paylink.Security": "4.1.9"}, "runtime": {"lib/net8.0/Essensoft.Paylink.Alipay.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Essensoft.Paylink.Security/4.1.9": {"runtime": {"lib/net8.0/Essensoft.Paylink.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Essensoft.Paylink.WeChatPay/4.1.9": {"dependencies": {"Essensoft.Paylink.Security": "4.1.9"}, "runtime": {"lib/net8.0/Essensoft.Paylink.WeChatPay.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Essensoft.Paylinks.Security/5.0.2": {"runtime": {"lib/net8.0/Essensoft.Paylinks.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FreeSpire.Office/8.2.0": {"dependencies": {"System.Drawing.Common": "6.0.0", "System.Security.Cryptography.Xml": "6.0.0", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net6.0/Spire.Barcode.dll": {"assemblyVersion": "********", "fileVersion": "6.12.1.9360"}, "lib/net6.0/Spire.Doc.dll": {"assemblyVersion": "********", "fileVersion": "11.1.5.3360"}, "lib/net6.0/Spire.Pdf.dll": {"assemblyVersion": "9.2.2.0", "fileVersion": "9.2.2.1360"}, "lib/net6.0/Spire.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.6350"}, "lib/net6.0/Spire.XLS.dll": {"assemblyVersion": "********", "fileVersion": "13.1.1.5350"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}}, "IGeekFan.AspNetCore.Knife4jUI/0.0.13": {"runtime": {"lib/net7.0/IGeekFan.AspNetCore.Knife4jUI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "IP2Region.Ex/1.2.0": {"runtime": {"lib/netstandard2.0/IP2Region.Ex.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IPTools.China/1.6.0": {"dependencies": {"IP2Region.Ex": "1.2.0", "IPTools.Core": "1.6.0"}, "runtime": {"lib/net5.0/IPTools.China.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IPTools.Core/1.6.0": {"dependencies": {"IP2Region.Ex": "1.2.0"}, "runtime": {"lib/net5.0/IPTools.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "JavaScriptEngineSwitcher.ChakraCore/3.26.0": {"dependencies": {"JavaScriptEngineSwitcher.Core": "3.24.1"}, "runtime": {"lib/netstandard2.1/JavaScriptEngineSwitcher.ChakraCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netstandard2.1/ru-RU/JavaScriptEngineSwitcher.ChakraCore.resources.dll": {"locale": "ru-RU"}}}, "JavaScriptEngineSwitcher.ChakraCore.Native.win-x64/3.26.0": {"runtimeTargets": {"runtimes/win-x64/native/ChakraCore.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "********"}}}, "JavaScriptEngineSwitcher.Core/3.24.1": {"dependencies": {"AdvancedStringBuilder": "0.1.1"}, "runtime": {"lib/netstandard2.0/JavaScriptEngineSwitcher.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netstandard2.0/ru-RU/JavaScriptEngineSwitcher.Core.resources.dll": {"locale": "ru-RU"}}}, "JavaScriptEngineSwitcher.V8/3.24.2": {"dependencies": {"JavaScriptEngineSwitcher.Core": "3.24.1", "Microsoft.ClearScript.V8": "7.4.5"}, "runtime": {"lib/net5.0/JavaScriptEngineSwitcher.V8.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/net5.0/ru-RU/JavaScriptEngineSwitcher.V8.resources.dll": {"locale": "ru-RU"}}}, "JavaScriptEngineSwitcher.V8.Native.linux-x64/3.24.2": {"dependencies": {"Microsoft.ClearScript.V8.Native.linux-x64": "7.4.5"}}, "JavaScriptEngineSwitcher.V8.Native.win-x64/3.24.2": {"dependencies": {"Microsoft.ClearScript.V8.Native.win-x64": "7.4.5"}}, "log4net/2.0.8": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections.NonGeneric": "4.0.1", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Process": "4.1.0", "System.Diagnostics.StackTrace": "4.0.1", "System.Diagnostics.TraceSource": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Watcher": "4.0.0", "System.Linq": "4.3.0", "System.Net.NameResolution": "4.0.0", "System.Net.Requests": "4.0.11", "System.Net.Sockets": "4.1.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.Timer": "4.0.1", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/log4net.dll": {"assemblyVersion": "2.0.8.0", "fileVersion": "2.0.8.0"}}}, "MailKit/4.7.0": {"dependencies": {"MimeKit": "4.7.0"}, "runtime": {"lib/net8.0/MailKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mapster/7.4.0": {"dependencies": {"Mapster.Core": "1.2.1"}, "runtime": {"lib/net7.0/Mapster.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.0"}}}, "Mapster.Core/1.2.1": {"runtime": {"lib/net7.0/Mapster.Core.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.2.1.0"}}}, "Mapster.DependencyInjection/1.0.1": {"dependencies": {"Mapster": "7.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net7.0/Mapster.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.11": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.AspNetCore.JsonPatch/8.0.11": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.11": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.11", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51604"}}}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.WebEncoders": "2.2.0"}}, "Microsoft.AspNetCore.WebSockets/2.2.1": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Net.WebSockets.WebSocketProtocol": "4.5.3"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Build.Tasks.Git/1.1.0": {}, "Microsoft.ClearScript.Core/7.4.5": {"runtime": {"lib/net5.0/ClearScript.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.ClearScript.V8/7.4.5": {"dependencies": {"Microsoft.ClearScript.Core": "7.4.5", "Microsoft.ClearScript.V8.ICUData": "7.4.5", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net5.0/ClearScript.V8.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.ClearScript.V8.ICUData/7.4.5": {"runtime": {"lib/netstandard1.0/ClearScript.V8.ICUData.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.ClearScript.V8.Native.linux-x64/7.4.5": {"runtimeTargets": {"runtimes/linux-x64/native/ClearScriptV8.linux-x64.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ClearScript.V8.Native.win-x64/7.4.5": {"runtimeTargets": {"runtimes/win-x64/native/ClearScriptV8.win-x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.11.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "********", "fileVersion": "4.1100.24.37604"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.11.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "4.11.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "********", "fileVersion": "4.1100.24.37604"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.11.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.CSharp": "4.11.0", "Microsoft.CodeAnalysis.Common": "4.11.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.11.0", "System.Collections.Immutable": "8.0.0", "System.Composition": "8.0.0", "System.IO.Pipelines": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "********", "fileVersion": "4.1100.24.37604"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.11.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "4.11.0", "System.Collections.Immutable": "8.0.0", "System.Composition": "8.0.0", "System.IO.Pipelines": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "********", "fileVersion": "4.1100.24.37604"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.2.0.0"}}}, "Microsoft.Data.Sqlite/9.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Data.Sqlite.Core/9.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore/8.0.11": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.11", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.11", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52104"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.11": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52104"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.11": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.11": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.11", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52104"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Http.Polly/8.0.0": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "Microsoft.SourceLink.Common/1.1.0": {}, "Microsoft.SourceLink.GitHub/1.1.0": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0", "Microsoft.SourceLink.Common": "1.1.0"}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.19.6": {}, "Microsoft.Win32.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MimeKit/4.7.0": {"dependencies": {"BouncyCastle.Cryptography": "2.4.0", "System.Security.Cryptography.Pkcs": "8.0.0"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Minio/5.0.0": {"dependencies": {"CommunityToolkit.HighPerformance": "8.1.0", "System.IO.Hashing": "7.0.0", "System.Reactive.Linq": "5.0.0"}, "runtime": {"lib/net7.0/Minio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MiniProfiler.AspNetCore/4.5.4": {"dependencies": {"MiniProfiler.Shared": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"dependencies": {"MiniProfiler.AspNetCore": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MiniProfiler.Shared/4.5.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net7.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Npgsql/5.0.18": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NPOI/2.5.5": {"dependencies": {"Portable.BouncyCastle": "1.8.9", "SharpZipLib": "1.3.2", "System.Configuration.ConfigurationManager": "8.0.0", "System.Drawing.Common": "6.0.0"}, "runtime": {"lib/netstandard2.1/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.1/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.1/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.1/NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OnceMi.AspNetCore.OSS/1.2.0": {"dependencies": {"Aliyun.OSS.SDK.NetCore": "2.13.0", "BceSdkDotNetCore": "1.0.2.911", "Microsoft.AspNetCore.StaticFiles": "2.2.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Minio": "5.0.0", "Qiniu": "8.3.1", "Tencent.QCloud.Cos.Sdk": "5.4.34"}, "runtime": {"lib/netstandard2.1/OnceMi.AspNetCore.OSS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Oracle.ManagedDataAccess.Core/3.21.100": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.1"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "3.1.21.1", "fileVersion": "3.1.21.1"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "4.0.4.0", "fileVersion": "4.0.4.0"}}}, "Polly/7.2.4": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.4.982"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.4"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Portable.BouncyCastle/1.8.9": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "1.8.9.0", "fileVersion": "1.8.9.1"}}}, "Qiniu/8.3.1": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Qiniu.dll": {"assemblyVersion": "8.3.1.0", "fileVersion": "8.3.1.0"}}}, "RabbitMQ.Client/6.4.0": {"dependencies": {"System.Memory": "4.5.4", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.4.0.0"}}}, "Roslynator.Analyzers/4.12.4": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "Senparc.CO2NET/2.4.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Http.Polly": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.1/Senparc.CO2NET.dll": {"assemblyVersion": "2.4.1.1", "fileVersion": "2.4.1.1"}}}, "Senparc.CO2NET.APM/1.4.2": {"dependencies": {"Senparc.CO2NET": "2.4.1.1"}, "runtime": {"lib/netstandard2.0/Senparc.CO2NET.APM.dll": {"assemblyVersion": "1.4.2.0", "fileVersion": "1.4.2.0"}}}, "Senparc.NeuChar/2.4.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Senparc.CO2NET": "2.4.1.1", "Senparc.CO2NET.APM": "1.4.2"}, "runtime": {"lib/netstandard2.1/Senparc.NeuChar.dll": {"assemblyVersion": "2.4.1.0", "fileVersion": "2.4.1.0"}}}, "Senparc.NeuChar.App/1.4.1": {"dependencies": {"Senparc.NeuChar": "2.4.1"}, "runtime": {"lib/netstandard2.1/Senparc.NeuChar.App.dll": {"assemblyVersion": "1.4.1.0", "fileVersion": "1.4.1.0"}}}, "Senparc.Weixin/6.18.2": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Newtonsoft.Json": "13.0.3", "Senparc.CO2NET": "2.4.1.1", "Senparc.NeuChar": "2.4.1", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "runtime": {"lib/netstandard2.1/Senparc.Weixin.dll": {"assemblyVersion": "6.18.2.0", "fileVersion": "6.18.2.0"}}}, "Senparc.Weixin.MP/16.21.2": {"dependencies": {"Senparc.CO2NET.APM": "1.4.2", "Senparc.NeuChar": "2.4.1", "Senparc.NeuChar.App": "1.4.1", "Senparc.Weixin": "6.18.2", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.1/Senparc.Weixin.MP.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Senparc.Weixin.Work/3.21.2": {"dependencies": {"Senparc.NeuChar": "2.4.1", "Senparc.Weixin": "6.18.2", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.1/Senparc.Weixin.Work.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpZipLib/1.3.2": {"runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.ImageSharp/3.0.2": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.6": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.6", "SkiaSharp.NativeAssets.macOS": "2.88.6"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.6": {"dependencies": {"SkiaSharp": "2.88.6"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Data.Sqlite": "9.0.0", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "3.21.100", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.8.0", "SqlSugarCore.Kdbndp": "9.3.7.428", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SqlSugarCore.Dm/8.8.0": {"runtime": {"lib/netstandard2.1/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.33449", "fileVersion": "8.3.1.33449"}}}, "SqlSugarCore.Kdbndp/9.3.7.428": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.7.428", "fileVersion": "9.3.7.428"}}}, "StyleCop.Analyzers/1.2.0-beta.406": {"dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.406"}}, "StyleCop.Analyzers.Unstable/1.2.0.406": {}, "Swashbuckle.AspNetCore/8.1.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.1.1", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.1", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.1"}}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.5.0": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.0.12": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Collections.NonGeneric/4.0.1": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Composition/8.0.0": {"dependencies": {"System.Composition.AttributedModel": "8.0.0", "System.Composition.Convention": "8.0.0", "System.Composition.Hosting": "8.0.0", "System.Composition.Runtime": "8.0.0", "System.Composition.TypedParts": "8.0.0"}}, "System.Composition.AttributedModel/8.0.0": {"runtime": {"lib/net8.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.Convention/8.0.0": {"dependencies": {"System.Composition.AttributedModel": "8.0.0"}, "runtime": {"lib/net8.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.Hosting/8.0.0": {"dependencies": {"System.Composition.Runtime": "8.0.0"}, "runtime": {"lib/net8.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.Runtime/8.0.0": {"runtime": {"lib/net8.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.TypedParts/8.0.0": {"dependencies": {"System.Composition.AttributedModel": "8.0.0", "System.Composition.Hosting": "8.0.0", "System.Composition.Runtime": "8.0.0"}, "runtime": {"lib/net8.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Console/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.Process/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.0.1", "Microsoft.Win32.Registry": "4.7.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.0.10", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.StackTrace/4.0.1": {"dependencies": {"System.Collections.Immutable": "8.0.0", "System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "8.0.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.Protocols/6.0.1": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Formats.Asn1/8.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.FileSystem.Watcher/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.0.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.IO.Hashing/7.0.0": {"runtime": {"lib/net7.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Management/6.0.2": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Net.NameResolution/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.7.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Requests/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.WebHeaderCollection": "4.0.1", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Net.Sockets/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.0.11", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Net.WebHeaderCollection/4.0.1": {"dependencies": {"System.Collections": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Net.WebSockets.WebSocketProtocol/4.5.3": {"runtime": {"lib/netcoreapp2.1/System.Net.WebSockets.WebSocketProtocol.dll": {"assemblyVersion": "4.0.0.2", "fileVersion": "4.6.27129.4"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.1"}}}, "System.Reactive.Linq/5.0.0": {"dependencies": {"System.Reactive": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Reactive.Linq.dll": {"assemblyVersion": "3.0.6000.0", "fileVersion": "3.0.6000.0"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.MetadataLoadContext/7.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net7.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.0.1": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Cng/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.0.12", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.OpenSsl/4.0.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Pkcs/8.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.0"}}, "System.Security.Cryptography.Primitives/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.X509Certificates/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Xml/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "8.0.0"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/8.0.5": {}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Overlapped/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.0.10": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "Tea/1.1.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Tea.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "Tencent.QCloud.Cos.Sdk/5.4.34": {"runtime": {"lib/netstandard2.0/COSXML.dll": {"assemblyVersion": "5.4.34.0", "fileVersion": "5.4.34.0"}}}, "TencentCloudSDK.Common/3.0.1045": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "4.7.2"}, "runtime": {"lib/netstandard2.0/TencentCloudCommon.dll": {"assemblyVersion": "3.0.1045.0", "fileVersion": "3.0.1045.0"}}}, "TencentCloudSDK.Sms/3.0.1045": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "4.7.2", "TencentCloudSDK.Common": "3.0.1045"}, "runtime": {"lib/netstandard2.0/TencentCloudSms.dll": {"assemblyVersion": "3.0.1045.0", "fileVersion": "3.0.1045.0"}}}, "UAParser/3.1.47": {"runtime": {"lib/netcoreapp2.0/UAParser.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "BPM.Application/3.6.0": {"dependencies": {"BPM.Common.CodeGen": "5.0.1", "BPM.Domain": "3.6.0", "BPM.Extras.Youzan": "3.6.0", "CsvHelper": "33.0.1", "Essensoft.Paylink.Alipay": "4.1.9", "Essensoft.Paylink.WeChatPay": "4.1.9", "Essensoft.Paylinks.Security": "5.0.2"}, "runtime": {"BPM.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Domain/3.6.0": {"dependencies": {"BPM.Common": "5.0.1"}, "runtime": {"BPM.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BPM.Extras.Youzan/3.6.0": {"dependencies": {"BPM": "5.0.1", "BPM.Common.Core": "5.0.1"}, "runtime": {"BPM.Extras.Youzan.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"BPM.API.Entry/3.6.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdvancedStringBuilder/0.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-32<PERSON>rlspOA59ewotQ4tlKP27QhKxMKGB7oncqlwVHn/hAz+j/r3HvLr6/W9ni9Mnyktlcg6kt7QZS8rEgur1+4Q==", "path": "advancedstringbuilder/0.1.1", "hashPath": "advancedstringbuilder.0.1.1.nupkg.sha512"}, "AlibabaCloud.EndpointUtil/0.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-p/vsdJoVIfc1QREW4JX1tpzKdZZcFdw6/qfrylfcFXc0e2BDMQ2kPrv3nkyr2u+p4BF0PmOYl4EDqRtqLiBc+g==", "path": "alibabacloud.endpointutil/0.1.1", "hashPath": "alibabacloud.endpointutil.0.1.1.nupkg.sha512"}, "AlibabaCloud.GatewaySpi/0.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-EQzRziykoEhA6qkUNcgUrSM+Zs8WbOwyxRBM01Uw0J520nfgL+lMmlZVFnx4IRjwxizTIs5ZHprR7Z8pwcTQzQ==", "path": "alibabacloud.gatewayspi/0.0.2", "hashPath": "alibabacloud.gatewayspi.0.0.2.nupkg.sha512"}, "AlibabaCloud.OpenApiClient/0.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-nh2HFM8u8lA0QevYWZt8xEBEiMg+diY/J2Cb0654eGc50/SJkZ5idMjKpc+6aVaJEJ+YyQQASwjmZeE09CWLpA==", "path": "alibabacloud.openapiclient/0.1.10", "hashPath": "alibabacloud.openapiclient.0.1.10.nupkg.sha512"}, "AlibabaCloud.OpenApiUtil/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-E5s0uhVrAK3Vy6kmLqQRZy5pWJXvsAacXzCoayHyY/1j5Ew7rEb2UOgFkY6LrytZcsrsxN+rCBu1yjPiC0DDCQ==", "path": "alibabacloud.openapiutil/1.1.1", "hashPath": "alibabacloud.openapiutil.1.1.1.nupkg.sha512"}, "AlibabaCloud.SDK.Dysmsapi20170525/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LsIneH6/A6Fbqc7VAWJcga8B76MZ+Djm/KP4g+JUnrE37Um+jWDGUvDQcPEtXTxiaMsIvp1g6jWAK1dO9xvwnQ==", "path": "alibabacloud.sdk.dysmsapi20170525/3.0.0", "hashPath": "alibabacloud.sdk.dysmsapi20170525.3.0.0.nupkg.sha512"}, "AlibabaCloud.TeaUtil/0.1.18": {"type": "package", "serviceable": true, "sha512": "sha512-V6vvBXWbtM09bkMG9dWqdssqvZ2c+AJ6jJhKSkdHFGe0rqG2tPvIjjn4IEzepmb7eeAs1KPTF9vLzlM96BOzZA==", "path": "alibabacloud.teautil/0.1.18", "hashPath": "alibabacloud.teautil.0.1.18.nupkg.sha512"}, "AlibabaCloud.TeaXML/0.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-mHxE6H3eq4jaeqn3hryIYTI0k8quvPZfZdEE+PdU8rw+NGRvev68D8Aei6xjwW/pArZaTG6yPawYu5c0EaZkfw==", "path": "alibabacloud.teaxml/0.0.5", "hashPath": "alibabacloud.teaxml.0.0.5.nupkg.sha512"}, "AlipaySDKNet.Standard/4.6.442": {"type": "package", "serviceable": true, "sha512": "sha512-flBBsqq5b5VSwfAv70CIwTWQTyHQOGLlhUkTpR3s/BJSn8RVXX4Q0BTjfJ/gP47pXZ3Idbl9yZJvF7OqaJ6H0A==", "path": "alipaysdknet.standard/4.6.442", "hashPath": "alipaysdknet.standard.4.6.442.nupkg.sha512"}, "Aliyun.Credentials/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-Y7eev+WuiFzgP2J8stZNU2R+jAfW4wg2uAekfI0Mywp7JxjPX7on/5JdsCqPCLbyR15wsrftCeac/XrW29THdA==", "path": "aliyun.credentials/1.3.2", "hashPath": "aliyun.credentials.1.3.2.nupkg.sha512"}, "Aliyun.OSS.SDK.NetCore/2.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-ElvJwTAdBqFmgb7K4PdxDXPFbOBCIUI5OvCOMfCoUoDL21aivtWMFUtU1v4Dxc2wcrN8XQdY1EKeGFhJK/zVyQ==", "path": "aliyun.oss.sdk.netcore/2.13.0", "hashPath": "aliyun.oss.sdk.netcore.2.13.0.nupkg.sha512"}, "Aspose.Cells/21.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-DMeLtrvxsdzwbUOruirZwa+QJNPQdx/OrPPnlb50+ardZFcJnxuzgTo8+Tws05Wyl39CTibKXhlaQt5NJhvSog==", "path": "aspose.cells/21.8.0", "hashPath": "aspose.cells.21.8.0.nupkg.sha512"}, "Aspose.Words/21.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-aMpfRNHEMtwSeYwDk/sAoNXIIYCV8I3wMNNVPzyKVdFv7alhkUFu9kTm+7gZfuw6HHNrKaF2JfL369n6zr685A==", "path": "aspose.words/21.8.0", "hashPath": "aspose.words.21.8.0.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BceSdkDotNetCore/1.0.2.911": {"type": "package", "serviceable": true, "sha512": "sha512-DNa0svdlYlgQABUo2rTSaXuQ6ZMrA/sqMV8dcuiRxEp0mAOkQyJdawwuxise05GmhrTbyysKfpw4l5rPcBMN0Q==", "path": "bcesdkdotnetcore/1.0.2.911", "hashPath": "bcesdkdotnetcore.1.0.2.911.nupkg.sha512"}, "Ben.Demystifier/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "path": "ben.demystifier/0.4.1", "hashPath": "ben.demystifier.0.4.1.nupkg.sha512"}, "BouncyCastle.Cryptography/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SwXsAV3sMvAU/Nn31pbjhWurYSjJ+/giI/0n6tCrYoupEK34iIHCuk3STAd9fx8yudM85KkLSVdn951vTng/vQ==", "path": "bouncycastle.cryptography/2.4.0", "hashPath": "bouncycastle.cryptography.2.4.0.nupkg.sha512"}, "BPM/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IZZGNwHeWix+lMIkAxhWMB/Z11jI/z3xg8ymgJhEuWV9bslCEUmNYxekRvtLe1R+XyhQp3VJMABlHtjtzgluaA==", "path": "bpm/5.0.1", "hashPath": "bpm.5.0.1.nupkg.sha512"}, "BPM.Apps.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xjOdyN/O4uGKI5pHVVgkzMn+QRu3ClRt5rJ4kTTtW2azI/x9kWCLYdTILtrIN+YOz4nJ6u5TuyxCd7ce9AxINw==", "path": "bpm.apps.entitys/5.0.1", "hashPath": "bpm.apps.entitys.5.0.1.nupkg.sha512"}, "BPM.Common/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SskMzF2iX9ky3Zpqlw7xZFlsoKly7+UGIgx6FqZUoA/Ww4fvOTlq703Yoi9PlUUqXj/gFiuCHzGKG2IL2TWWnQ==", "path": "bpm.common/5.0.1", "hashPath": "bpm.common.5.0.1.nupkg.sha512"}, "BPM.Common.CodeGen/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KXOMazzR2+HPe5KLgFv3DaD8cKF1s2Q8P2fNkkFgniKF0Ckg4X1jnMeZlP2ASnaHo4CRh+49LWrmrNQ/4yMlAg==", "path": "bpm.common.codegen/5.0.1", "hashPath": "bpm.common.codegen.5.0.1.nupkg.sha512"}, "BPM.Common.Core/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-c3jbgGeH8lTnIEq8Yfjb2gokMrkkpi7IfaVT8y45aNQFVVwZGbfNokp5hOBcxhn8rc9nUWBEWpaW1SOyc1fwyA==", "path": "bpm.common.core/5.0.1", "hashPath": "bpm.common.core.5.0.1.nupkg.sha512"}, "BPM.Engine.Entity/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VJYU/wnBf/siwtpchJwjT9C6OxIaX8yFSWTQTuhlzDdWiMH5bQD7RF0WNuHTb6EJznGHlQmK4AjWSadh3McH0w==", "path": "bpm.engine.entity/5.0.1", "hashPath": "bpm.engine.entity.5.0.1.nupkg.sha512"}, "BPM.Extend.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-N5P498q+UmCaQUWElKywsnXdIL+a4SC+SmL5sck6zCZfwNpLso2TIt30IVwuT7l//qYrapCBxEoOOTRWtBF5PA==", "path": "bpm.extend.entitys/5.0.1", "hashPath": "bpm.extend.entitys.5.0.1.nupkg.sha512"}, "BPM.Extend.Interfaces/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dWnsuP4CcNUEy9s1FxWSFxHKm4RcsUWHGEgJ7uoLyFQXbWwJ4dXbm0R5s327jEWLjCjm5IREP8T2esonQDrjlg==", "path": "bpm.extend.interfaces/5.0.1", "hashPath": "bpm.extend.interfaces.5.0.1.nupkg.sha512"}, "BPM.Extras.Authentication.JwtBearer/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Qi2EGsEZjZpy6KztAR/btFbZl4YEyveyuMYUNg0wgfmManoefEwtTey7+8jyEm3UmhlMrFosfA78vzb1Wq5lBA==", "path": "bpm.extras.authentication.jwtbearer/5.0.1", "hashPath": "bpm.extras.authentication.jwtbearer.5.0.1.nupkg.sha512"}, "BPM.Extras.CollectiveOAuth/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-an8A7fDR+4MFV8F2MoUKxX5UZpYigD2NGEt5LW8cfmJp9Wh77o/6RTwwAkgnTRth6wKjEk6khJNznt7ovjz0Vw==", "path": "bpm.extras.collectiveoauth/5.0.1", "hashPath": "bpm.extras.collectiveoauth.5.0.1.nupkg.sha512"}, "BPM.Extras.DatabaseAccessor.SqlSugar/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-8IM7F0WOXVh9jO7jeTzgcUHF61QFeVzBHKUzXrihl4+ZA1pGPlXO+F+3A86l4NEWRSK89BxkE9XXJQ7en78ztg==", "path": "bpm.extras.databaseaccessor.sqlsugar/5.0.1", "hashPath": "bpm.extras.databaseaccessor.sqlsugar.5.0.1.nupkg.sha512"}, "BPM.Extras.DependencyModel.CodeAnalysis/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7no17+MQbpeuBJQFiazsb1GAgSxRRdbaJJLEY37jMAmA1kglJw1egy+QNq6wLJ3xQJiy2e5z0HeJEfht1/BJ5g==", "path": "bpm.extras.dependencymodel.codeanalysis/5.0.1", "hashPath": "bpm.extras.dependencymodel.codeanalysis.5.0.1.nupkg.sha512"}, "BPM.Extras.EventBus.RabbitMQ/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-1KqLySRD/UqUkNcYdZo+J9u0cbg0c0f5lGJ+KoxjpJs18Lg+7LQ4p/JXEwtqVWCwbfLcetnOkno3orc2pOIm1A==", "path": "bpm.extras.eventbus.rabbitmq/5.0.1", "hashPath": "bpm.extras.eventbus.rabbitmq.5.0.1.nupkg.sha512"}, "BPM.Extras.ObjectMapper.Mapster/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-D9XPuN09ZhLUfym7WSDGeH6/WvD3a0LVCh5NhejuTA68RDQ62NwX/5ISX1Ju2rXFEWpRZC372+/c+p/ZjBnDgA==", "path": "bpm.extras.objectmapper.mapster/5.0.1", "hashPath": "bpm.extras.objectmapper.mapster.5.0.1.nupkg.sha512"}, "BPM.Extras.Thirdparty/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+EfoTEuL0Vg+uPmksdKjgp9OGrXPeU9mcsNvaN18S41xmtmM6zrChLJ4kQkVFgQPisUrv1zT7g8GMYqOOfm3pA==", "path": "bpm.extras.thirdparty/5.0.1", "hashPath": "bpm.extras.thirdparty.5.0.1.nupkg.sha512"}, "BPM.Extras.WebSockets/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9vzTKRy4HRXbjuK1cvRHy7YB3D4AHG1E1NjFq+dwouwSdemo0oT4VrY0gi/rmVcSwAMgaQn7W7vM3A6dssQuAg==", "path": "bpm.extras.websockets/5.0.1", "hashPath": "bpm.extras.websockets.5.0.1.nupkg.sha512"}, "BPM.InteAssistant.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nhipNG/4FrmiK+CxOucQlend6IzX+geWUxxJ09kRmTOG7dVO7hrfdEP7yOWtOkLptg63zKHt26+SAe7ZtLWE6g==", "path": "bpm.inteassistant.entitys/5.0.1", "hashPath": "bpm.inteassistant.entitys.5.0.1.nupkg.sha512"}, "BPM.Message/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qXpuzC0vhvFbr1m/lwROUOQGf0CZyimtQ68ntVbQlybiE0Xy0QUCZzmhx7vj1bOUTrAudNyBIUxf5p1fJTkAtQ==", "path": "bpm.message/5.0.1", "hashPath": "bpm.message.5.0.1.nupkg.sha512"}, "BPM.Message.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XdgYddy2YHSO+yGnYAw6SUb8TcugLBQQJg+KgScd/oHZgG5x0gK5xh/UoD8pPMkfc135refs7oXETD+DI/XmUQ==", "path": "bpm.message.entitys/5.0.1", "hashPath": "bpm.message.entitys.5.0.1.nupkg.sha512"}, "BPM.Message.Interfaces/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bQp82yNjecpahZfq0PDcSclGLpOQiB5KZ1lAHRlsDyZ6IjNCq6nx9TCgyuwGujCVEfecUC1T6Hqnp/EjmnKHPQ==", "path": "bpm.message.interfaces/5.0.1", "hashPath": "bpm.message.interfaces.5.0.1.nupkg.sha512"}, "BPM.OAuth/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6c+Dob2SYu8iH1xBMBQuZqzcx+XB0WrN9o/hJ3+8GiiPNZtur9txGg31mv5ovJHFs1o3gezsjCHjVK4uj9q7EQ==", "path": "bpm.oauth/5.0.1", "hashPath": "bpm.oauth.5.0.1.nupkg.sha512"}, "BPM.Systems/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kfAKBWCo2p5cAtlvCAkM261QT0x+RTKCM7mUisQEEjAbgVVv26ddeYKgWUctY9edOSzBeSmnfw9R+Xnuw1EpzA==", "path": "bpm.systems/5.0.1", "hashPath": "bpm.systems.5.0.1.nupkg.sha512"}, "BPM.Systems.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-av3HIuSbqvbbO2ud6wAnTuKiVErj4rBnB9oWMVZkhCenih/rRUrgxvGE5NNv5PDLe0vAL8mdUqhx0AHJ3gZY8A==", "path": "bpm.systems.entitys/5.0.1", "hashPath": "bpm.systems.entitys.5.0.1.nupkg.sha512"}, "BPM.Systems.Interfaces/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-c0s8RBGo668rI9yrhZtxoyz5Rvoi/Ho1SAw9vjC7p54zxOj6D4BsT5YY7t5CSVAh6/ScZWjW513epXbQ2rszIw==", "path": "bpm.systems.interfaces/5.0.1", "hashPath": "bpm.systems.interfaces.5.0.1.nupkg.sha512"}, "BPM.TaskScheduler/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-1VYGyjlXs4oj5oss9kKqtUANOeFU7qkllMcHFKRquptA6q9twIzTgvv7ErKCfJqf2BAymD0EZ/cf9eX7638Qlg==", "path": "bpm.taskscheduler/5.0.1", "hashPath": "bpm.taskscheduler.5.0.1.nupkg.sha512"}, "BPM.TaskScheduler.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-mftXmzN/z4V/Bbz+AMicfj8TPFjTTpc3pXHX8ClXNg9eOl9ITbTKCa9AOb1rs85UuO9HgrUGSgbbjUe73A74wg==", "path": "bpm.taskscheduler.entitys/5.0.1", "hashPath": "bpm.taskscheduler.entitys.5.0.1.nupkg.sha512"}, "BPM.TaskScheduler.Interfaces/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-tmMIXr8wSyrrUIRf3Gd4uB4tspVcdbnAp2/UpbK4OBosCc1Gn4hXGe19v6akuvlNV8A9LPjwIw3Se5M1x8/BTg==", "path": "bpm.taskscheduler.interfaces/5.0.1", "hashPath": "bpm.taskscheduler.interfaces.5.0.1.nupkg.sha512"}, "BPM.VisualDev/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-24Io/95zcN1AGgyah4kuNzFtMIxvKkZP06RN9OoRP/kqpBZlQJveGE4XFyIAaeZeXTYg2R6KgvJeZL6VQbzoJw==", "path": "bpm.visualdev/5.0.1", "hashPath": "bpm.visualdev.5.0.1.nupkg.sha512"}, "BPM.VisualDev.Engine/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fVdSwWS6i0zVghp42epyuqin4EgKr6Et1kFmgIHGMkHUBJjQQ1gZ3j6H/MmndCa+owAUxzNdLNuZJxj+NpR/3Q==", "path": "bpm.visualdev.engine/5.0.1", "hashPath": "bpm.visualdev.engine.5.0.1.nupkg.sha512"}, "BPM.VisualDev.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-NUt/ra+rV//eUUJVzUUMr3LbCrlpugYNNyFu72bKEQMLzClszSr4E+/dDpdXvlo54VaWlL3Q4nFU2q23nfxESw==", "path": "bpm.visualdev.entitys/5.0.1", "hashPath": "bpm.visualdev.entitys.5.0.1.nupkg.sha512"}, "BPM.VisualDev.Interfaces/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-W/cyL39cQ3u1wvLIQvTg6aSZmTHF+No2enn3o/MhJjpOKNXpXfo2YGRHGH3zxB25OBFX93ItdjW1Ea5hQIyUTg==", "path": "bpm.visualdev.interfaces/5.0.1", "hashPath": "bpm.visualdev.interfaces.5.0.1.nupkg.sha512"}, "BPM.WorkFlow/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LfMMc0uNwmVOc6dEORqtsn+/Zb4cnN2xGvXkk/7mdl5zmzLVuIAq6LWww7NvKSGzd7nF4M2y4//QnRAjouv6Nw==", "path": "bpm.workflow/5.0.1", "hashPath": "bpm.workflow.5.0.1.nupkg.sha512"}, "BPM.WorkFlow.Entitys/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UGq21j75s/22mcWukLvL7Vvd4WIlyQ7QMmygntZ8P4IEfMsIRPBuQpB9aKgAOc+Mngy296reVAby1ehB9Ny/2w==", "path": "bpm.workflow.entitys/5.0.1", "hashPath": "bpm.workflow.entitys.5.0.1.nupkg.sha512"}, "BPM.WorkFlow.Interfaces/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-b4cmjfbNSbBf8szcc6k4ORFqMgNhf87LvEXZyGql9lZipVxVvMd8Si28anW+kzPmAXOc00i/NQN6BmPRY6r+nA==", "path": "bpm.workflow.interfaces/5.0.1", "hashPath": "bpm.workflow.interfaces.5.0.1.nupkg.sha512"}, "CommunityToolkit.HighPerformance/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kgDi65k02hrgnHy3N0xENecsr0spW13RdIA1tipovi9t16gKziI7uZIu3qkxz0GctCHNM4hfeqXYg//6wHJ6Kw==", "path": "communitytoolkit.highperformance/8.1.0", "hashPath": "communitytoolkit.highperformance.8.1.0.nupkg.sha512"}, "CSRedisCore/3.8.670": {"type": "package", "serviceable": true, "sha512": "sha512-NH3H7IS5R/O4eHDijMpF1GlzcQY+vxVMZ6XonucW4yf7fT87p0L53+G3qUxRe5e0euD5TGzPefMyakv6wQMpAQ==", "path": "csrediscore/3.8.670", "hashPath": "csrediscore.3.8.670.nupkg.sha512"}, "CsvHelper/33.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fev4lynklAU2A9GVMLtwarkwaanjSYB4wUqO2nOJX5hnzObORzUqVLe+bDYCUyIIRQM4o5Bsq3CcyJR89iMmEQ==", "path": "csvhelper/33.0.1", "hashPath": "csvhelper.33.0.1.nupkg.sha512"}, "DingDing.SDK.NetCore/2021.1.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-WCDN5eox8weTgU3WdGVNohFC1emnV9r8gPmzReMuQujR7Sk/2sgjOhbEVbym1uL4DbLumwHcjx6OPa24M9wIhA==", "path": "dingding.sdk.netcore/2021.1.7.1", "hashPath": "dingding.sdk.netcore.2021.1.7.1.nupkg.sha512"}, "DotNetCore.Natasha.CSharp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-NLZJjeMKm51/63tLRyUVd3LhnlMM0q+5dQz7acEOecclmS2rOYvJr8e4UYC4OD3/qdFMzh3OBhz1cuPQW7coTA==", "path": "dotnetcore.natasha.csharp/*******", "hashPath": "dotnetcore.natasha.csharp.*******.nupkg.sha512"}, "DotNetCore.Natasha.Domain/*******": {"type": "package", "serviceable": true, "sha512": "sha512-d/85GIB+O524E1mNwlPXgathkscFOdnfUiutM+TTMnyBiVD3aBNrtM5ffHVp5di/zkOEJb8T38ig4jutzZwiLA==", "path": "dotnetcore.natasha.domain/*******", "hashPath": "dotnetcore.natasha.domain.*******.nupkg.sha512"}, "Essensoft.Paylink.Alipay/4.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-c76c1TSKs5gXlT87HJvkOTouxogLenyl4H1NaJ3v/52fwmazBKuAJzoRFTF3S/NSSzbBAckLeozkUHbhI9+C4g==", "path": "essensoft.paylink.alipay/4.1.9", "hashPath": "essensoft.paylink.alipay.4.1.9.nupkg.sha512"}, "Essensoft.Paylink.Security/4.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-dAaJ+zpaZhYQvpGVxwrP0upCFfqbs/Mo6ljn0KmWtpbAgxAxvxSwROplSn/21denLUV4zK7FASNvRb9HjhkMpQ==", "path": "essensoft.paylink.security/4.1.9", "hashPath": "essensoft.paylink.security.4.1.9.nupkg.sha512"}, "Essensoft.Paylink.WeChatPay/4.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-My8GStY/vgxG/tdVc8YhSdpaNbawyASpJJUHtA4j03EsUlwTCaZWKfbPcC8lP59GkMps0zMgwj6dGFDQPHQ5Nw==", "path": "essensoft.paylink.wechatpay/4.1.9", "hashPath": "essensoft.paylink.wechatpay.4.1.9.nupkg.sha512"}, "Essensoft.Paylinks.Security/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6yjYo4HNY0qTslMpegBSK/kmAeM9bJqlRUTpgTPRSwTCfWmmRhEs12kzA5W7sfFqqOPrnMuliyRpgTm9bSKbrQ==", "path": "essensoft.paylinks.security/5.0.2", "hashPath": "essensoft.paylinks.security.5.0.2.nupkg.sha512"}, "FreeSpire.Office/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ldbV/wZrdyWbpxIQ5EoVwk9cQkUbJwx3vQVqxtncAmjG0SQtYKOVbbQD1c++T0hMy+PPdqzhzfRzm8LpDKXb2g==", "path": "freespire.office/8.2.0", "hashPath": "freespire.office.8.2.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "IGeekFan.AspNetCore.Knife4jUI/0.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-E5OoD0RreHvkTlTxYeNOzlKZOf5PDAHTwMGfYRKrrGkD4m4yc5qK69fs1/zZJXUA14vZWbbXfDuOIe3tH11HSQ==", "path": "igeekfan.aspnetcore.knife4jui/0.0.13", "hashPath": "igeekfan.aspnetcore.knife4jui.0.0.13.nupkg.sha512"}, "IP2Region.Ex/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-B8TxhuAw72cPwjgf8pqDf62l1TJL0jXw4J13fXHg4Igq1OwT7SRotQX6gN6puyIgHVYLtKxnmhFf60oUITxmxA==", "path": "ip2region.ex/1.2.0", "hashPath": "ip2region.ex.1.2.0.nupkg.sha512"}, "IPTools.China/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-12VnC92ffiKlLRwr5Ay3uFvZMCB9SDNn77sVlNycQu1OJAunnuCNBOVZTkg9D2UL2cc+iMwra6if9viXhrrt7w==", "path": "iptools.china/1.6.0", "hashPath": "iptools.china.1.6.0.nupkg.sha512"}, "IPTools.Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-qO+EY5vEwLKtOkQD1aMweM9pIVSFLwTi/Z2ZnX08qBXI4yPdRuguJJvzT2YVk2Addv999A+bWifIS8ahiwJcTg==", "path": "iptools.core/1.6.0", "hashPath": "iptools.core.1.6.0.nupkg.sha512"}, "JavaScriptEngineSwitcher.ChakraCore/3.26.0": {"type": "package", "serviceable": true, "sha512": "sha512-uKGlMJn73uw+mX6q1fJvuuqg6Bb7hz/2RK1IkD656iD5SSlH6wFUfnor/1mVBY76hbUPn8NFPb8STSmiLKfdmg==", "path": "javascriptengineswitcher.chakracore/3.26.0", "hashPath": "javascriptengineswitcher.chakracore.3.26.0.nupkg.sha512"}, "JavaScriptEngineSwitcher.ChakraCore.Native.win-x64/3.26.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lpv8M74NyDe3Ots06xfDtflghE4AWX1MtfAeotXqfzxwLGlAnlnIDX30alQ1hkf6z5KyfJguvD1vkd2GxmRXLA==", "path": "javascriptengineswitcher.chakracore.native.win-x64/3.26.0", "hashPath": "javascriptengineswitcher.chakracore.native.win-x64.3.26.0.nupkg.sha512"}, "JavaScriptEngineSwitcher.Core/3.24.1": {"type": "package", "serviceable": true, "sha512": "sha512-9r2ZMni/Tll48IUP1ckCpAYZNllkaiLg62/wfazh8nVRI2C2DNvldCBLGG2P77soDvmLDaE3tO7lpGhZP9YBDw==", "path": "javascriptengineswitcher.core/3.24.1", "hashPath": "javascriptengineswitcher.core.3.24.1.nupkg.sha512"}, "JavaScriptEngineSwitcher.V8/3.24.2": {"type": "package", "serviceable": true, "sha512": "sha512-A23FkzvK2ieBE4mSKFAOQWOnV9OwzjPJh5dy2Cfe2BZGjmsLgK9gVhaDQv9XBNduEeVhQS9Enz00/T2P+F+4ag==", "path": "javascriptengineswitcher.v8/3.24.2", "hashPath": "javascriptengineswitcher.v8.3.24.2.nupkg.sha512"}, "JavaScriptEngineSwitcher.V8.Native.linux-x64/3.24.2": {"type": "package", "serviceable": true, "sha512": "sha512-rrJ5uMI+WZaxD3N6PBltJ83y/fiycIJZPZcSevZNDbxVVcFbJM2NpL1/5x4DOOAVk57pUqfEl+2OM8sTSVVnyg==", "path": "javascriptengineswitcher.v8.native.linux-x64/3.24.2", "hashPath": "javascriptengineswitcher.v8.native.linux-x64.3.24.2.nupkg.sha512"}, "JavaScriptEngineSwitcher.V8.Native.win-x64/3.24.2": {"type": "package", "serviceable": true, "sha512": "sha512-UudHgGpw4qYv63r/oGZ6XYibHUKbeanONQl4Ec72oEj0+ua1hZdmfbyQENRUMB/8yaPx3eQabRgUpfc1FTuLJA==", "path": "javascriptengineswitcher.v8.native.win-x64/3.24.2", "hashPath": "javascriptengineswitcher.v8.native.win-x64.3.24.2.nupkg.sha512"}, "log4net/2.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-N41MQGHZImiCfn0cUuSBjZxrcNfIQCuCgQP0rpgB3J/NWponEh3lc1LxJEuIsPAR9Oc1jVvfkNNFCY1C5hf9LA==", "path": "log4net/2.0.8", "hashPath": "log4net.2.0.8.nupkg.sha512"}, "MailKit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NHTD7QXeDwhgwl+l7jM/Ys8qs43r1J6zQ0PvuSBVTj3P2omWibkt+TZquXWmQeqEyAaYtVGOHUhTEhlaT63QDA==", "path": "mailkit/4.7.0", "hashPath": "mailkit.4.7.0.nupkg.sha512"}, "Mapster/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-RYGoDqvS4WTKIq0HDyPBBVIj6N0mluOCXQ1Vk95JKseMHEsbCXSEGKSlP95oL+s42IXAXbqvHj7p0YaRBUcfqg==", "path": "mapster/7.4.0", "hashPath": "mapster.7.4.0.nupkg.sha512"}, "Mapster.Core/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-11lokmfliBEMMmjeqxFGNpqGXq6tN96zFqpBmfYeahr4Ybk63oDmeJmOflsATjobYkX248g5Y62oQ2NNnZaeww==", "path": "mapster.core/1.2.1", "hashPath": "mapster.core.1.2.1.nupkg.sha512"}, "Mapster.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LfjnRIwx6WAo3ssq8PFeaHFaUz00BfSG9BhWgXsiDa3H5lDhG0lpMGDF6w2ZnooS4eHYmAv4f77VxmzpvgorNg==", "path": "mapster.dependencyinjection/1.0.1", "hashPath": "mapster.dependencyinjection.1.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-9KhRuywosM24BPf1R5erwsvIkpRUu1+btVyOPlM3JgrhFVP4pq5Fuzi3vjP01OHXfbCtNhWa+HGkZeqaWdcO5w==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.11", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-l1tFnQm2LtFE3M9YRM/bdwtxxCV50Y5jnN0LjliQH1sqvWsN46++Uu3QCJL9IdOweFvXSf3Shi7DI/Vc1jkdKA==", "path": "microsoft.aspnetcore.jsonpatch/8.0.11", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-XcfFd8e0g2M0mcAKVNgoHJtWYJfKrPntHhgqiZ1Ci37i3AEJbM0GHIa715i0UPSksiKmDxsJWXnM3rg8keF/Zg==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.11", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-n5Mg5D0aRrhHJJ6bJcwKqQydIFcgUq0jTlvuynoJjwA2IvAzh8Aqf9cpYagofQbIlIXILkCP6q6FgbngyVtpYA==", "path": "microsoft.aspnetcore.razor.language/6.0.36", "hashPath": "microsoft.aspnetcore.razor.language.6.0.36.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-byZDrjir6Co5EoWbraQyG0qbPCUG6XgGYQstipMF9lucOAjq/mqnIyt8B8iMWnin/ghZoOln9Y01af4rUAwOhA==", "path": "microsoft.aspnetcore.staticfiles/2.2.0", "hashPath": "microsoft.aspnetcore.staticfiles.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebSockets/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ilk4fQ0xdVpJk1a+72thHv2LglUZPWL+vECOG3mw+gOesNx0/p56HNJXZw8k1pj8ff1cVHn8KtfvyRZxdplNQA==", "path": "microsoft.aspnetcore.websockets/2.2.1", "hashPath": "microsoft.aspnetcore.websockets.2.2.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Build.Tasks.Git/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-DD84CsvKf+hfDBbYXIvBXeY7JGfbtoIMMjlPGVaKIp6aWoIdL5EJbWvk+skGkM7/Weezj3q1a89nTPAcGlGcpw==", "path": "microsoft.build.tasks.git/1.1.0", "hashPath": "microsoft.build.tasks.git.1.1.0.nupkg.sha512"}, "Microsoft.ClearScript.Core/7.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-CIZCty2L4mXy1xg77IbNPqsz2pS0ZsFVJZ5YolPB4j62xzvDuCFU/MKnTnKve67FGIWaB9w4LVV7YUEFmjEwzw==", "path": "microsoft.clearscript.core/7.4.5", "hashPath": "microsoft.clearscript.core.7.4.5.nupkg.sha512"}, "Microsoft.ClearScript.V8/7.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-/czu8ho8xEiiLF806QP6Iwgp0aN5QE1B/MsXnIIMxiaM/KccD8AfSsM68lVuyW+boGqjF7zjoSlUKs/ERUoLOQ==", "path": "microsoft.clearscript.v8/7.4.5", "hashPath": "microsoft.clearscript.v8.7.4.5.nupkg.sha512"}, "Microsoft.ClearScript.V8.ICUData/7.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ji6av4T2xtqZYp5Gm0MPxvez5V6s4NF/wflD8a49A/KOI3kJyPHcmL3xCRuhuHo24NbfQX6+f0YbTdwhd5VSvw==", "path": "microsoft.clearscript.v8.icudata/7.4.5", "hashPath": "microsoft.clearscript.v8.icudata.7.4.5.nupkg.sha512"}, "Microsoft.ClearScript.V8.Native.linux-x64/7.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-upzvb1bN1DCXE3fK+7Vt/pfZ/ejYI7X/t2/JOYf0p4HjvCexDkebeIBJ96alPPBMqDwqRCZy0N80/wYFwwDKgA==", "path": "microsoft.clearscript.v8.native.linux-x64/7.4.5", "hashPath": "microsoft.clearscript.v8.native.linux-x64.7.4.5.nupkg.sha512"}, "Microsoft.ClearScript.V8.Native.win-x64/7.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-LvsaJoVgcf09FJ6mZ6SIptdeuwmy42s1N2sMVO3Wqp+HAta7PPksxVh8cGelrGQmmW1C5hT1Jzc6ykxrzU4Adg==", "path": "microsoft.clearscript.v8.native.win-x64/7.4.5", "hashPath": "microsoft.clearscript.v8.native.win-x64.7.4.5.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-djf8ujmqYImFgB04UGtcsEhHrzVqzHowS+EEl/Yunc5LdrYrZhGBWUTXoCF0NzYXJxtfuD+UVQarWpvrNc94Qg==", "path": "microsoft.codeanalysis.common/4.11.0", "hashPath": "microsoft.codeanalysis.common.4.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-6XYi2EusI8JT4y2l/F3VVVS+ISoIX9nqHsZRaG6W5aFeJ5BEuBosHfT/ABb73FN0RZ1Z3cj2j7cL28SToJPXOw==", "path": "microsoft.codeanalysis.csharp/4.11.0", "hashPath": "microsoft.codeanalysis.csharp.4.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-/oRJPIMvzOfiunIegSF6FEa4VvBAUSXlbLDKxyzXuOZN9nLHg3fHuX6Mr9JZLNIupbe2xqQZEmfsPxgB01vCmg==", "path": "microsoft.codeanalysis.csharp.workspaces/4.11.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-UtwEt42V7/LnvAcschSlmUXRLEj0poX1H7QeFbY5bezcS+tJBCssAq8C7GfisgIA0qZPo2xzOrwKGBbg51CetA==", "path": "microsoft.codeanalysis.workspaces.common/4.11.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.11.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Data.Sqlite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lw6wthgXGx3r/U775k1UkUAWIn0kAT0wj4ZRq0WlhPx4WAOiBsIjgDKgWkXcNTGT0KfHiClkM+tyPVFDvxeObw==", "path": "microsoft.data.sqlite/9.0.0", "hashPath": "microsoft.data.sqlite.9.0.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cFfZjFL+tqzGYw9lB31EkV1IWF5xRQNk2k+MQd+Cf86Gl6zTeAoiZIFw5sRB1Z8OxpEC7nu+nTDsLSjieBAPTw==", "path": "microsoft.data.sqlite.core/9.0.0", "hashPath": "microsoft.data.sqlite.core.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-stbjWBTtpQ1HtqXMFyKnXFTr76PvaOHI2b2h85JqBi3eZr00nspvR/a90Zwh8CQ4rVawqLiTG0+0yZQWaav+sQ==", "path": "microsoft.entityframeworkcore/8.0.11", "hashPath": "microsoft.entityframeworkcore.8.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-++zY0Ea724ku1jptWJmF7jm3I4IXTexfT4qi1ETcSFFF7qj+qm6rRgN7mTuKkwIETuXk0ikfzudryRjUGrrNKQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.11", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-NI/AJQjtC7qgWM8Nr85sRkwlog2AnFer5RKP8xTUH0RuPF3nN0tGXBEeYJOLZWp+/+M/C6O7MMDRhKRE8bZwIA==", "path": "microsoft.entityframeworkcore.analyzers/8.0.11", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.11.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-3TuuW3i5I4Ro0yoaHmi2MqEDGObOVuhLaMEnd/heaLB1fcvm4fu4PevmC4BOWnI0vo176AIlV5o4rEQciLoohw==", "path": "microsoft.entityframeworkcore.relational/8.0.11", "hashPath": "microsoft.entityframeworkcore.relational.8.0.11.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "path": "microsoft.extensions.dependencymodel/8.0.2", "hashPath": "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UbZJib/wukyGVTvX7ZLS988su5XLrDoHDBSXp00Jxre0ONB1XW7e1zTk7vQbJq1PzmD5x7CBqdZQlH2OWte+Uw==", "path": "microsoft.extensions.http.polly/8.0.0", "hashPath": "microsoft.extensions.http.polly.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.SourceLink.Common/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TyGnSaCanfxunClp2af9jpXY127q7g5tiOv0XN/JGcalyKwYusYp06BUGSmCopg/GhmJJSiR/9PS0suXHCGxtw==", "path": "microsoft.sourcelink.common/1.1.0", "hashPath": "microsoft.sourcelink.common.1.1.0.nupkg.sha512"}, "Microsoft.SourceLink.GitHub/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-DD/LF81k5ODHCfPnBCds97N6KKSS1X1NiDcJYUUpiTu5mmuPktZplhYFrAPQPktMBKvNMdwFrwJ7Fqa3tyTIuA==", "path": "microsoft.sourcelink.github/1.1.0", "hashPath": "microsoft.sourcelink.github.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.19.6": {"type": "package", "serviceable": true, "sha512": "sha512-7GOQdMzQcH7o/bPFL/I2kQEgMnq2pYZ+exhGb9nNqs624K9w2jB2zieh4sOH9+a01i/G9iTWfeUI3IGMF7SKNg==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.19.6", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.19.6.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "path": "microsoft.win32.primitives/4.0.1", "hashPath": "microsoft.win32.primitives.4.0.1.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MimeKit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VDqnKACvUiNo5eQshOqB1XzmQROkCSJRkmPu6zj/JqKIn3PID2TtnWGHN5YjkhlLIB7x8MwkXYG6iPLDfiYdcQ==", "path": "mimekit/4.7.0", "hashPath": "mimekit.4.7.0.nupkg.sha512"}, "Minio/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7tZj90WEuuH60RAP4wBYexjMuJOhCnK7I46hCiX3CtZPackHisLZ8aAJmn3KlwbUX22dBDphwemD+h37vet8Qw==", "path": "minio/5.0.0", "hashPath": "minio.5.0.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-meedJsjpYOeHPhE8H6t+dGQ9zLxcCQVpi4DXzmxmYAXywmTzlo6jv2IASUv5QijTU0CxsROln3FHd8RsTO8Z8A==", "path": "miniprofiler.aspnetcore/4.5.4", "hashPath": "miniprofiler.aspnetcore.4.5.4.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-+NqXyCy9aNdroPm6leW5+cpngtCnkCdoyOlJzvVN62uucSx+MYkx8jmKbgAt+aCP6aghADfHBExwrTIldHxapg==", "path": "miniprofiler.aspnetcore.mvc/4.5.4", "hashPath": "miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512"}, "MiniProfiler.Shared/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-f8ckFm/xTS8C2Bn4BdVc94dNvg+tRfk0e4XFaETOqRi6r0PUOyn3Z9jTQCVpB3R1pP5WiRsEIrqqxux95BVpTA==", "path": "miniprofiler.shared/4.5.4", "hashPath": "miniprofiler.shared.4.5.4.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Npgsql/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1u4iCPKL9wtPeSUzChIbgq/BlOhvf44o7xASacdpMY7z0PbqACKpNOF0fjEn9jDV/AJl/HtPY6zk5qasQ1URhw==", "path": "npgsql/5.0.18", "hashPath": "npgsql.5.0.18.nupkg.sha512"}, "NPOI/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-xTCJOXdcBWV9GUAVRJTC73L5bU3Ahgtuk13U/rOix8Gi05XWf9E4K+vGx63JZt35WoroXq+hM0RZ+aad2y8Ceg==", "path": "npoi/2.5.5", "hashPath": "npoi.2.5.5.nupkg.sha512"}, "OnceMi.AspNetCore.OSS/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESndB5bQ37n+iTMCCL/HGll0+WhXuHeErfMaUbhnGM/ib0koFMY91cUe1wwLWLTi8EKucQgdAzc/im4+QS5zew==", "path": "oncemi.aspnetcore.oss/1.2.0", "hashPath": "oncemi.aspnetcore.oss.1.2.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.100": {"type": "package", "serviceable": true, "sha512": "sha512-nsqyUE+v246WB0SOnR1u9lfZxYoNcdj1fRjTt7TOhCN0JurEc6+qu+mMe+dl1sySB2UpyWdfqHG1iSQJYaXEfA==", "path": "oracle.manageddataaccess.core/3.21.100", "hashPath": "oracle.manageddataaccess.core.3.21.100.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Polly/7.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-bw00Ck5sh6ekduDE3mnCo1ohzuad946uslCDEENu3091+6UKnBuKLo4e+yaNcCzXxOZCXWY2gV4a35+K1d4LDA==", "path": "polly/7.2.4", "hashPath": "polly.7.2.4.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Portable.BouncyCastle/1.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-wlJo8aFoeyl+W93iFXTK5ShzDYk5WBqoUPjTNEM0Xv9kn1H+4hmuCjF0/n8HLm9Nnp1aY6KNndWqQTNk+NGgRQ==", "path": "portable.bouncycastle/1.8.9", "hashPath": "portable.bouncycastle.1.8.9.nupkg.sha512"}, "Qiniu/8.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-SdmxJ/W/32zWB5/tkUq9Qcn/XA2FW2wdOsBqbJBQ8tN1lWyb/4bD+DdWKy9WGzVrVmxtCxmzWeiJ14p1//ufcA==", "path": "qiniu/8.3.1", "hashPath": "qiniu.8.3.1.nupkg.sha512"}, "RabbitMQ.Client/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1znR1gGU+xYVSpO5z8nQolcUKA/yydnxQn7Ug9+RUXxTSLMm/eE58VKGwahPBjELXvDnX0k/kBrAitFLRjx9LA==", "path": "rabbitmq.client/6.4.0", "hashPath": "rabbitmq.client.6.4.0.nupkg.sha512"}, "Roslynator.Analyzers/4.12.4": {"type": "package", "serviceable": true, "sha512": "sha512-isl8hAh7yFNjyBEC4YlTSi+xGBblqBUC/2MCMmnBPwuXPewb7XYnMRzT3vXbP/gOGwT8hZUOy1g/aRH3lAF/NQ==", "path": "roslynator.analyzers/4.12.4", "hashPath": "roslynator.analyzers.4.12.4.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA==", "path": "runtime.native.system.net.http/4.0.1", "hashPath": "runtime.native.system.net.http.4.0.1.nupkg.sha512"}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw==", "path": "runtime.native.system.security.cryptography/4.0.0", "hashPath": "runtime.native.system.security.cryptography.4.0.0.nupkg.sha512"}, "Senparc.CO2NET/2.4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-EnbjQ4lJkTsC7JPnrhqv7ykrjcKjdRir0rNJO4I71jLRYfsvc/Za08nBkEuAGd0oJkP5V+yhKXdZE0Wi1jaXxQ==", "path": "senparc.co2net/2.4.1.1", "hashPath": "senparc.co2net.2.4.1.1.nupkg.sha512"}, "Senparc.CO2NET.APM/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-n+0XWjzkoVBDKvli4DOISvPuxZHnMSPG0sSnvrns5RD19P0QT1ykyBLHRQsWW1evdhYy/8+eW9lFtYITDlgQfQ==", "path": "senparc.co2net.apm/1.4.2", "hashPath": "senparc.co2net.apm.1.4.2.nupkg.sha512"}, "Senparc.NeuChar/2.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-9ZXK9dDoUWDdpln/oj4W//ancvwvPloU5RqV9+Cd8e47Z2soRnid6aW3SY5oupgqyPbbOGNVjpVcoGXqkAlBdA==", "path": "senparc.neuchar/2.4.1", "hashPath": "senparc.neuchar.2.4.1.nupkg.sha512"}, "Senparc.NeuChar.App/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-yGHDDn55wXW9Mzis9TIfzBYMA6hyx+iXssT+DUN8yjxck+H/CWAUIck9UOjJJva1IVkTG/O1MTC/IN5nMWkGXA==", "path": "senparc.neuchar.app/1.4.1", "hashPath": "senparc.neuchar.app.1.4.1.nupkg.sha512"}, "Senparc.Weixin/6.18.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ichsr7H3MsZcyM5RK4W5EOLuiZdq2oOmaUAFpCAjYCYqpacO/ziOd280ElDrrgfR57T8oFudVtJ9RouhWnkZvQ==", "path": "senparc.weixin/6.18.2", "hashPath": "senparc.weixin.6.18.2.nupkg.sha512"}, "Senparc.Weixin.MP/16.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-fkM+4GIIqK7j6ljY3dkzA9fqfvD6s9uc7huKz3T3IyAHtbI+RR9ZwhZskBx2ajHNrPaFhEwhrzzdQS0yBHFGbw==", "path": "senparc.weixin.mp/16.21.2", "hashPath": "senparc.weixin.mp.16.21.2.nupkg.sha512"}, "Senparc.Weixin.Work/3.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-IQvEXOnDDgfF6bkM5WIVvnfTYOzB7jOwdQc/YXjsY5vcE6NKQFz+XW0RP2onnQs8Ip1rhn/jbU9JzOOG5jNFSw==", "path": "senparc.weixin.work/3.21.2", "hashPath": "senparc.weixin.work.3.21.2.nupkg.sha512"}, "SharpZipLib/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-WSdeDReL8eugMCw5BH/tFAZpgR+YsYMwm6kIvqg3J8LbfRjbbebmEzn63AbEveqyMOljBO68g6tCCv165wMkSg==", "path": "sharpziplib/1.3.2", "hashPath": "sharpziplib.1.3.2.nupkg.sha512"}, "SixLabors.ImageSharp/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PlwUHnLZ67G1i0xXDYQJYbFiJsvXiEFWtKdV0QvY9j+dFasYbl+vWXXY42/asTsNjchsH5BLuoRzlf8Fa8lzpg==", "path": "sixlabors.imagesharp/3.0.2", "hashPath": "sixlabors.imagesharp.3.0.2.nupkg.sha512"}, "SkiaSharp/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-wdfeBAQrEQCbJIRgAiargzP1Uy+0grZiG4CSgBnhAgcJTsPzlifIaO73JRdwIlT3TyBoeU9jEqzwFUhl4hTYnQ==", "path": "skiasharp/2.88.6", "hashPath": "skiasharp.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-ecgMclPor+X1wi2dZSVDo1sV2Dm8gwEKNRtS+qiE9qfnQzGHbYWlbTBWalnZBaIl3BLC21b1QO8gMgabhSAh+g==", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.6", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-Sko9LFxRXSjb3OGh5/RxrVRXxYo48tr5NKuuSy6jB85GrYt8WRqVY1iLOLwtjPiVAt4cp+pyD4i30azanS64dw==", "path": "skiasharp.nativeassets.macos/2.88.6", "hashPath": "skiasharp.nativeassets.macos.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-7TzFO0u/g2MpQsTty4fyCDdMcfcWI+aLswwfnYXr3gtNS6VLKdMXPMeKpJa3pJSLnUBN6wD0JjuCe8OoLBQ6cQ==", "path": "skiasharp.nativeassets.win32/2.88.6", "hashPath": "skiasharp.nativeassets.win32.2.88.6.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-h4wIN8i38Crj9jxjmhBzncMyLGHbWPR/NTuQAT4cgiODR7MzHB1wiObyUOtK1QGMhNG1W01GsB2QO6Z6fkOU7Q==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/8.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1Wh/JdK/UgaJe9m76/EKTrV0d18sBDvsgh7FCmRB8U0oJYA0pkFJWLchqi9CMYv1RJjc3f4tB0tFzKjlRV5rQ==", "path": "sqlsugarcore.dm/8.8.0", "hashPath": "sqlsugarcore.dm.8.8.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.7.428": {"type": "package", "serviceable": true, "sha512": "sha512-PPir32f/dH83D5AUvcdwFjbE0/8aEcceXY/aJuhAnVJEe2ibQi2gTpPqsqYdTsqXHoLl7lrml2tVrnZllYa0gQ==", "path": "sqlsugarcore.kdbndp/9.3.7.428", "hashPath": "sqlsugarcore.kdbndp.9.3.7.428.nupkg.sha512"}, "StyleCop.Analyzers/1.2.0-beta.406": {"type": "package", "serviceable": true, "sha512": "sha512-YbsYoczQPZyz+4nmQ7bBiU9uQkk7Q2KUizQWEv01S4/ImCdJFiHvJfm8HAINNS0cvSLOA7xM9Y+KWQ2FOYjgkA==", "path": "stylecop.analyzers/1.2.0-beta.406", "hashPath": "stylecop.analyzers.1.2.0-beta.406.nupkg.sha512"}, "StyleCop.Analyzers.Unstable/1.2.0.406": {"type": "package", "serviceable": true, "sha512": "sha512-FclNdBR81ynIo9l1QNlo+l0I/PaFIYaPQPlMram8XVIMh6G6G43KTa1aCxfwTj13uKlAJS/LhLy6KjOPOeAU4w==", "path": "stylecop.analyzers.unstable/1.2.0.406", "hashPath": "stylecop.analyzers.unstable.1.2.0.406.nupkg.sha512"}, "Swashbuckle.AspNetCore/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HJHexmU0PiYevgTLvKjYkxEtclF2w4O7iTd3Ef3p6KeT0kcYLpkFVgCw6glpGS57h8769anv8G+NFi9Kge+/yw==", "path": "swashbuckle.aspnetcore/8.1.1", "hashPath": "swashbuckle.aspnetcore.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+8D5jQtnl6X4f2hJQwf0Khj0SnCQANzirCELjXJ6quJ4C1aNNCvJrAsQ+4fOKAMqJkvW48cKj79ftG+YoGcRg==", "path": "swashbuckle.aspnetcore.swagger/8.1.1", "hashPath": "swashbuckle.aspnetcore.swagger.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2EuPzXSNleOOzYvziERWRLnk1Oz9i0Z1PimaUFy1SasBqeV/rG+eMfwFAMtTaf4W6gvVOzRcUCNRHvpBIIzr+A==", "path": "swashbuckle.aspnetcore.swaggergen/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-GDLX/MpK4oa2nYC1N/zN2UidQTtVKLPF6gkdEmGb0RITEwpJG9Gu8olKqPYnKqVeFn44JZoCS0M2LGRKXP8B/A==", "path": "swashbuckle.aspnetcore.swaggerui/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.8.1.1.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ==", "path": "system.collections.concurrent/4.0.12", "hashPath": "system.collections.concurrent.4.0.12.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hMxFT2RhhlffyCdKLDXjx8WEC5JfCvNozAZxCablAuFRH74SCV4AgzE8yJCh/73bFnEoZgJ9MJmkjQ0dJmnKqA==", "path": "system.collections.nongeneric/4.0.1", "hashPath": "system.collections.nongeneric.4.0.1.nupkg.sha512"}, "System.Composition/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E9oO9olNNxA39J8CxQwf7ceIPm+j/B/PhYpyK9M4LhN/OLLRw6u5fNInkhVqaWueMB9iXxYqnwqwgz+W91loIA==", "path": "system.composition/8.0.0", "hashPath": "system.composition.8.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyElSuvmBMYdn2iPG0n29i7Igu0bq99izOP3MAtEwskY3OP9jqsavvVmPn9lesVaj/KT/o/QkNjA43dOJTsDQw==", "path": "system.composition.attributedmodel/8.0.0", "hashPath": "system.composition.attributedmodel.8.0.0.nupkg.sha512"}, "System.Composition.Convention/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UuVkc1B3vQU/LzEbWLMZ1aYVssv4rpShzf8wPEyrUqoGNqdYKREmB8bXR73heOMKkwS6ZnPz3PjGODT2MenukQ==", "path": "system.composition.convention/8.0.0", "hashPath": "system.composition.convention.8.0.0.nupkg.sha512"}, "System.Composition.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qwbONqoxlazxcbiohvb3t1JWZgKIKcRdXS5uEeLbo5wtuBupIbAvdC3PYTAeBCZrZeERvrtAbhYHuuS43Zr1bQ==", "path": "system.composition.hosting/8.0.0", "hashPath": "system.composition.hosting.8.0.0.nupkg.sha512"}, "System.Composition.Runtime/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-G+kRyB5/6+3ucRRQz+DF4uSHGqpkK8Q4ilVdbt4zvxpmvLVZNmSkyFAQpJLcbOyVF85aomJx0m+TGMDVlwx7ZQ==", "path": "system.composition.runtime/8.0.0", "hashPath": "system.composition.runtime.8.0.0.nupkg.sha512"}, "System.Composition.TypedParts/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DsSklhuA+Dsgo3ZZrar8hjBFvq1wa1grrkNCTt+6SoX3vq0Vy+HXJnVXrU/nNH1BjlGH684A7h4hJQHZd/u5mA==", "path": "system.composition.typedparts/8.0.0", "hashPath": "system.composition.typedparts.8.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Console/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qSKUSOIiYA/a0g5XXdxFcUFmv1hNICBD7QZ0QhGYVipPIhvpiydY8VZqr1thmCXvmn8aipMg64zuanB4eotK9A==", "path": "system.console/4.0.0", "hashPath": "system.console.4.0.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.Process/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpVZ5bnlSs3tTeJ6jYyDJEIa6tavhAd88lxq1zbYhkkCu0Pno2+gHXcvZcoygq2d8JxW3gojXqNJMTAshduqZA==", "path": "system.diagnostics.process/4.1.0", "hashPath": "system.diagnostics.process.4.1.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6i2EbRq0lgGfiZ+FDf0gVaw9qeEU+7IS2+wbZJmFVpvVzVOgZEt0ScZtyenuBvs6iDYbGiF51bMAa0oDP/tujQ==", "path": "system.diagnostics.stacktrace/4.0.1", "hashPath": "system.diagnostics.stacktrace.4.0.1.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg==", "path": "system.diagnostics.tracing/4.1.0", "hashPath": "system.diagnostics.tracing.4.1.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ndUZlEkAMc1XzM0xGN++SsJrNhRkIHaKI8+te325vrUgoLT1ufWNI6KB8FFrL7NpRMHPrdxP99aF3fHbAPxW0A==", "path": "system.directoryservices.protocols/6.0.1", "hashPath": "system.directoryservices.protocols.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AJukBuLoe3QeAF+mfaRKQb2dgyrvt340iMBHYv+VdBzCUM06IxGlvl0o/uPOS7lHnXPN6u8fFRHSHudx5aTi8w==", "path": "system.formats.asn1/8.0.0", "hashPath": "system.formats.asn1.8.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q==", "path": "system.globalization.calendars/4.0.1", "hashPath": "system.globalization.calendars.4.0.1.nupkg.sha512"}, "System.Globalization.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg==", "path": "system.globalization.extensions/4.0.1", "hashPath": "system.globalization.extensions.4.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Watcher/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qM4Wr3La+RYb/03B0mZZjbA7tHsGzDffnuXP8Sl48HW2JwCjn3kfD5qdw0sqyNNowUipcJMi9/q6sMUrOIJ6UQ==", "path": "system.io.filesystem.watcher/4.0.0", "hashPath": "system.io.filesystem.watcher.4.0.0.nupkg.sha512"}, "System.IO.Hashing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sDnWM0N3AMCa86LrKTWeF3BZLD2sgWyYUc7HL6z4+xyDZNQRwzmxbo4qP2rX2MqC+Sy1/gOSRDah5ltxY5jPxw==", "path": "system.io.hashing/7.0.0", "hashPath": "system.io.hashing.7.0.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Management/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-s6c9x2Kghd+ncEDnT6ApYVOacDXr/Y57oSUSx6wjegMOfKxhtrXn3PdASPNU59y3kB9OJ1yb3l5k6uKr3bhqew==", "path": "system.management/6.0.2", "hashPath": "system.management.6.0.2.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULq9g3SOPVuupt+Y3U+A37coXzdNisB1neFCSKzBwo182u0RDddKJF8I5+HfyXqK6OhJPgeoAwWXrbiUXuRDsg==", "path": "system.net.http/4.1.0", "hashPath": "system.net.http.4.1.0.nupkg.sha512"}, "System.Net.NameResolution/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JdqRdM1Qym3YehqdKIi5LHrpypP4JMfxKQSNCJ2z4WawkG0il+N3XfNeJOxll2XrTnG7WgYYPoeiu/KOwg0DQw==", "path": "system.net.nameresolution/4.0.0", "hashPath": "system.net.nameresolution.4.0.0.nupkg.sha512"}, "System.Net.Primitives/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw==", "path": "system.net.primitives/4.0.11", "hashPath": "system.net.primitives.4.0.11.nupkg.sha512"}, "System.Net.Requests/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-vxGt7C0cZixN+VqoSW4Yakc1Y9WknmxauDqzxgpw/FnBdz4kQNN51l4wxdXX5VY1xjqy//+G+4CvJWp1+f+y6Q==", "path": "system.net.requests/4.0.11", "hashPath": "system.net.requests.4.0.11.nupkg.sha512"}, "System.Net.Sockets/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xAz0N3dAV/aR/9g8r0Y5oEqU1JRsz29F5EGb/WVHmX3jVSLqi2/92M5hTad2aNWovruXrJpJtgZ9fccPMG9uSw==", "path": "system.net.sockets/4.1.0", "hashPath": "system.net.sockets.4.1.0.nupkg.sha512"}, "System.Net.WebHeaderCollection/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XX2TIAN+wBSAIV51BU2FvvXMdstUa8b0FBSZmDWjZdwUMmggQSifpTOZ5fNH20z9ZCg2fkV1L5SsZnpO2RQDRQ==", "path": "system.net.webheadercollection/4.0.1", "hashPath": "system.net.webheadercollection.4.0.1.nupkg.sha512"}, "System.Net.WebSockets.WebSocketProtocol/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-O8RIMEVeOFzT8fscu6MDc4y+0LfnlXdqGovb0rJHBNVKhwn6BsNJmTY1oYUZPPsMfcc5OP20WpX4vj/aK8n98g==", "path": "system.net.websockets.websocketprotocol/4.5.3", "hashPath": "system.net.websockets.websocketprotocol.4.5.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reactive.Linq/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IB4/qlV4T1WhZvM11RVoFUSZXPow9VWVeQ1uDkSKgz6bAO+gCf65H/vjrYlwyXmojSSxvfHndF9qdH43P/IuAw==", "path": "system.reactive.linq/5.0.0", "hashPath": "system.reactive.linq.5.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z9PvtMJra5hK8n+g0wmPtaG7HQRZpTmIPRw5Z0LEemlcdQMHuTD5D7OAY/fZuuz1L9db++QOcDF0gJTLpbMtZQ==", "path": "system.reflection.metadataloadcontext/7.0.0", "hashPath": "system.reflection.metadataloadcontext.7.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "path": "system.runtime.caching/8.0.0", "hashPath": "system.runtime.caching.8.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Numerics/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg==", "path": "system.runtime.numerics/4.0.1", "hashPath": "system.runtime.numerics.4.0.1.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg==", "path": "system.security.cryptography.algorithms/4.2.0", "hashPath": "system.security.cryptography.algorithms.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw==", "path": "system.security.cryptography.cng/4.2.0", "hashPath": "system.security.cryptography.cng.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ==", "path": "system.security.cryptography.csp/4.0.0", "hashPath": "system.security.cryptography.csp.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ==", "path": "system.security.cryptography.encoding/4.0.0", "hashPath": "system.security.cryptography.encoding.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw==", "path": "system.security.cryptography.openssl/4.0.0", "hashPath": "system.security.cryptography.openssl.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "path": "system.security.cryptography.pkcs/8.0.0", "hashPath": "system.security.cryptography.pkcs.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA==", "path": "system.security.cryptography.primitives/4.0.0", "hashPath": "system.security.cryptography.primitives.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w==", "path": "system.security.cryptography.x509certificates/4.1.0", "hashPath": "system.security.cryptography.x509certificates.4.1.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puJ4UCh9JVRwOCyCIcq71JY6Axr8Sp8E2GjTIU1Fj8hm4+oX6NEoyGFGa/+pBG8SrVxbQPSj7hvtaREyTHHsmw==", "path": "system.security.cryptography.xml/6.0.0", "hashPath": "system.security.cryptography.xml.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Overlapped/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-f7aLuLkBoCQM2kng7zqLFBXz9Gk48gDK8lk1ih9rH/1arJJzZK9gJwNvPDhL6Ps/l6rwOr8jw+4FCHL0KKWiEg==", "path": "system.threading.overlapped/4.0.1", "hashPath": "system.threading.overlapped.4.0.1.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-IMXgB5Vf/5Qw1kpoVgJMOvUO1l32aC+qC3OaIZjWJOjvcxuxNWOK2ZTWWYXfij22NHxT2j1yWX5vlAeQWld9vA==", "path": "system.threading.threadpool/4.0.10", "hashPath": "system.threading.threadpool.4.0.10.nupkg.sha512"}, "System.Threading.Timer/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw==", "path": "system.threading.timer/4.0.1", "hashPath": "system.threading.timer.4.0.1.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "Tea/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-iYgLT7XCU1ncc1QTU5cAtEbXEnyDyQtFoLj6TyQJeJVqQDLl8e/yoo5fVvmASy5YlNgSxD8mDqNeLwoU6CwQqg==", "path": "tea/1.1.2", "hashPath": "tea.1.1.2.nupkg.sha512"}, "Tencent.QCloud.Cos.Sdk/5.4.34": {"type": "package", "serviceable": true, "sha512": "sha512-2bBeaa6fGHUI0zKRORNFK/s3SrEdQxEkqCEui6w/DCYnjaF+/XF09BGdxcIOn0h2EHHNjXPf10Iwh28gS0O93w==", "path": "tencent.qcloud.cos.sdk/5.4.34", "hashPath": "tencent.qcloud.cos.sdk.5.4.34.nupkg.sha512"}, "TencentCloudSDK.Common/3.0.1045": {"type": "package", "serviceable": true, "sha512": "sha512-Gpb9x9t7IOb73gjsqQ9GQzrW9GZvk8HmR8rBKCg+YxH3RCIHtuffQHd5KlgBg/PWvujFy/nE9I7UFVxBhC4Tbw==", "path": "tencentcloudsdk.common/3.0.1045", "hashPath": "tencentcloudsdk.common.3.0.1045.nupkg.sha512"}, "TencentCloudSDK.Sms/3.0.1045": {"type": "package", "serviceable": true, "sha512": "sha512-HyWnUDfl/f4lx2EENyoEc2LyLde/IM6d/62tTydMSy3/pHtNqIGBgpzSPWwSpIYsNOqHe+ktOgE73DKPDzVrfQ==", "path": "tencentcloudsdk.sms/3.0.1045", "hashPath": "tencentcloudsdk.sms.3.0.1045.nupkg.sha512"}, "UAParser/3.1.47": {"type": "package", "serviceable": true, "sha512": "sha512-I68Jl/Vs5RQZdz9BbmYtnXgujg0jVd61LhKbyNZOCm9lBxZFGxLbiQo6yFj21VYi7DzPvEvrVOmeC6v41AoLfw==", "path": "uaparser/3.1.47", "hashPath": "uaparser.3.1.47.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "BPM.Application/3.6.0": {"type": "project", "serviceable": false, "sha512": ""}, "BPM.Domain/3.6.0": {"type": "project", "serviceable": false, "sha512": ""}, "BPM.Extras.Youzan/3.6.0": {"type": "project", "serviceable": false, "sha512": ""}}}