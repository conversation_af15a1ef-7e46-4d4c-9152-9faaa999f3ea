using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;
using BPM.Application.Bill;

namespace BPM.Application;

/// <summary>
/// 本地任务-下载支付宝账单.
/// </summary>
[JobDetail("job_download_alipay_bill", Description = "下载支付宝账单", GroupName = "BuiltIn", Concurrent = true)]
public class AlipayBillJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 支付宝账单服务.
    /// </summary>
    private readonly AlipayBillService _alipayBillService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public AlipayBillJobService(IServiceScopeFactory serviceScopeFactory, AlipayBillService alipayBillService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _alipayBillService = alipayBillService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _alipayBillService.DownloadAlipayBill();
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 