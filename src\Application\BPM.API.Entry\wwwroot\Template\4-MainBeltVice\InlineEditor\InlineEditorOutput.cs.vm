@if(Model.IsUploading)
{
@:using BPM.Common.Models;
@:
}
namespace BPM.@(Model.NameSpace).<EMAIL>;
 
/// <summary>
/// @(Model.BusName)输出参数.
/// </summary>
public class @(Model.ClassName)InlineEditorOutput
{
@foreach (var column in Model.TableField)
{
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@if (column.PrimaryKey)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}else if(column.IsShow){
switch(column.bpmKey)
{
case "datePicker":
case "switch":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
break;
case "createTime":
case "modifyTime":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
break;
case "relationForm":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_id { get; set; }
@:
break;
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<List<string>> @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
}
break;
case "checkbox":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
break;
case "radio":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_name { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @(parameterName)_name { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(parameterName) { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(parameterName)_name { get; set; }
@:
break;
}
}
}
@if(Model.ConcurrencyLock)
{
    @:/// <summary>
    @:/// 乐观锁.
    @:/// </summary>
    @:public string version { get; set; }
@:
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
    @:/// <summary>
    @:/// 流程真实ID.
    @:/// </summary>
    @:public string flowTaskId { get; set; }
@:
}
@if(Model.EnableFlow)
{
    @:/// <summary>
    @:/// 流程状态.
    @:/// </summary>
    @:public int? flowState { get; set; }
@:
    @:/// <summary>
    @:/// 流程引擎ID.
    @:/// </summary>
    @:public string flowId { get; set; }
}
}