using BPM.DependencyInjection;

namespace BPM.Domain.Requests.product;

/// <summary>
/// 获取在售商品
/// </summary>
[SuppressSniffer]
public class getOnSaleProductRequest
{
    /// <summary>
    /// 店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店;默认网店渠道
    /// </summary>
    public int channel { get; set; } = -1;
    /// <summary>
    /// 页码，从1 ~100开始，分页数不能超过100页。
    /// </summary>
    public int page_no { get; set; } = 1;

    /// <summary>
    /// 行数
    /// </summary>
    public int page_size { get; set; } = 20;

    /// <summary>
    /// 排序字段
    /// </summary>
    public string order_by { get; set; } = "update_time:desc";

    /// <summary>
    /// 搜索字段，支持查询搜索商品名称和商品编码，支持商品名称模糊搜索
    /// </summary>
    public string q { get; set; }

    /// <summary>
    ///  更新开始时间
    /// </summary>
    public long? update_time_start { get; set; }

    /// <summary>
    /// 更新结束时间
    /// </summary>
    public long? update_time_end { get; set; }

    // 新增属性：用于接收日期字符串格式的时间
    public string update_time_start_str { get; set; }
    public string update_time_end_str { get; set; }
}
