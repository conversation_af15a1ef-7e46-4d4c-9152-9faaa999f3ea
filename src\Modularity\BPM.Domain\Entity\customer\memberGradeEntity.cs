﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 会员实体
/// </summary>
[SugarTable("MEMBER_GRADE")]
public class memberGradeEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 客户id
    /// </summary>
    public string customer_id { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string phone { get; set; }

    /// <summary>
    /// 等级编号
    /// </summary>
    public string grade_id { get; set; }

    /// <summary>
    /// 有赞等级编号
    /// </summary>
    public string yz_grade_id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_date { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime modify_date { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }

    /// <summary>
    /// 同步内容
    /// </summary>
    public string tag_body { get; set; }

}

