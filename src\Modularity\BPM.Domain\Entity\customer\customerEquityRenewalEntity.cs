﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 客户权益续期记录表
/// </summary>
[SugarTable("CUSTOMER_EQUITY_RENEWAL")]
[Tenant("IPOS-CRM")]
public class customerEquityRenewalEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 操作人记录（非校验必须是店铺下管理员身份员工 ）
    /// </summary>
    public string operator_yz_open_id { get; set; }

    /// <summary>
    /// 有赞用户id，用户在有赞的唯一id。推荐使用
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 权益卡号
    /// </summary>
    public string equity_card_no { get; set; }

    /// <summary>
    /// 结束时间延期天数 单位：天
    /// </summary>
    public int? extension_end_time { get; set; }

    /// <summary>
    /// 开始时间延期天数 单位：天 （该字段需要加白使用，并且卡要未生效状态。使用前请联系平台）
    /// </summary>
    public int? extension_begin_time { get; set; }

    /// <summary>
    /// 延期天数
    /// </summary>
    public int? extension_date_num { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime modify_date { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }


    /// <summary>
    /// 同步日志
    /// </summary>
    public string tag_body { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime? create_time { get; set; }


}

