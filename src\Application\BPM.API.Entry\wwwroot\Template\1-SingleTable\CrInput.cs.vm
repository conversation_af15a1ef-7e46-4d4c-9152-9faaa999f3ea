﻿@if(Model.IsUploading )
{
@:using BPM.Common.Models;
}
@if(Model.EnableFlow)
{
@:using BPM.Common.Models.WorkFlow;
}
using BPM.JsonSerialization;
using Newtonsoft.Json;

namespace BPM.@(Model.NameSpace).<EMAIL>;
 
/// <summary>
/// @(Model.BusName)修改输入参数.
/// </summary>
public class @(Model.ClassName)CrInput@(Model.EnableFlow ? " : FlowTaskOtherModel":"")
{
@foreach (var column in Model.TableField)
{
@if (column.bpmKey != null)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
    @:public List<string> @column.LowerColumnName { get; set; }
@:
}
else
{
    @:public string @column.LowerColumnName { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:public List<List<string>> @column.LowerColumnName { get; set; }
@:
}
else
{
    @:public List<string> @column.LowerColumnName { get; set; }
@:
}
break;
case "switch":
    @:[JsonConverter(typeof(BoolJsonConverter))]
    @:public bool @column.LowerColumnName { get; set; }
@:
break;
case "checkbox":
    @:public List<string> @column.LowerColumnName { get; set; }
@:
break;
case "radio":
    @:public string @column.LowerColumnName { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:public List<FileControlsModel> @column.LowerColumnName { get; set; }
@:
break;
case "createTime":
case "modifyTime":
    
@:
break;
default:
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
break;
}
}
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
    @:/// <summary>
    @:/// 流程真实ID.
    @:/// </summary>
    @:public string flowTaskId { get; set; }
@:
}
@if(Model.ConcurrencyLock)
{
    @:/// <summary>
    @:/// 乐观锁.
    @:/// </summary>
    @:public long version { get; set; }
@:
}
@if(Model.EnableFlow)
{
    @:/// <summary>
    @:/// 紧急程度.
    @:/// </summary>
    @:public int? flowUrgent { get; set; } = 1;
@:
    @:/// <summary>
    @:/// 流程状态.
    @:/// </summary>
    @:public int flowState { get; set; }
@:
}
@if(Model.EnableFlow || Model.Type == 3)
{
    @:/// <summary>
    @:/// 流程状态.
    @:/// </summary>
    @:public string flowId { get; set; }
}
}