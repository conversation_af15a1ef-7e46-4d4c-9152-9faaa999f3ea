using BPM.DependencyInjection;
using BPM.Domain.Entitys.Dto;
using BPM.Domain.Requests.product;

namespace BPM.Application.Services;

/// <summary>
/// 商品同步策略接口
/// </summary>
public interface IProductSyncStrategy
{
    /// <summary>
    /// 同步商品数据
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <param name="resultHandler">结果处理器</param>
    /// <returns>同步结果</returns>
    Task<dynamic> SyncProductsAsync(getOnSaleProductRequest request, ProductSyncResultHandler resultHandler);

    /// <summary>
    /// 是否支持该请求类型
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>是否支持</returns>
    bool CanHandle(getOnSaleProductRequest request);
}

/// <summary>
/// 时间范围同步策略
/// </summary>
[SuppressSniffer]
public class TimeRangeSyncStrategy : IProductSyncStrategy
{
    private readonly Func<getOnSaleProductRequest, long, long, Task<resultLogDto>> _autoProcessByTime;

    public TimeRangeSyncStrategy(Func<getOnSaleProductRequest, long, long, Task<resultLogDto>> autoProcessByTime)
    {
        _autoProcessByTime = autoProcessByTime;
    }

    public bool CanHandle(getOnSaleProductRequest request)
    {
        return request.update_time_start.HasValue && request.update_time_end.HasValue &&
               request.update_time_start.Value != 0 && request.update_time_end.Value != 0;
    }

    public async Task<dynamic> SyncProductsAsync(getOnSaleProductRequest request, ProductSyncResultHandler resultHandler)
    {
        return await _autoProcessByTime(request, request.update_time_start.Value, request.update_time_end.Value);
    }
}

/// <summary>
/// 直接同步策略
/// </summary>
[SuppressSniffer]
public class DirectSyncStrategy : IProductSyncStrategy
{
    private readonly Func<getOnSaleProductRequest, Task<resultLogDto>> _processProducts;

    public DirectSyncStrategy(Func<getOnSaleProductRequest, Task<resultLogDto>> processProducts)
    {
        _processProducts = processProducts;
    }

    public bool CanHandle(getOnSaleProductRequest request)
    {
        return !request.update_time_start.HasValue || !request.update_time_end.HasValue ||
               request.update_time_start.Value == 0 || request.update_time_end.Value == 0;
    }

    public async Task<dynamic> SyncProductsAsync(getOnSaleProductRequest request, ProductSyncResultHandler resultHandler)
    {
        return await _processProducts(request);
    }
}

/// <summary>
/// 商品同步策略工厂抽象类
/// </summary>
[SuppressSniffer]
public abstract class ProductSyncStrategyFactory
{
    /// <summary>
    /// 获取适合的策略
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>同步策略</returns>
    public abstract IProductSyncStrategy GetStrategy(getOnSaleProductRequest request);
}
