﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.refund;

/// <summary>
/// 交易退款明细结构体.
/// </summary>
[SuppressSniffer]
public class refundItemDto
{
    /// <summary>
    /// 退款状态.
    /// </summary>
    public string status { get; set; }

    /// <summary>
    /// 退款方式.
    /// </summary>
    public string refund_type { get; set; }

    /// <summary>
    /// 退款申请时间.
    /// </summary>
    public DateTime created { get; set; }

    /// <summary>
    /// 运费.
    /// </summary>
    public string refund_postage { get; set; }

    /// <summary>
    /// 修改日期
    /// </summary>
    public DateTime modified { get; set; }

    /// <summary>
    /// 退款时间
    /// </summary>
    public DateTime refund_account_time { get; set; }

    /// <summary>
    /// 退货明细.
    /// </summary>
    public List<Item> refund_order_item { get; set; }

    /// <summary>
    /// 退款金额详情.
    /// </summary>
    public List<Payment> refund_fund_list { get; set; }
}

/// <summary>
/// 退货明细.
/// </summary>
public class Item
{
    /// <summary>
    /// 退款金额.
    /// </summary>
    public long refund_fee { get; set; }

    /// <summary>
    /// 退款明细id.
    /// </summary>
    public string oid { get; set; }

    /// <summary>
    /// 退款数量.
    /// </summary>
    public int item_num { get; set; }
}

/// <summary>
/// 退货付款明细.
/// </summary>
public class Payment
{
    /// <summary>
    /// 退款ID.
    /// </summary>
    public string refund_id { get; set; }

    /// <summary>
    /// 退款流水号.
    /// </summary>
    public string refund_no { get; set; }

    /// <summary>
    /// 退款金额.
    /// </summary>
    public long refund_fee { get; set; }

    /// <summary>
    /// 支付渠道.
    /// </summary>
    public int pay_way { get; set; }

    /// <summary>
    /// 退款方式.
    /// </summary>
    public int refund_mode { get; set; }
}