using BPM.Domain.Dto.points;
using BPM.Domain.Dto.trade;
using BPM.Domain.Entity.order;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Services.Abstractions;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Threading.Tasks;
using System;


namespace BPM.Application;

/// <summary>
/// 订单管理服务类
/// 主要功能：
/// 1. 从有赞平台批量获取订单数据
/// 2. 解析和过滤订单信息
/// 3. 将订单数据保存到本地数据库
/// 4. 处理订单相关的业务逻辑
/// </summary>
/// <remarks>
/// 使用说明：
/// - 支持分页获取订单数据
/// - 自动处理订单去重
/// - 支持手机号解密
/// - 支持多租户数据库操作
/// - 订单数据包含主订单、订单明细和支付信息三个部分
/// </remarks>
[ApiDescriptionSettings(Tag = "订单管理", Name = "Order", Order = 600)]
[Route("api/order")]
public class OrderService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 数据库服务实例
    /// 用于处理多租户数据库连接和事务管理
    /// </summary>
    private readonly SqlSugarScope _db;

    /// <summary>
    /// 订单库数据仓储
    /// 用于处理订单主表数据
    /// </summary>
    private readonly ISqlSugarClient _repository;

    /// <summary>
    /// 订单明细库数据仓储
    /// 用于处理订单商品明细数据
    /// </summary>
    private readonly ISqlSugarClient _repository1;

    /// <summary>
    /// 订单支付库数据仓储
    /// 用于处理订单支付信息数据
    /// </summary>
    private readonly ISqlSugarClient _repository2;

    /// <summary>
    /// 有赞服务接口
    /// 用于调用有赞开放平台API
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 每页数据大小
    /// 用于分页查询订单数据，默认每页100条
    /// </summary>
    private const int PAGE_SIZE = 100;

    /// <summary>
    /// 订单类型
    /// NORMAL表示普通订单
    /// </summary>
    private const string ORDER_TYPE = "NORMAL";

    /// <summary>
    /// 最大查询天数
    /// 限制单次查询的时间范围不超过90天
    /// </summary>
    private const int MAX_QUERY_DAYS = 90;

    /// <summary>
    /// 日志记录器
    /// 用于记录服务运行时的关键信息和错误
    /// </summary>
    private readonly ILogger<OrderService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="youzanService">有赞服务接口</param>
    /// <param name="logger">日志记录器</param>
    public OrderService(ISqlSugarClient context, IYouzanService youzanService, ILogger<OrderService> logger)
    {
        _db = (SqlSugarScope)context;
        _repository = _db.AsTenant().GetConnection("IPOS-ORDER");
        _repository1 = _db.AsTenant().GetConnection("IPOS-ORDER");
        _repository2 = _db.AsTenant().GetConnection("IPOS-ORDER");
        _youzanService = youzanService;
        _logger = logger;
    }

    /// <summary>
    /// 批量查询订单接口
    /// </summary>
    /// <param name="request">查询参数，包含开始时间和结束时间。如果未传入时间，默认查询最近5天的订单</param>
    /// <returns>订单列表及查询结果信息</returns>
    /// <remarks>
    /// 功能说明：
    /// 1. 支持分页查询有赞平台订单数据
    /// 2. 自动过滤已支付订单
    /// 3. 支持item_type为0或20的订单
    /// 4. 自动处理手机号解密
    /// 5. 自动保存订单数据到本地数据库
    /// 6. 未传入时间参数时，默认查询最近5天的订单
    /// 
    /// 使用限制：
    /// - 查询时间范围不能超过90天
    /// - 时间格式必须为：yyyy-MM-dd HH:mm:ss
    /// - 开始时间不能大于结束时间
    /// </remarks>
    [AllowAnonymous]
    [HttpPost("ORDER")]
    public async Task<dynamic> GetTradesSold([FromBody] object requestObj)
    {
        try
        {
            GetTradesSoldRequest request;

            try
            {
                // 尝试将请求对象转换为 GetTradesSoldRequest
                if (requestObj is string jsonString)
                {
                    // 如果是字符串，尝试反序列化
                    request = JsonConvert.DeserializeObject<GetTradesSoldRequest>(jsonString);
                }
                else
                {
                    // 如果是对象，尝试转换
                    request = JsonConvert.DeserializeObject<GetTradesSoldRequest>(
                        JsonConvert.SerializeObject(requestObj));
                }

                // 验证转换后的对象
                if (request == null)
                {
                    return new {
                        code = 400,
                        msg = "无效的请求参数格式",
                        data = new {
                            succeed = 0,
                            fail = 0,
                            succeed_data = new List<string>(),
                            fail_data = new List<string> { "请求参数格式错误" }
                        },
                        extras = (object)null,
                        timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                    };
                }
            }
            catch (Exception ex)
            {
                return new {
                    code = 400,
                    msg = "请求参数解析失败",
                    data = new {
                        succeed = 0,
                        fail = 0,
                        succeed_data = new List<string>(),
                        fail_data = new List<string> { $"参数解析错误: {ex.Message}" }
                    },
                    extras = (object)null,
                    timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                };
            }

            // 验证日期格式
            if (!string.IsNullOrEmpty(request.start_created) &&
                !DateTime.TryParse(request.start_created, out _))
            {
                return new {
                    code = 400,
                    msg = "开始时间格式错误",
                    data = new {
                        succeed = 0,
                        fail = 0,
                        succeed_data = new List<string>(),
                        fail_data = new List<string> { "开始时间必须为有效的日期时间格式(yyyy-MM-dd HH:mm:ss)" }
                    },
                    extras = (object)null,
                    timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                };
            }

            if (!string.IsNullOrEmpty(request.end_created) &&
                !DateTime.TryParse(request.end_created, out _))
            {
                return new {
                    code = 400,
                    msg = "结束时间格式错误",
                    data = new {
                        succeed = 0,
                        fail = 0,
                        succeed_data = new List<string>(),
                        fail_data = new List<string> { "结束时间必须为有效的日期时间格式(yyyy-MM-dd HH:mm:ss)" }
                    },
                    extras = (object)null,
                    timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                };
            }

            // 验证时间范围
            if (!string.IsNullOrEmpty(request.start_created) &&
                !string.IsNullOrEmpty(request.end_created))
            {
                var startDate = DateTime.Parse(request.start_created);
                var endDate = DateTime.Parse(request.end_created);

                if (startDate > endDate)
                {
                    return new {
                        code = 400,
                        msg = "时间范围错误",
                        data = new {
                            succeed = 0,
                            fail = 0,
                            succeed_data = new List<string>(),
                            fail_data = new List<string> { "开始时间不能大于结束时间" }
                        },
                        extras = (object)null,
                        timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                    };
                }

                if ((endDate - startDate).TotalDays > MAX_QUERY_DAYS)
                {
                    return new {
                        code = 400,
                        msg = "查询时间范围过大",
                        data = new {
                            succeed = 0,
                            fail = 0,
                            succeed_data = new List<string>(),
                            fail_data = new List<string> { $"查询时间范围不能超过{MAX_QUERY_DAYS}天" }
                        },
                        extras = (object)null,
                        timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                    };
                }
            }

            // 处理默认时间范围
            if (string.IsNullOrEmpty(request.tid) &&
                (string.IsNullOrEmpty(request.start_created) || string.IsNullOrEmpty(request.end_created)))
            {
                request.end_created = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                request.start_created = DateTime.Now.AddDays(-5).Date.ToString("yyyy-MM-dd HH:mm:ss");
                _logger.LogInformation($"使用默认时间范围：{request.start_created} 至 {request.end_created}");
            }

            // 初始化结果统计变量
            var successCount = 0;    // 成功处理的订单数量
            var failCount = 0;       // 处理失败的订单数量
            var failReasons = new List<string>();  // 失败原因列表
            var successData = new List<string>();  // 成功数据列表
            var pageNo = 1;          // 当前页码，从1开始
            var hasMore = true;      // 是否还有更多数据需要处理

            // 循环分页获取订单数据
            while (hasMore)
            {
                // 构建有赞接口请求参数
                // 根据是否传入tid参数，构建不同的查询条件
                var param = new YouzanParameter
                {
                    url = "youzan.trades.sold.get/4.0.2",  // 有赞订单查询接口地址
                    method = "POST",                        // 请求方法
                    //body = !string.IsNullOrEmpty(request.tid)
                    //    ? new { tid = request.tid, type = ORDER_TYPE, page_no = pageNo, page_size = PAGE_SIZE }  // 按订单号查询
                    //    : new {
                    //        start_created = request.start_created,
                    //        end_created = request.end_created,         // 按时间范围查询
                    //        type = ORDER_TYPE,
                    //        page_no = pageNo,
                    //        page_size = PAGE_SIZE
                    //    }
                    body = !string.IsNullOrEmpty(request.tid)
                        ? new { tid = request.tid, page_no = pageNo, page_size = PAGE_SIZE }  // 按订单号查询
                        : new {
                            start_created = request.start_created,
                            end_created = request.end_created,         // 按时间范围查询
                            page_no = pageNo,
                            page_size = PAGE_SIZE
                        }
                };

                // 调用有赞接口获取订单数据
                var result = await _youzanService.GetData(param);

                // 处理接口调用失败的情况
                if (!result.success)
                {
                    // 返回错误信息
                    return new {
                        code = 500,
                        msg = result.message,
                        data = new { succeed = 0, fail = 0, succeed_data = new List<string>(), fail_data = new List<string> { result.message } },
                        extras = (object)null,
                        timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                    };
                }

                // 检查返回数据是否为空
                if (result.data == null)
                {
                    // 返回数据为空的错误信息
                    return new {
                        code = 500,
                        msg = "返回数据为空",
                        data = new { succeed = 0, fail = 0, succeed_data = new List<string>(), fail_data = new List<string> { "返回数据为空" } },
                        extras = (object)null,
                        timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                    };
                }

                // 解析返回的JSON数据
                var jObject = JObject.Parse(result.data.ToString());

                // 检查是否包含错误响应
                if (jObject["gw_err_resp"] != null)
                {
                    // 获取错误信息
                    var errMsg = jObject["gw_err_resp"]["err_msg"]?.ToString() ?? "未知错误";
                    // 返回错误信息
                    return new {
                        code = 500,
                        msg = errMsg,
                        data = new { succeed = 0, fail = 0, succeed_data = new List<string>(), fail_data = new List<string> { errMsg } },
                        extras = (object)null,
                        timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
                    };
                }

                // 将JSON数据反序列化为有赞订单响应对象
                var response = JsonConvert.DeserializeObject<YouzanOrderResponse>(result.data.ToString());

                // 如果没有订单数据，退出循环
                if (response?.full_order_info_list == null)
                {
                    break;
                }

                // 过滤订单数据
                // 1. 只保留已支付的订单
                // 2. 只保留商品类型为0或20的订单
                var filteredOrders = response.full_order_info_list
                    .Where(order =>
                        order?.full_order_info?.order_info?.order_tags?.is_payed == true &&
                        order.full_order_info.orders?.Any(item => item.item_type == 0 || item.item_type == 20 || item.item_type == 182) == true)
                    .ToList();

                // 处理每个订单
                foreach (var order in filteredOrders)
                {
                    try
                    {
                        // 获取订单基本信息
                        var orderInfo = order.full_order_info;
                        var orderNo = orderInfo.order_info.tid;
                        var title = orderInfo.orders?.FirstOrDefault()?.title ?? "";

                        // 检查订单是否已存在于数据库中
                        var existingOrder = await _repository.Queryable<orderEntity>()
                            .FirstAsync(x => x.order_no == orderNo);

                        // 如果订单已存在，记录失败信息并继续处理下一个订单
                        if (existingOrder != null)
                        {
                            failCount++;
                            var failMsg = $"当前记录存在:{orderNo} {title}";
                            failReasons.Add(failMsg);
                            //_logger.LogWarning($"订单已存在，跳过处理: {failMsg}");
                            // 跳过处理下一个订单
                            continue;
                        }

                        // 解密买家手机号
                        string phone = "";
                        var decryptParam = new YouzanParameter
                        {
                            url = "youzan.cloud.secret.decrypt.single/1.0.0",  // 有赞手机号解密接口
                            method = "POST",
                            body = new { source = orderInfo.buyer_info.buyer_phone }.ToJsonString()
                        };

                        // 调用解密接口
                        var decryptResult = await _youzanService.GetData(decryptParam);
                        // 处理解密结果：成功则使用解密后的手机号，失败则使用默认值
                        phone = decryptResult.success ? (decryptResult.data.IsNullOrEmpty() ? "匿名用户" : decryptResult.data.ToString()) : "解密失败";

                        // 保存订单数据到数据库
                        var saveResult = await SaveOrderData(orderInfo, phone, orderNo);

                        // 处理保存结果
                        if (string.IsNullOrEmpty(saveResult))
                        {
                            // 保存成功，增加成功计数
                            successCount++;
                            var successMsg = $"成功保存订单:{orderNo} {title} 买家手机:{phone}";
                            successData.Add(successMsg);
                            _logger.LogInformation($"订单保存成功: {successMsg}");
                        }
                        else
                        {
                            // 保存失败，记录失败信息
                            failCount++;
                            var failMsg = $"保存失败:{orderNo} {title} - {saveResult}";
                            failReasons.Add(failMsg);
                            _logger.LogError($"订单保存失败: {failMsg}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 处理订单过程中发生异常，记录失败信息
                        failCount++;
                        var failMsg = $"处理失败:{order?.full_order_info?.order_info?.tid ?? "未知订单"} - {ex.Message}";
                        failReasons.Add(failMsg);
                        _logger.LogError($"订单处理异常: {failMsg}");
                    }
                }

                // 检查是否还有更多数据需要处理
                hasMore = pageNo * PAGE_SIZE < response.total_results;
                pageNo++;  // 页码加1，准备处理下一页数据
            }

            // 在响应前输出详细的成功和失败数据日志
            _logger.LogInformation($"GetTradesSold方法执行完成 - 总成功数量: {successCount}, 总失败数量: {failCount}");

            if (successData.Count > 0)
            {
                _logger.LogInformation($"成功处理的订单详情:");
                foreach (var success in successData)
                {
                    _logger.LogInformation($"  - {success}");
                }
            }

            // if (failReasons.Count > 0)
            // {
            //     _logger.LogWarning($"失败处理的订单详情:");
            //     foreach (var fail in failReasons)
            //     {
            //         _logger.LogInformation($"  - {fail}");
            //     }
            // }

            // 返回标准格式的成功响应
            return new {
                code = 200,          // 状态码：200表示成功
                msg = "操作成功",     // 操作结果描述
                data = new           // 返回数据
                {
                    succeed = successCount,    // 成功处理的订单数量
                    fail = failCount,         // 处理失败的订单数量
                    succeed_data = successData,  // 成功数据详情列表
                    fail_data = failReasons   // 失败原因列表
                },
                extras = (object?)null,        // 扩展数据（本接口不使用）
                timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()  // 当前时间戳
            };
        }
        catch (Exception ex)
        {
            // 处理整个过程中发生的未预期异常
            _logger.LogError($"GetTradesSold方法执行异常: {ex.Message}");
            return new {
                code = 500,          // 状态码：500表示服务器错误
                msg = ex.Message,    // 错误信息
                data = new { succeed = 0, fail = 0, succeed_data = new List<string>(), fail_data = new List<string> { ex.Message } },
                extras = (object?)null,
                timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
            };
        }
    }

    /// <summary>
    /// 保存订单数据到数据库
    /// </summary>
    /// <param name="orderInfo">订单详细信息，包含订单主体、商品明细和支付信息</param>
    /// <param name="phone">解密后的买家手机号</param>
    /// <param name="orderNo">订单编号</param>
    /// <returns>成功返回空字符串，失败返回错误信息</returns>
    /// <remarks>
    /// 处理流程：
    /// 1. 保存订单主体信息
    /// 2. 保存订单商品明细
    /// 3. 保存订单支付信息
    /// 
    /// 注意事项：
    /// - 使用事务确保数据一致性
    /// - 自动处理字段长度限制
    /// - 自动生成必要的ID和序号
    /// </remarks>
    private async Task<string> SaveOrderData(YouzanFullOrderInfo orderInfo, string phone, string orderNo)
    {
        try
        {
            // 开启数据库事务
            // 确保三个表的数据要么全部保存成功，要么全部回滚
            _db.BeginTran();
            try
            {
                // 1. 保存订单主体信息到订单表
                // 包含：订单基本信息、买家信息、支付状态等
                var orderEntity = new orderEntity
                {
                    order_no = orderNo,                    // 订单编号
                    yz_open_id = orderInfo.buyer_info.yz_open_id,  // 买家有赞ID
                    buyer_phone = phone,                   // 解密后的买家手机号
                    status = orderInfo.order_info.status,  // 订单状态
                    created = orderInfo.order_info.created,  // 订单创建时间
                    kdt_id = orderInfo.order_info.node_kdt_id.ToString(),  // 店铺ID
                    kdt_name = orderInfo.order_info.shop_name,  // 店铺名称
                    total_fee = orderInfo.pay_info.total_fee.ParseToDecimal(),  // 订单总金额
                    pay_time = orderInfo.order_info.pay_time,  // 支付时间
                    pay_type = orderInfo.order_info.pay_type,  // 支付方式
                    item_type = orderInfo.orders?.FirstOrDefault()?.item_type ?? 0,  // 商品类型
                    is_fenxiao_order = !orderInfo.order_info.is_retail_order  // 是否分销订单
                };

                // 保存订单主体数据
                await _repository.Insertable(orderEntity).ExecuteCommandAsync();

                // 2. 保存订单商品明细
                // 一个订单可能包含多个商品，需要遍历保存
                if (orderInfo.orders != null && orderInfo.orders.Count > 0)
                {
                    // 构建订单商品明细列表
                    var orderItems = orderInfo.orders.Select((item, index) => new orderItemEntity
                    {
                        order_seq = (index + 1).ToString(),  // 商品序号，从1开始
                        order_no = orderNo,                  // 关联的订单编号
                        oid = item.oid ?? "",               // 商品订单号
                        item_id = item.item_id.ToString(),  // 商品ID
                        item_no = item.item_no ?? "",       // 商品编码
                        item_type = item.item_type,         // 商品类型
                        alias = item.alias ?? "",           // 商品别名
                        item_barcode = item.item_barcode ?? "",  // 商品条码
                        is_present = item.is_present ? 1 : 0,    // 是否赠品
                        num = item.num,                     // 购买数量
                        outer_item_id = item.outer_item_id ?? "",  // 外部商品ID
                        outer_sku_id = item.outer_sku_id ?? "",    // 外部规格ID
                        price = item.price.ParseToDecimal(),       // 商品单价
                        points_price = item.points_price.ParseToDecimal(),  // 积分价格
                        discount_price = item.discount_price.ParseToDecimal(),  // 优惠金额
                        payment = item.payment.ParseToDecimal(),    // 实付金额
                        sku_barcode = item.sku_barcode ?? "",      // 规格条码
                        sku_id = item.sku_id.ToString(),           // 规格ID
                        sku_no = item.sku_no ?? "",                // 规格编码
                        // 处理规格属性名称，限制长度为500
                        sku_properties_name = (item.sku_properties_name?.Length > 500 ?
                            item.sku_properties_name.Substring(0, 500) :
                            item.sku_properties_name) ?? "",
                        // 处理商品标题，限制长度为500
                        title = (item.title?.Length > 500 ?
                            item.title.Substring(0, 500) :
                            item.title) ?? "",
                        total_fee = item.total_fee.ParseToDecimal()  // 商品总金额
                    }).ToList();

                    // 批量保存订单商品明细
                    await _repository1.Insertable(orderItems).ExecuteCommandAsync();
                }

                // 3. 保存订单支付记录
                // 包含支付金额、运费、交易号等信息
                if (orderInfo.pay_info != null)
                {
                    var paymentEntity = new orderPaymentEntity
                    {
                        id = Guid.NewGuid().ToString("N"),  // 生成32位的唯一标识
                        order_no = orderNo,                 // 关联的订单编号
                        payment = orderInfo.pay_info.payment.ParseToDecimal(),  // 支付金额
                        post_fee = orderInfo.pay_info.post_fee.ParseToDecimal(),  // 运费
                        // 处理外部交易号（可能有多个）
                        outer_transactions = orderInfo.pay_info.outer_transactions != null ?
                            string.Join(",", orderInfo.pay_info.outer_transactions) : "",
                        // 处理交易号（可能有多个）
                        transaction = orderInfo.pay_info.transaction != null ?
                            string.Join(",", orderInfo.pay_info.transaction) : ""
                    };

                    // 保存支付记录
                    await _repository2.Insertable(paymentEntity).ExecuteCommandAsync();
                }

                // 提交事务
                _db.CommitTran();
                _logger.LogInformation($"成功处理订单: {orderNo}");
                return "";
            }
            catch (Exception ex)
            {
                // 发生异常时回滚事务
                _db.RollbackTran();
                _logger.LogError($"保存订单数据失败: {ex.Message}");
                return ex.Message;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"保存订单数据失败: {ex.Message}");
            return ex.Message;
        }
    }
}

/// <summary>
/// 批量查询订单请求参数类
/// </summary>
/// <remarks>
/// 使用说明：
/// - 时间格式：yyyy-MM-dd HH:mm:ss
/// - 时间范围不超过90天
/// - 开始时间必须小于结束时间
/// </remarks>
public class GetTradesSoldRequest
{
    /// <summary>
    /// 订单创建开始时间
    /// </summary>
    /// <example>2024-01-01 00:00:00</example>
    public string start_created { get; set; }

    /// <summary>
    /// 订单创建结束时间
    /// </summary>
    /// <example>2024-01-31 23:59:59</example>
    public string end_created { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    /// <example>E20240101123456789</example>
    public string tid { get; set; }
}
