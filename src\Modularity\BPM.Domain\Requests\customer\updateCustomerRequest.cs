﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.customer;

/// <summary>
/// 更新客户请求
/// </summary>
[SuppressSniffer]
public class updateCustomerRequest
{

    /// <summary>
    ///  用户创建时间(日期格式:yyyy-MM-dd HH:mm:ss)
    /// </summary>
    public string create_date { get; set; }

    /// <summary>
    /// 会员创建时间（时间格式:yyyy-MM-dd HH:mm:ss）当且仅当该用户是会员的情况下，更新会员创建时间有效
    /// </summary>
    public string member_created_at { get; set; }

    /// <summary>
    /// scrm渠道类型（2:伯俊）
    /// </summary>
    public int scrm_channel_type { get; set; } = 2;

    /// <summary>
    ///  外部requestId，在客户信息变更事件中会返回该字段
    /// </summary>
    public string external_request_id { get; set; }

    /// <summary>
    /// 是否走扩展点（不传默认为true）， true-走扩展点 false-不走扩展点(其中扩展点为：第三方修改客户信息扩展点)
    /// </summary>
    public bool is_do_ext_point { get; set; } = false;

    /// <summary>
    /// 用户帐号信息
    /// </summary>
    public accountInfo account { get; set; }

    /// <summary>
    /// 更新客户信息
    /// </summary>
    public customerUpdateInfo customer_update { get; set; }
}


public class accountInfo
{

    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 帐号类型。目前支持以下选项（只支持传一种）： FansID：自有粉丝ID， Mobile：手机号， YouZanAccount：有赞账号，YzOpenId：有赞OpenId
    /// </summary>
    public string account_type { get; set; }
}
/// <summary>
/// 客户更新信息
/// </summary>
public class customerUpdateInfo
{
    /// <summary>
    ///  微信
    /// </summary>
    public string wei_xin { get; set; }

    /// <summary>
    ///  性别，0：未知；1：男；2：女
    /// </summary>
    public int? gender { get; set; } = 0;

    /// <summary>
    /// 备注
    /// </summary>
    public string remark { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public string birthday { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 所属门店
    /// </summary>
    public long ascription_kdt_id { get; set; }

}