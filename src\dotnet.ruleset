<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name=".Net Rules" Description="These rules enforces consistent code quality guidelines." ToolsVersion="16.0">
  <Include Path="basicdesignguidelinerules.ruleset" Action="Default" />
  <Rules AnalyzerId="Microsoft.Analyzers.ManagedCodeAnalysis" RuleNamespace="Microsoft.Rules.Managed">
    <Rule Id="CA1014" Action="None" />
    <Rule Id="CA1020" Action="Warning" />
    <Rule Id="CA1021" Action="Warning" />
    <Rule Id="CA1040" Action="Warning" />
    <Rule Id="CA1045" Action="Warning" />
    <Rule Id="CA1501" Action="Warning" />
    <Rule Id="CA1502" Action="None" />
    <Rule Id="CA1504" Action="Warning" />
    <Rule Id="CA1505" Action="Warning" />
    <Rule Id="CA1700" Action="Warning" />
    <Rule Id="CA1701" Action="Warning" />
    <Rule Id="CA1702" Action="Warning" />
    <Rule Id="CA1703" Action="Warning" />
    <Rule Id="CA1704" Action="Warning" />
    <Rule Id="CA1707" Action="Warning" />
    <Rule Id="CA1709" Action="Warning" />
    <Rule Id="CA1710" Action="Warning" />
    <Rule Id="CA1711" Action="Info" />
    <Rule Id="CA1712" Action="Warning" />
    <Rule Id="CA1713" Action="Warning" />
    <Rule Id="CA1714" Action="Warning" />
    <Rule Id="CA1715" Action="Warning" />
    <Rule Id="CA1717" Action="Warning" />
    <Rule Id="CA1719" Action="Warning" />
    <Rule Id="CA1720" Action="Warning" />
    <Rule Id="CA1721" Action="Warning" />
    <Rule Id="CA1722" Action="Warning" />
    <Rule Id="CA1725" Action="Info" />
    <Rule Id="CA1726" Action="Warning" />
    <Rule Id="CA2204" Action="Warning" />
    <Rule Id="CA2254" Action="None" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="AD0001" Action="Info" />
    <Rule Id="CS1591" Action="None" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp.Features" RuleNamespace="Microsoft.CodeAnalysis.CSharp.Features">
    <Rule Id="IDE0007" Action="None" />
    <Rule Id="IDE0007WithoutSuggestion" Action="None" />
    <Rule Id="IDE0008" Action="Warning" />
    <Rule Id="IDE0079" Action="Hidden" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeAnalysis.NetAnalyzers" RuleNamespace="Microsoft.CodeAnalysis.NetAnalyzers">
    <Rule Id="CA1711" Action="Info" />
    <Rule Id="CA1725" Action="Info" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeQuality.Analyzers" RuleNamespace="Microsoft.CodeQuality.Analyzers">
    <Rule Id="CA1725" Action="Info" />
  </Rules>
  <Rules AnalyzerId="Roslynator.CSharp.Analyzers" RuleNamespace="Roslynator.CSharp.Analyzers">
    <Rule Id="RCS1008" Action="Info" />
    <Rule Id="RCS1009" Action="Hidden" />
    <Rule Id="RCS1012" Action="Hidden" />
    <Rule Id="RCS1022" Action="None" />
    <Rule Id="RCS1023" Action="None" />
    <Rule Id="RCS1024" Action="None" />
    <Rule Id="RCS1025" Action="None" />
    <Rule Id="RCS1026" Action="None" />
    <Rule Id="RCS1027" Action="None" />
    <Rule Id="RCS1028" Action="None" />
    <Rule Id="RCS1029" Action="Info" />
    <Rule Id="RCS1030" Action="None" />
    <Rule Id="RCS1031" Action="Info" />
    <Rule Id="RCS1046" Action="None" />
    <Rule Id="RCS1053" Action="None" />
    <Rule Id="RCS1054" Action="Hidden" />
    <Rule Id="RCS1057" Action="Info" />
    <Rule Id="RCS1062" Action="Hidden" />
    <Rule Id="RCS1067" Action="None" />
    <Rule Id="RCS1075" Action="None" />
    <Rule Id="RCS1076" Action="Hidden" />
    <Rule Id="RCS1079" Action="None" />
    <Rule Id="RCS1082" Action="Warning" />
    <Rule Id="RCS1083" Action="Warning" />
    <Rule Id="RCS1086" Action="None" />
    <Rule Id="RCS1087" Action="None" />
    <Rule Id="RCS1088" Action="None" />
    <Rule Id="RCS1092" Action="None" />
    <Rule Id="RCS1095" Action="Info" />
    <Rule Id="RCS1102" Action="None" />
    <Rule Id="RCS1109" Action="Info" />
    <Rule Id="RCS1115" Action="Hidden" />
    <Rule Id="RCS1155" Action="None" />
    <Rule Id="RCS1116" Action="Hidden" />
    <Rule Id="RCS1117" Action="Hidden" />
    <Rule Id="RCS1119" Action="Info" />
    <Rule Id="RCS1120" Action="Info" />
    <Rule Id="RCS1121" Action="Info" />
    <Rule Id="RCS1122" Action="None" />
    <Rule Id="RCS1125" Action="Hidden" />
    <Rule Id="RCS1127" Action="Info" />
    <Rule Id="RCS1131" Action="Hidden" />
    <Rule Id="RCS1137" Action="Hidden" />
    <Rule Id="RCS1139" Action="None" />
    <Rule Id="RCS1144" Action="Hidden" />
    <Rule Id="RCS1147" Action="Hidden" />
    <Rule Id="RCS1148" Action="Hidden" />
    <Rule Id="RCS1149" Action="Hidden" />
    <Rule Id="RCS1150" Action="Info" />
    <Rule Id="RCS1152" Action="Hidden" />
    <Rule Id="RCS1153" Action="None" />
    <Rule Id="RCS1167" Action="Hidden" />
    <Rule Id="RCS1168" Action="None" />
    <Rule Id="RCS1178" Action="Info" />
    <Rule Id="RCS1183" Action="Hidden" />
    <Rule Id="RCS1184" Action="None" />
    <Rule Id="RCS1185" Action="None" />
    <Rule Id="RCS1194" Action="None" />
    <Rule Id="RCS1207" Action="Hidden" />
    <Rule Id="RCS1210" Action="None" />
    <Rule Id="RCS1219" Action="None" />
    <Rule Id="RCS1231" Action="None" />
  </Rules>
  <Rules AnalyzerId="StyleCop.Analyzers" RuleNamespace="StyleCop.Analyzers">
    <Rule Id="SA1649" Action="None"/>
    <Rule Id="SA1600" Action="None"/>
    <Rule Id="SA1208" Action="None"/>
    <Rule Id="SA1616" Action="None"/>
    <Rule Id="SA1614" Action="None"/>
    <Rule Id="SA1505" Action="None" />
    <Rule Id="SA1508" Action="None" />
    <Rule Id="SA1518" Action="None" />
    <Rule Id="SA1000" Action="Info" />
    <Rule Id="SA1101" Action="None" />
    <Rule Id="SA1118" Action="None" />
    <Rule Id="SA1129" Action="None" />
    <Rule Id="SA1124" Action="None" />
    <Rule Id="SA1009" Action="None" />
    <Rule Id="SA1013" Action="None" />
    <Rule Id="SA1201" Action="None" />
    <Rule Id="SA1204" Action="None" />
    <Rule Id="SA1210" Action="None" />
    <Rule Id="SA1300" Action="None" />
    <Rule Id="SA1309" Action="None" />
    <Rule Id="SA1313" Action="None" />
    <Rule Id="SA1401" Action="None" />
    <Rule Id="SA1402" Action="None" />
    <Rule Id="SA1413" Action="None" />
    <Rule Id="SA1512" Action="None" />
    <Rule Id="SA1516" Action="None" />
    <Rule Id="SA1601" Action="None" />
    <Rule Id="SA1602" Action="None" />
    <Rule Id="SA1604" Action="None" />
    <Rule Id="SA1605" Action="None" />
    <Rule Id="SA1606" Action="None" />
    <Rule Id="SA1607" Action="None" />
    <Rule Id="SA1608" Action="None" />
    <Rule Id="SA1611" Action="None" />
    <Rule Id="SA1615" Action="None" />
    <Rule Id="SA1618" Action="None" />
    <Rule Id="SA1623" Action="None" />
    <Rule Id="SA1642" Action="None" />
    <Rule Id="SA1643" Action="None" />
    <Rule Id="SA1633" Action="None" />
    <Rule Id="SA1648" Action="None" />
    <Rule Id="SX1309" Action="Warning" />
    <Rule Id="SA1503" Action="None" />
    <Rule Id="SA1501" Action="None" />
    <Rule Id="SA1202" Action="None" />
  </Rules>
</RuleSet>