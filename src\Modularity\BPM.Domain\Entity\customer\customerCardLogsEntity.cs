﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;

/// <summary>
/// 客户会员卡日志
/// </summary>
[SugarTable("CUSTOMER_CARD_LOGS")]
public class customerCardLogsEntity
{
    /// <summary>
    /// 积分变更时间
    /// </summary>
    public string changeAt { get; set; }

    /// <summary>
    /// 积分过期时间
    /// </summary>
    public string expiredAt { get; set; }

    /// <summary>
    /// 变动积分，精确到小数点后两位，赠送积分为正数，扣减积分为负数
    /// </summary>
    public string changePoint { get; set; }
    /// <summary>
    /// 客户编号
    /// </summary>
    public string customerId { get; set; }
    /// <summary>
    /// 手机号
    /// </summary>
    public string customerPhone { get; set; }
    /// <summary>
    /// 积分渠道("ON_LINE_MALL"=在线商城)
    /// </summary>
    public string pointChannel { get; set; }

    /// <summary>
    /// 积分方式(1=POS积分;2=平台积分;3=积分调整;4=系统积分;5=积分消耗;99=其他)
    /// </summary>
    public string pointMode { get; set; }
    /// <summary>
    ///  积分类型(1=开卡礼包;2=升级礼包;3=购物积分;4=退货;7=积分调整;8=积分清零;12=积分抽奖;13=活动积分;99=其他)
    /// </summary>
    public string pointType { get; set; }
    /// <summary>
    /// 关联单号
    /// </summary>
    public string relatedNo { get; set; }

    /// <summary>
    /// 流水号
    /// </summary>
    public string serialNo { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updateTime { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }



}

