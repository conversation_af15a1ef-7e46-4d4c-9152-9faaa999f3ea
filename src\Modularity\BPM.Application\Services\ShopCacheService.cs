using BPM.Common.Manager;
using BPM.Domain.Entity.shop;
using SqlSugar;

namespace BPM.Application.Services;

/// <summary>
/// 店铺缓存服务
/// </summary>
public class ShopCacheService : ITransient
{
    private readonly SqlSugarProvider _repository;
    private readonly ICacheManager _cache;
    
    /// <summary>
    /// 缓存相关常量
    /// </summary>
    private static class CacheKeys
    {
        public const string SHOP_PREFIX = "shop";
        public const string SHOP_ID_PREFIX = "shop:source_no";
        public const string SHOP_SOURCE_PREFIX = "shop:source_no_shop";
        public const string ALL_SHOPS_LOADED = "all_shops_loaded";
        public static readonly TimeSpan CACHE_DURATION = TimeSpan.FromHours(24);
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public ShopCacheService(ISqlSugarClient context, ICacheManager cache)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<shopEntity>();
        _cache = cache;
    }

    /// <summary>
    /// 获取所有店铺信息并缓存
    /// </summary>
    public async Task<Dictionary<string, shopEntity>> LoadAllShopsToCache()
    {
        var result = new Dictionary<string, shopEntity>();

        try
        {
            // 检查是否已经加载过所有店铺
            var isLoaded = await _cache.GetAsync<bool>(CacheKeys.ALL_SHOPS_LOADED);
            if (!isLoaded)
            {
                Log.Information("开始加载所有店铺信息到缓存");
                
                // 从数据库获取所有店铺
                var allShops = await _repository.Queryable<shopEntity>().ToListAsync();
                
                // 批量写入缓存
                var tasks = new List<Task>();
                foreach (var shop in allShops)
                {
                    // 缓存店铺完整信息
                    var shopCacheKey = $"{CacheKeys.SHOP_PREFIX}:{shop.net_source_no}";
                    tasks.Add(_cache.SetAsync(shopCacheKey, shop, CacheKeys.CACHE_DURATION));

                    // 缓存店铺完整信息
                    var sourceShopCacheKey = $"{CacheKeys.SHOP_SOURCE_PREFIX}:{shop.source_no}";
                    tasks.Add(_cache.SetAsync(sourceShopCacheKey, shop, CacheKeys.CACHE_DURATION));

                    // 缓存店铺ID映射关系
                    var idCacheKey = $"{CacheKeys.SHOP_ID_PREFIX}:{shop.source_no}";
                    tasks.Add(_cache.SetAsync(idCacheKey, shop.id, CacheKeys.CACHE_DURATION));
                    
                    result[shop.net_source_no.ToString()] = shop;
                }
                
                // 等待所有缓存写入完成
                await Task.WhenAll(tasks);
                
                // 设置加载标记
                await _cache.SetAsync(CacheKeys.ALL_SHOPS_LOADED, true, CacheKeys.CACHE_DURATION);
                
                Log.Information($"成功加载 {allShops.Count} 个店铺到缓存");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"加载店铺信息到缓存时出错: {ex.Message}");
            throw;
        }
        
        return result;
    }

    /// <summary>
    /// 从缓存获取单个店铺信息
    /// </summary>
    /// <param name="netSourceNo">店铺网络来源编号</param>
    /// <returns>店铺实体</returns>
    public async Task<shopEntity> GetShopFromCache(string netSourceNo)
    {
        // 参数校验
        if (string.IsNullOrEmpty(netSourceNo))
        {
            Log.Warning("获取店铺缓存时网络来源编号为空");
            return null;
        }

        // 特殊店铺编号处理
        if (netSourceNo == "165085058")
        {
            var specialShop = new shopEntity
            {
                id = "1005",
                net_source_no = 165085058,
                source_no = 165085058
            };
            return specialShop;
        }
        // 特殊店铺编号处理
        if (netSourceNo == "143730711")
        {
            var specialShop = new shopEntity
            {
                id = "1035",  // 修改为数据库中实际的store_id
                net_source_no = 143730711,
                source_no = 143730711,
                store_no = "1035"  // 添加store_no字段
            };
            return specialShop;
        }
        
        // 构建缓存key
        var cacheKey = $"{CacheKeys.SHOP_PREFIX}:{netSourceNo}";
        
        // 首先尝试从Redis缓存获取数据
        var shop = await _cache.GetAsync<shopEntity>(cacheKey);
        
        // 如果缓存中没有数据，尝试重新加载所有店铺
        if (shop == null)
        {
            await LoadAllShopsToCache();
            shop = await _cache.GetAsync<shopEntity>(cacheKey);
            
            if (shop == null)
            {
                Log.Warning($"店铺 {netSourceNo} 在重新加载后仍未找到，尝试使用source_no获取");
                shop = await GetSourceShopFromCache(netSourceNo);
            }
        }
        
        return shop;
    }

    /// <summary>
    /// 从缓存获取单个店铺信息
    /// </summary>
    /// <param name="sourceNo">来源编号</param>
    /// <returns>店铺实体</returns>
    public async Task<shopEntity> GetSourceShopFromCache(string sourceNo)
    {
        // 参数校验
        if (string.IsNullOrEmpty(sourceNo))
        {
            Log.Warning("获取店铺缓存时来源编号为空");
            return null;
        }

        // 特殊店铺编号处理
        if (sourceNo == "165085058")
        {
            var specialShop = new shopEntity
            {
                id = "1005",
                net_source_no = 165085058,
                source_no = 165085058
            };
            return specialShop;
        }
        // // 特殊店铺编号处理
        // if (sourceNo == "143730711")
        // {
        //     var specialShop = new shopEntity
        //     {
        //         id = "1005",
        //         net_source_no = 143730711,
        //         source_no = 143730711
        //     };
        //     return specialShop;
        // }

        // 构建缓存key
        var cacheKey = $"{CacheKeys.SHOP_SOURCE_PREFIX}:{sourceNo}";
        
        // 首先尝试从Redis缓存获取数据
        var shop = await _cache.GetAsync<shopEntity>(cacheKey);

        // 如果缓存中没有数据，尝试重新加载所有店铺
        if (shop == null)
        {
            await LoadAllShopsToCache();
            shop = await _cache.GetAsync<shopEntity>(cacheKey);

            // 如果重新加载后仍未找到，尝试使用netSourceNo获取
            if (shop == null)
            {
                Log.Warning($"店铺 {sourceNo} 在重新加载后仍未找到，尝试使用net_source_no获取");
                shop = await GetShopFromCache(sourceNo);
            }
        }

        return shop;
    }

    /// <summary>
    /// 根据店铺编号获取店铺ID
    /// </summary>
    /// <param name="sourceNo">店铺编号</param>
    /// <returns>店铺ID</returns>
    public async Task<string> GetStoreIdFromCache(long sourceNo)
    {
        // 特殊店铺编号处理
        if (sourceNo == 165085058 )
        {   
            return "1005";
        }

        var cacheKey = $"{CacheKeys.SHOP_ID_PREFIX}:{sourceNo}";
        var storeId = await _cache.GetAsync<string>(cacheKey);
        
        if (string.IsNullOrEmpty(storeId))
        {   
            // 如果缓存未命中，重新加载所有店铺
            await LoadAllShopsToCache();
            storeId = await _cache.GetAsync<string>(cacheKey);
            
            // 如果仍然未找到，尝试通过net_source_no获取店铺信息
            if (string.IsNullOrEmpty(storeId))
            {   
                Log.Warning($"店铺编号 {sourceNo} 未找到对应的店铺ID，尝试使用net_source_no获取");
                var shop = await GetShopFromCache(sourceNo.ToString());
                if (shop != null)
                {
                    return shop.id;
                }
                
                Log.Warning($"店铺编号 {sourceNo} 通过net_source_no仍未找到对应的店铺ID");
                return sourceNo.ToString();
            }
        }
        
        return storeId;
    }

    /// <summary>
    /// 批量从缓存获取店铺信息
    /// </summary>
    /// <param name="netSourceNos">店铺网络来源编号列表</param>
    /// <returns>店铺信息列表</returns>
    public async Task<List<shopEntity>> GetShopsFromCache(List<string> netSourceNos)
    {
        // 参数校验
        if (netSourceNos == null || !netSourceNos.Any())
        {
            return new List<shopEntity>();
        }

        var result = new List<shopEntity>();
        var missingShops = false;
        
        // 尝试从缓存获取每个店铺
        foreach (var no in netSourceNos.Where(x => !string.IsNullOrEmpty(x)))
        {
            var cacheKey = $"{CacheKeys.SHOP_PREFIX}:{no}";
            var shop = await _cache.GetAsync<shopEntity>(cacheKey);
            if (shop != null)
            {
                result.Add(shop);
            }
            else
            {
                missingShops = true;
                break;
            }
        }
        
        // 如果有任何店铺未找到，重新加载所有店铺
        if (missingShops)
        {
            await LoadAllShopsToCache();
            
            // 重新尝试获取所有请求的店铺
            result.Clear();
            foreach (var no in netSourceNos.Where(x => !string.IsNullOrEmpty(x)))
            {
                var cacheKey = $"{CacheKeys.SHOP_PREFIX}:{no}";
                var shop = await _cache.GetAsync<shopEntity>(cacheKey);
                if (shop != null)
                {
                    result.Add(shop);
                }
                else
                {
                    Log.Warning($"店铺 {no} 在重新加载后仍未找到");
                }
            }
        }
        
        return result;
    }
}