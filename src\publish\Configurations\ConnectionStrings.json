{
  "ConnectionStrings": {
    "ConnectionConfigs": [
      {
        "ConfigId": "default",
        "DBName": "BPM.IPOS",
        "DBType": "SqlServer", //MySql;SqlServer;Oracle;PostgreSQL;Dm;Kdbndp;Sqlite;
        "Host": "**************",
        "Port": "1433",
        "UserName": "webapi",
        "Password": "jalkjroew783aiwr*&^!alekjf",
        "DBSchema": "dbo"
      },
      {
        "ConfigId": "BPM-Job", // 不可修改
        "DBName": "BPM.IPOS.Sundial",
        "DBType": "SqlServer", //MySql;SqlServer;Oracle;PostgreSQL;Dm;Kdbndp;Sqlite;
        "Host": "**************",
        "Port": "1433",
        "UserName": "webapi",
        "Password": "jalkjroew783aiwr*&^!alekjf",
        "DBSchema": "dbo"
      },
      {
        "ConfigId": "IPOS-CRM",
        "DBName": "CRM",
        "DBType": "SqlServer", //MySql;SqlServer;Oracle;PostgreSQL;Dm;Kdbndp;Sqlite;
        "Host": "**************",
        "Port": "1433",
        "UserName": "webapi",
        "Password": "jalkjroew783aiwr*&^!alekjf",
        "DBSchema": "dbo"
      },
      {
        "ConfigId": "IPOS-PRODUCT",
        "DBName": "PRODUCT",
        "DBType": "SqlServer", //MySql;SqlServer;Oracle;PostgreSQL;Dm;Kdbndp;Sqlite;
        "Host": "**************",
        "Port": "1433",
        "UserName": "webapi",
        "Password": "jalkjroew783aiwr*&^!alekjf",
        "DBSchema": "dbo"
      },
      {
        "ConfigId": "IPOS-ORDER",
        "DBName": "ORDER",
        "DBType": "SqlServer", //MySql;SqlServer;Oracle;PostgreSQL;Dm;Kdbndp;Sqlite;
        "Host": "**************",
        "Port": "1433",
        "UserName": "webapi",
        "Password": "jalkjroew783aiwr*&^!alekjf",
        "DBSchema": "dbo"
      }
    ]
  }
}