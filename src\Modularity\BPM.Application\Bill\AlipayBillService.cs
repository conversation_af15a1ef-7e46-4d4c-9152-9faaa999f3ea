﻿using BPM.Common.Enums;
using BPM.DatabaseAccessor;
using BPM.Domain.Dto.shop;
using BPM.Domain.Entity.shop;
using BPM.LinqBuilder;
using BPM.RemoteRequest.Extensions;
using CsvHelper;
using CsvHelper.Configuration;
using Essensoft.Paylink.Alipay;
using Essensoft.Paylink.Alipay.Domain;
using Essensoft.Paylink.Alipay.Request;
using System.Globalization;
using System.IO.Compression;
using System.Text.Json;

namespace BPM.Application.Bill;


/// <summary>
/// 支付宝对账单
/// </summary>
[ApiDescriptionSettings(Tag = "Bill", Name = "Bill", Order = 215)]
[Route("api/bill")]
public class AlipayBillService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 支付宝客户端
    /// </summary>
    private readonly IAlipayClient _alipayClient;


    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly ISqlSugarClient _sugarClient;

    /// <summary>
    /// 构造函数
    /// </summary>
    public AlipayBillService(IAlipayClient alipayClient, ISqlSugarClient sugarClient)
    {
        _alipayClient = alipayClient;
        _sugarClient = sugarClient;
    }



    /// <summary>
    /// 下载支付宝对账单
    /// </summary>
    /// <param name="bill_date">对账单日期，格式：yyyy-MM-dd，默认为前一天</param>
    /// <returns>返回下载结果的JSON字符串，包含状态码、消息、记录数等信息</returns>
    /// <remarks>
    /// 方法流程：
    /// 1. 获取对账单日期，默认为前一天
    /// 2. 获取所有支付宝配置信息
    /// 3. 遍历每个支付宝配置，下载并解析对账单
    /// 4. 保存解析后的账单数据
    /// 
    /// 返回结果说明：
    /// - status: 200表示成功，500表示失败
    /// - count: 成功处理的记录数
    /// - meg: 处理结果说明
    /// - trade_date: 对账单日期
    /// - syn_date: 同步时间
    /// </remarks>
    [HttpGet("download/alipay")]
    [AllowAnonymous]
    public async Task<dynamic> DownloadAlipayBill(string bill_date = "")
    {
        try
        {
            // 如果未指定日期，默认使用前一天
            if (bill_date.IsNullOrEmpty())
                bill_date = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");

            // 验证日期格式
            if (!DateTime.TryParseExact(bill_date, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
            {
                return new { status = 400, meg = "日期格式错误，请使用yyyy-MM-dd格式", count = 0, trade_date = bill_date, syn_date = DateTime.Now }.ToJsonString();
            }

            // 获取所有支付宝配置信息
            var alipayList = await getAlipayList();
            if (alipayList == null || !alipayList.Any())
            {
                return new { status = 200, meg = "没有找到支付宝配置信息", count = 0, trade_date = bill_date, syn_date = DateTime.Now }.ToJsonString();
            }

            var aliPayBills = new List<shopAlipayBillEntity>();
            var errors = new List<string>();

            // 遍历每个支付宝配置
            foreach (var item in alipayList)
            {
                try
                {
                    // 构建支付宝API配置
                    var config = new AlipayOptions()
                    {
                        AppId = item.app_id,
                        AppPrivateKey = item.app_private_key,
                        AlipayPublicKey = item.alipay_public_key,
                        SignType = item.sign_type
                    };

                    // 构建账单下载请求
                    var request = new AlipayDataDataserviceBillDownloadurlQueryRequest();
                    request.BizContent = JsonSerializer.Serialize(new {
                        bill_type = "trade",
                        bill_date = bill_date
                    });

                    // 执行账单下载请求
                    var response = await _alipayClient.ExecuteAsync(request, config);
                    if (response?.Code != "10000")
                    {
                        errors.Add($"支付宝配置[{item.app_id}]请求失败: {response?.Msg ?? "未知错误"}");
                        continue;
                    }

                    if (string.IsNullOrEmpty(response.BillDownloadUrl))
                    {
                        errors.Add($"支付宝配置[{item.app_id}]返回的下载地址为空");
                        continue;
                    }

                    // 下载账单ZIP文件并获取流
                    var (stream, encoding, response1) = await response.BillDownloadUrl.GetAsStreamAsync();
                    if (stream == null)
                    {
                        errors.Add($"支付宝配置[{item.app_id}]下载账单文件失败");
                        continue;
                    }

                    using (ZipArchive archive = new ZipArchive(stream, ZipArchiveMode.Read, true, encoding))
                    {
                        if (archive.Entries.Count == 0)
                        {
                            errors.Add($"支付宝配置[{item.app_id}]的账单ZIP文件为空");
                            continue;
                        }

                        // 遍历所有文件，查找并处理业务明细文件
                        bool foundDetailFile = false;
                        foreach (var entry in archive.Entries)
                        {
                            try
                            {
                                using (var reader = new StreamReader(entry.Open(), System.Text.Encoding.GetEncoding("GB2312")))
                                {
                                    // 检查第一行是否是业务明细文件
                                    var firstLine = reader.ReadLine();
                                    if (firstLine == null || !firstLine.Contains("#支付宝业务明细查询"))
                                    {
                                        continue;
                                    }

                                    // 是业务明细文件，继续处理CSV数据
                                    foundDetailFile = true;
                                    var csv_config = new CsvConfiguration(CultureInfo.InvariantCulture)
                                    {
                                        HasHeaderRecord = true,
                                        MissingFieldFound = null,
                                        BadDataFound = null,
                                        IgnoreBlankLines = true
                                    };

                                    using (var csv = new CsvReader(reader, csv_config))
                                    {
                                        csv.Context.RegisterClassMap<AliPayBillMap>();
                                        var isHeader = true;
                                        var recordCount = 0;

                                        while (csv.Read())
                                        {
                                            var field = csv.GetField(0);
                                            if (string.IsNullOrEmpty(field) || field.StartsWith('#'))
                                            {
                                                continue;
                                            }

                                            if (isHeader)
                                            {
                                                csv.ReadHeader();
                                                isHeader = false;
                                                continue;
                                            }

                                            try
                                            {
                                                var model = csv.GetRecord<shopAlipayBillEntity>();
                                                if (model != null && !string.IsNullOrEmpty(model.transaction_id) && !string.IsNullOrEmpty(model.out_trade_no))
                                                {
                                                    recordCount++;
                                                    model.id = SnowflakeIdHelper.NextId();
                                                    model.terminal_id = (model.terminal_id ?? "").Replace("\t", "");
                                                    model.out_trade_no = (model.out_trade_no ?? "").Replace("\t", "");
                                                    model.shop_id = (model.shop_id ?? "").Replace("\t", "");
                                                    model.operator_id = (model.operator_id ?? "").Replace("\t", "");
                                                    model.trade_type = (model.trade_type ?? "").Replace("\t", "");
                                                    model.transaction_id = (model.transaction_id ?? "").Replace("\t", "");
                                                    model.out_refund_no = (model.out_refund_no ?? "").Replace("\t", "");
                                                    model.bill_date = bill_date;

                                                    aliPayBills.Add(model);
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                errors.Add($"支付宝配置[{item.app_id}]解析第{recordCount + 1}条记录失败: {ex.Message}");
                                            }
                                        }

                                        if (recordCount == 0)
                                        {
                                            errors.Add($"支付宝配置[{item.app_id}]没有有效的账单记录");
                                        }
                                    }
                                    break; // 找到并处理完明细文件后退出循环
                                }
                            }
                            catch (Exception ex)
                            {
                                errors.Add($"支付宝配置[{item.app_id}]读取文件[{entry.FullName}]失败: {ex.Message}");
                            }
                        }

                        if (!foundDetailFile)
                        {
                            errors.Add($"支付宝配置[{item.app_id}]未找到业务明细文件");
                            continue;
                        }
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"处理支付宝配置[{item.app_id}]时发生错误: {ex.Message}");
                }
            }

            // 处理最终结果
            if (aliPayBills.Count > 0)
            {
                try
                {
                    await saveAlipayBill(aliPayBills, bill_date);
                    return new {
                        count = aliPayBills.Count,
                        status = 200,
                        meg = errors.Any() ? $"同步部分成功，成功{aliPayBills.Count}条，{errors.Count}个错误" : "同步成功",
                        errors = errors,
                        trade_date = bill_date,
                        syn_date = DateTime.Now
                    }.ToJsonString();
                }
                catch (Exception ex)
                {
                    return new {
                        status = 500,
                        meg = "保存数据时发生错误",
                        error = ex.Message,
                        count = aliPayBills.Count,
                        trade_date = bill_date,
                        syn_date = DateTime.Now
                    }.ToJsonString();
                }
            }
            else
            {
                return new {
                    count = 0,
                    status = 200,
                    meg = errors.Any() ? $"同步失败，发生{errors.Count}个错误" : "当天没有交易记录",
                    errors = errors,
                    trade_date = bill_date,
                    syn_date = DateTime.Now
                }.ToJsonString();
            }
        }
        catch (Exception ex)
        {
            return new {
                status = 500,
                meg = "处理过程中发生异常",
                error = ex.Message,
                trade_date = bill_date,
                syn_date = DateTime.Now
            }.ToJsonString();
        }
    }

    /// <summary>
    /// 获取支付宝配置列表
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task<List<shopAlipayOutInput>> getAlipayList()
    {
        return await _sugarClient.Queryable<shopAlipayConfigEntity>()
            //.Where(x => x.shop_id == "3002")
            .GroupBy(x => new { x.app_id, x.app_private_key, x.alipay_public_key, x.sign_type })
           .Select(a => new shopAlipayOutInput
           {
               app_id = a.app_id,
               alipay_public_key = a.alipay_public_key,
               app_private_key = a.app_private_key,
               sign_type = a.sign_type
           }).ToListAsync();
    }

    /// <summary>
    /// 保存支付宝对账单
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    [NonAction]
    [UnitOfWork]
    private async Task saveAlipayBill(List<shopAlipayBillEntity> list, string bill_date)
    {
        // 删除当天已对账记录
        await _sugarClient.Deleteable<shopAlipayBillEntity>(p => p.bill_date.Equals(bill_date)).ExecuteCommandAsync();
        // 插入当前对账明细
        await _sugarClient.Insertable(list).ExecuteCommandAsync();

    }

}



/// <summary>
/// 支付宝对账单关系映射
/// </summary>
public class AliPayBillMap : ClassMap<shopAlipayBillEntity>
{
    public AliPayBillMap()
    {
        Map(x => x.transaction_id).Name("支付宝交易号");
        Map(x => x.out_trade_no).Name("商户订单号");
        Map(x => x.shop_id).Name("门店编号");
        Map(x => x.terminal_id).Name("终端号");
        Map(x => x.operator_id).Name("操作员");
        Map(x => x.trade_fee).Name("商家实收（元）");
        Map(x => x.trade_type).Name("业务类型");
        Map(x => x.trade_time).Name("创建时间");
        Map(x => x.finish_time).Name("完成时间");
        Map(x => x.charge).Name("服务费（元）");
        Map(x => x.out_refund_no).Name("退款批次号/请求号");
        Map(x => x.body).Name("备注");
    }
}