﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 客户会员卡实体
/// </summary>
[SugarTable("CUSTOMER_CARD")]
[Tenant("IPOS-CRM")]
public class customerCardEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 客户编号
    /// </summary>
    public string customer_sn { get; set; }

    /// <summary>
    /// 权益编号
    /// </summary>
    public string grade_id { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string phone { get; set; }

    /// <summary>
    /// 门店号
    /// </summary>
    public string store_id { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int state { get; set; }

    /// <summary>
    /// 积分
    /// </summary>
    public decimal point { get; set; }

    /// <summary>
    /// 来源卡号
    /// </summary>
    public string outer_card_no { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public int source { get; set; }

    /// <summary>
    /// 权益规则编号
    /// </summary>
    public string backage_code { get; set; } = "1";

    /// <summary>
    /// 操作类型,1=开卡、2=续费
    /// </summary>
    public int type { get; set; } = 1;

    /// <summary>
    /// 权益卡本次发生变更的类型 1.续费: 2.到期:3.延长有效期: 4.退款;5.作废权益卡
    /// </summary>
    public int change_type { get; set; }


    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime exp_date { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_date { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_mall_status { get; set; }

    /// <summary>
    /// 版本时间戳
    /// </summary>
    public long versionTimestamp { get; set; }

}

