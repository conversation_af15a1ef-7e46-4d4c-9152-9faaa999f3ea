﻿global using BPM.Common.Extension;
global using BPM.Common.Security;
global using BPM.DependencyInjection;
global using BPM.DynamicApiController;
global using BPM.Extras.Youzan.Request;
global using BPM.Extras.Youzan.Services.Abstractions;
global using BPM.FriendlyException;
global using BPM.Logging;
global using BPM.Logging.Attributes;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using SqlSugar;
global using System.Web;