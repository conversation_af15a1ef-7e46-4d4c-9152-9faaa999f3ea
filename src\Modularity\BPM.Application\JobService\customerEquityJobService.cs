﻿using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;

namespace BPM.Application;

/// <summary>
/// 本地任务-创建客户.
/// </summary>
[JobDetail("job_create_customer_equity", Description = "创建客户权益", GroupName = "BuiltIn", Concurrent = true)]
public class customerEquityJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// crm服务.
    /// </summary>
    private readonly CustomerEquityService _customerEquityService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public customerEquityJobService(IServiceScopeFactory serviceScopeFactory, CustomerEquityService customerEquityService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _customerEquityService = customerEquityService;
    }


    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        resultLogDto result = await _customerEquityService.createCustomerEquity();
        context.Result = string.Format("执行成功,成功:" + result.succeed + "行");
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
}
