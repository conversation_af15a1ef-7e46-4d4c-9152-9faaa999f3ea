﻿using BPM.DependencyInjection;

namespace BPM.Business.Entitys.Dto.shop;

/// <summary>
/// 门店列表输出.
/// </summary>
[SuppressSniffer]
public class shopListOutput:shopBaseDto
{
    /// <summary>
    /// 主键
    /// </summary>
    public string id { get; set; }

    /// <summary>
    /// 创建时间.
    /// </summary>
    public DateTime? creatorTime { get; set; }

    /// <summary>
    /// 创建人.
    /// </summary>
    public string creatorUser { get; set; }

    /// <summary>
    /// 修改时间.
    /// </summary>
    public DateTime? lastModifyTime { get; set; }
}