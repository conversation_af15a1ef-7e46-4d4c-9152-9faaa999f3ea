﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.customer;

/// <summary>
/// 客户级别响应
/// </summary>
[SuppressSniffer]
public class customerLevelResponse
{

    /// <summary>
    /// 会员等级集合
    /// </summary>
    public List<levelItem> level_details { get; set; }

}


public class levelItem
{
    /// <summary>
    /// 是否启用；true-启用,false-禁用
    /// </summary>
    public bool is_enabled { get; set; }

    /// <summary>
    /// 成长值会员的成长值门槛
    /// </summary>
    public int min_growth { get; set; }

    /// <summary>
    /// 生效持续天数,termType=2时，改值有效
    /// </summary>
    public int term_days { get; set; }

    /// <summary>
    /// 等级值
    /// </summary>
    public int level_value { get; set; }

    /// <summary>
    /// 等级别名
    /// </summary>
    public string level_alias { get; set; }
    /// <summary>
    /// 注册会员
    /// </summary>
    public string name { get; set; }

    /// <summary>
    ///  等级类型：1.免费类型2.付费类型
    /// </summary>
    public int level_type { get; set; }

    /// <summary>
    /// 生效方式模式:1-永久有效，2-从领取开始，持续一段时长
    /// </summary>
    public int term_type { get; set; }

}