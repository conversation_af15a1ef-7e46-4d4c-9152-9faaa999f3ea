namespace BPM.Application.DTOs;

/// <summary>
/// 更新商品价格DTO
/// </summary>
public class updateProducPriceDto
{
    /// <summary> 
    /// 商品编号
    /// </summary> 
    public long product_id { get; set; }

    /// <summary> 
    /// 是否多规格
    /// </summary> 
    public int has_sku { get; set; }

    /// <summary> 
    /// 规格编号
    /// </summary> 
    public long sku_id { get; set; }

    /// <summary> 
    /// 门店销售价
    /// </summary> 
    public long sale_price { get; set; }
}

/// <summary>
/// 更新商品库存DTO
/// </summary>
public class updateProducStockDto
{
    /// <summary> 
    /// 门店编号
    /// </summary> 
    public long store_id { get; set; }

    /// <summary> 
    /// 商品编号
    /// </summary> 
    public long product_id { get; set; }

    /// <summary> 
    /// 是否多规格
    /// </summary> 
    public int? has_sku { get; set; }

    /// <summary> 
    /// 规格编号
    /// </summary> 
    public long sku_id { get; set; }

    /// <summary> 
    /// 门店库存
    /// </summary> 
    public int? stock { get; set; }
}

/// <summary>
/// 更新多规格SKU
/// </summary>
public class updateMultiSku
{
    /// <summary>
    /// SKU编号
    /// </summary>
    public long sku_id { get; set; }
    
    /// <summary>
    /// 更新库存参数列表
    /// </summary>
    public List<updateStockParam> update_price_stock_open_param_list { get; set; } = new List<updateStockParam>();
}

/// <summary>
/// 更新库存参数
/// </summary>
public class updateStockParam
{
    /// <summary>
    /// 节点店铺ID
    /// </summary>
    public long node_kdt_id { get; set; }
    
    /// <summary>
    /// 库存数量
    /// </summary>
    public int? stock { get; set; }
}

/// <summary>
/// 补全商品请求参数
/// </summary>
public class CompleteProductsRequest
{
    /// <summary>
    /// 最大处理数量
    /// </summary>
    public int maxCount { get; set; } = 100;
}
