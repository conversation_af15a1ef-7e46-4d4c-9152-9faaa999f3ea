﻿using SqlSugar;

namespace BPM.Domain.Entity.order;

/// <summary>
/// 订单支付记录
/// </summary>
[SugarTable("ORDER_PAYMENT")]
[Tenant("IPOS-ORDER")]
public class orderPaymentEntity
{

    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public string order_no { get; set; }

    /// <summary>
    /// 交易金额
    /// </summary>
    public decimal payment { get; set; }

    /// <summary>
    /// 运费
    /// </summary>
    public decimal post_fee { get; set; }

    /// <summary>
    /// 外部交易单号
    /// </summary>
    public string outer_transactions { get; set; }

    /// <summary>
    /// 交易单号
    /// </summary>
    public string transaction { get; set; }

}
