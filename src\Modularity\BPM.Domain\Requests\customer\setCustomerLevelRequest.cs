﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.customer;

/// <summary>
/// 客户级别请求
/// </summary>
[SuppressSniffer]
public class setCustomerLevelRequest
{

    /// <summary>
    /// 是否走(修改等级)扩展点，如果为true走扩展点，为false不走扩展点，默认为true
    /// </summary>
    public bool is_do_ext_point { get; set; } = false;

    /// <summary>
    /// 会员等级别名，有赞系统生成，可以使用【youzan.scrm.level.list】接口获得
    /// </summary>
    public string level_alias { get; set; }

    /// <summary>
    /// 外部requestId，在等级变更事件中会返回该字段
    /// </summary>
  //  public string external_request_id { get; set; }

    /// <summary>
    ///  设置等级来源的kdtId（外部传入）
    /// </summary>
   // public long origin_kdt_id { get; set; }

    /// <summary>
    ///  扩展 信息
    /// </summary>
  //  public levelExtInfo ext_info { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public userInfo user { get; set; }

}

/// <summary>
/// 等级扩展信息
/// </summary>
public class levelExtInfo
{
    /// <summary>
    /// 会员邀请导购账户帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
    /// </summary>
    public int invited_account_type { get; set; } = 5;

    /// <summary>
    /// 等级变更类型（1：商家设置等级 2：资产合并 3：触发升级规则 4：系统设置 5：主动退出 6：重新入会 7：融合仓-规则回回流）
    /// </summary>
    public int up_grade_type { get; set; } = 3;

    /// <summary>
    /// 会员邀请标识（invited_account_id 二选一即可 ）
    /// </summary>
    public string invited_sl { get; set; }

    /// <summary>
    /// 会员邀请导购账户ID；配合invited_account_type字段
    /// </summary>
    public string invited_account_id { get; set; }

    /// <summary>
    /// 目前仅连接器使用。设置等级锁类型，防止调用线下和调用线上使用同一把锁产生死锁。（1：连接器回流锁）
    /// </summary>
    public int set_level_lock_type { get; set; }

    /// <summary>
    /// 会员来源渠道 （900：有赞云开放平台）
    /// </summary>
    public int member_src_channel { get; set; } = 900;

    /// <summary>
    /// 会员来源方式 （302：三方平台）
    /// </summary>
    public int member_src_way { get; set; } = 302;
}

public class userInfo
{
    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
    /// </summary>
    public int account_type { get; set; } = 2;

}

/// <summary>
/// 客户级别请求
/// </summary>
[SuppressSniffer]
public class setCustomerLevelRequest_v3
{

    /// <summary>
    /// 会员等级别名，有赞系统生成，可以使用【youzan.scrm.level.list】接口获得
    /// </summary>
    public string level_alias { get; set; }

    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 账户类型
    /// </summary>
    public string account_type { get; set; } = "Mobile";

}
