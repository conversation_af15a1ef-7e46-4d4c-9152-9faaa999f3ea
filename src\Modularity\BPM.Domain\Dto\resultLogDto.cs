﻿using BPM.DependencyInjection;

namespace BPM.Domain.Entitys.Dto;

/// <summary>
/// 测试列表.
/// </summary>
[SuppressSniffer]
public class resultLogDto
{
    /// <summary>
    /// 成功数量
    /// </summary>
    public int? succeed { get; set; } = 0;

    /// <summary>
    /// 失败数量
    /// </summary>
    public int? fail { get; set; } = 0;

    /// <summary>
    ///  成功数据
    /// </summary>
    public object succeed_data { get; set; }

    /// <summary>
    ///  失败数据
    /// </summary>
    public object fail_data { get; set; }
}
