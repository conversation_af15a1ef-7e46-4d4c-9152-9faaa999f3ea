﻿using BPM.Domain.Entity.customer;
using BPM.Domain.Entitys.Dto;

namespace BPM.Application;

/// <summary>
/// 会员管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11
/// </summary>
[ApiDescriptionSettings(Tag = "会员管理", Name = "Crm", Order = 600)]
[Route("api/[controller]")]
public class MemberService : IDynamicApiController, ITransient
{

    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly SqlSugarProvider _repository;


    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context"></param>
    public MemberService(ISqlSugarClient context)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<customerCardEntity>();
    }

    /// <summary>
    /// 同步会员信息
    /// </summary>
    /// <returns>同步结果</returns>
    public async Task<dynamic> syncMember()
    {
        var succeed = 0;
        var fail = 0;
        var errorData = new List<string>();

        try
        {
            // 获取需要同步的会员数据
            var members = await _repository.Queryable<customerCardEntity>()
                .Where(x => x.tag_status == "sync")
                .ToListAsync();

            foreach (var member in members)
            {
                try
                {
                    // 在这里添加同步会员信息的具体逻辑
                    // TODO: 实现具体的同步逻辑
                    
                    // 更新同步状态
                    member.tag_status = "synced";
                    member.created_date = DateTime.Now;
                    await _repository.Updateable(member).ExecuteCommandAsync();
                    
                    succeed++;
                }
                catch (Exception ex)
                {
                    fail++;
                    errorData.Add($"会员 {member.id} 同步失败: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            fail++;
            errorData.Add($"同步过程发生错误: {ex.Message}");
        }

        return new resultLogDto
        {
            succeed = succeed,
            fail = fail,
            fail_data = errorData
        };
    }
}
