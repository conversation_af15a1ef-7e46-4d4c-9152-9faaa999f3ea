using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;

namespace BPM.Application;

/// <summary>
/// 本地任务-更新CRM OpenId2.
/// </summary>
[JobDetail("job_crm_update_openid2", Description = "更新CRM OpenId2", GroupName = "BuiltIn", Concurrent = true)]
public class CrmUpdateOpenId2JobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// CRM服务.
    /// </summary>
    private readonly CustomerService _customerService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public CrmUpdateOpenId2JobService(IServiceScopeFactory serviceScopeFactory, CustomerService customerService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _customerService = customerService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _customerService.updateCustomerOpenId2();
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 