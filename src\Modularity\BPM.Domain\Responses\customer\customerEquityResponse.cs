﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.customer;

/// <summary>
/// 客户级别响应
/// </summary>
[SuppressSniffer]
public class customerEquityResponse
{
    /// <summary>
    /// 权益等级集合
    /// </summary>
    public object items { get; set; }

    /// <summary>
    /// 每页条数。默认30条
    /// </summary>
    public int page_size { get; set; }

    /// <summary>
    /// 分页
    /// </summary>
    public int page { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int total { get; set; }

}


public class customerEquityItem
{
   

}