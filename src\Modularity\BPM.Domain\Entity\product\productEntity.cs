﻿using SqlSugar;

namespace BPM.Domain.Entity.product;


/// <summary>
/// 版 本 BPM敏捷开发框架
/// Copyright (c) 2018-2022 深圳市中畅源科技开发有限公司
/// 创建人：Aarons
/// 日 期：2022.03.21
/// 描 述：商品实体
/// </summary>
[SugarTable("PDT_PRODUCT")]
[Tenant("IPOS-PRODUCT")]
public class productEntity : baseEntity
{
    /// <summary> 
    /// 商品编号 
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "product_id", IsPrimaryKey = true)]
    public string product_id { get; set; }

    /// <summary> 
    /// 门店编号（sto_store表id） 
    /// </summary> 
    /// <returns></returns> 
    public string store_id { get; set; }

    /// <summary> 
    /// 总部商品id
    /// </summary> 
    /// <returns></returns> 
    public string root_product_id { get; set; }
    /// <summary> 
    /// 商品编码
    /// </summary> 
    /// <returns></returns> 
    public string product_code { get; set; }
    /// <summary> 
    /// 商品自编码 
    /// </summary> 
    /// <returns></returns> 
    public string bar_code { get; set; }
    /// <summary> 
    /// 商品类型（pdt_type表id） 
    /// </summary> 
    /// <returns></returns> 
    public string type_id { get; set; }
    /// <summary> 
    /// 商品类型（book.图书、product.常规商品、pre_sale.预售商品、gift.赠品） 
    /// </summary> 
    /// <returns></returns> 
    public string goods_type { get; set; }
    /// <summary> 
    /// 商品名称 
    /// </summary> 
    /// <returns></returns> 
    public string product_name { get; set; }
    /// <summary> 
    /// 商品状态（固定值：0.上架、1.下架、2.回收站） 
    /// </summary> 
    /// <returns></returns> 
    public int? state { get; set; }

    /// <summary> 
    /// 部类（book_department表id） 
    /// </summary> 
    /// <returns></returns> 
    public string dpt_code { get; set; }
    /// <summary> 
    /// 品牌编号（pdt_brand表id） 
    /// </summary> 
    /// <returns></returns> 
    public string brand_code { get; set; }
    /// <summary> 
    /// 定价 
    /// </summary> 
    /// <returns></returns> 
    public decimal? make_price { get; set; }

    /// <summary> 
    /// 是否启用多SKU（1.启用、2.不启用） 
    /// </summary> 
    /// <returns></returns> 
    public int? has_sku { get; set; }
    /// <summary> 
    /// 税率 
    /// </summary> 
    /// <returns></returns> 
    public int? inctax { get; set; }
    /// <summary>
    /// 重量
    /// </summary>
    public double weight { get; set; } = 0.1;
    /// <summary>
    /// 商品简介
    /// </summary>
    public string product_des { get; set; }

    /// <summary>
    /// 是否同步
    /// </summary>
    public int is_sync { get; set; }

    /// <summary>
    /// 标记状态(select,add,edit)
    /// </summary>
    public string tag_status { get; set; }


    /// <summary>
    /// 店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店
    /// </summary>
    public int channel { get; set; }


}


/// <summary>
/// 商品扩展字段
/// </summary>
[SugarTable("PDT_PRODUCT_EXT")]
[Tenant("IPOS-PRODUCT")]
public class productExtEntity
{
    public string product_id { get; set; }
    /// <summary> 
    /// 商品ISBN条码 
    /// </summary> 
    /// <returns></returns> 
    public string isbn { get; set; }
    public string author { get; set; }
    public DateTime pub_date { get; set; }
    public string pub_name { get; set; }
    public string cls_code { get; set; }
    public string clc_code { get; set; }
    public string series { get; set; }
    public string translator { get; set; }
    public string edition { get; set; }
    public string print { get; set; }
    public string bksize { get; set; }
    public string bkbind { get; set; }
    public string words { get; set; }
    public string pages { get; set; }
    public string paper { get; set; }
    public string jp { get; set; }
    public string language { get; set; }
    public string pkamount { get; set; }
    public int pkqty { get; set; }
    public string pkunit { get; set; }
    public string shunt_book { get; set; }
    public string shunt_name { get; set; }
    public string publish_area { get; set; }
    public DateTime print_date { get; set; }
    public string reader { get; set; }
    public string chapters { get; set; }
    public string preface { get; set; }
    public string contents { get; set; }

}

