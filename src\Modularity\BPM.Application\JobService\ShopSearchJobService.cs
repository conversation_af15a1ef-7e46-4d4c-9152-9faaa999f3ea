using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;

namespace BPM.Application;

/// <summary>
/// 本地任务-搜索门店.
/// </summary>
[JobDetail("job_shop_search", Description = "搜索门店", GroupName = "BuiltIn", Concurrent = true)]
public class ShopSearchJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 门店服务.
    /// </summary>
    private readonly ShopService _shopService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public ShopSearchJobService(IServiceScopeFactory serviceScopeFactory, ShopService shopService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _shopService = shopService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _shopService.getShopList();
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 