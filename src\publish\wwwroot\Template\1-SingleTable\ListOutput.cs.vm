﻿namespace BPM.@(Model.NameSpace).Entitys.Dto.@(Model.ClassName);

/// <summary>
/// @(Model.BusName)输入参数.
/// </summary>
public class @(Model.ClassName)ListOutput
{
@foreach (var column in Model.TableField)
{
@if (column.PrimaryKey){
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}else if (column.IsShow)
{
switch(column.bpmKey)
{
case "datePicker":
case "createTime":
case "modifyTime":
case "switch":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @column.LowerColumnName { get; set; }
@:
break;
case "uploadFile":
case "uploadImg":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @column.LowerColumnName { get; set; }
@:
break;
case "relationForm":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_id { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "string" : column.NetType) @column.LowerColumnName { get; set; }
@:
break;
}
}
}
@foreach (var column in Model.TableField)
{
@switch(column.bpmKey)
{
case "treeSelect":
@if(column.IsTreeParentField && Model.TableType == 5)
{
@if(!column.IsShow)
{
    @:/// <summary>
    @:/// @(Model.BusName).
    @:/// </summary>
    @:public @column.NetType @(column.LowerColumnName) { get; set; }
@:
}
    @:/// <summary>
    @:/// @(Model.BusName)-父级ID.
    @:/// </summary>
    @:public @column.NetType @(column.LowerColumnName)_pid { get; set; }
@:
    @:/// <summary>
    @:/// @(Model.BusName)-子级.
    @:/// </summary>
    @:public List<@(Model.ClassName)ListOutput> children { get; set; }
@:
}
break;
}
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
    @:/// <summary>
    @:/// 流程任务ID.
    @:/// </summary>
    @:public string flowTaskId { get; set; }
@:
}
@if(Model.EnableFlow)
{
    @:/// <summary>
    @:/// 流程状态.
    @:/// </summary>
    @:public int? flowState { get; set; } = 0;
@:
    @:/// <summary>
    @:/// 流程引擎ID.
    @:/// </summary>
    @:public string flowId { get; set; }
@:
}
}