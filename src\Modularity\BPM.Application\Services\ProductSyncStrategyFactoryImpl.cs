using BPM.DependencyInjection;
using BPM.Domain.Entitys.Dto;
using BPM.Domain.Requests.product;
using Microsoft.Extensions.DependencyInjection;

namespace BPM.Application.Services;

/// <summary>
/// 商品同步策略工厂实现
/// </summary>
[SuppressSniffer]
public class ProductSyncStrategyFactoryImpl : ProductSyncStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;
    private List<IProductSyncStrategy> _strategies;

    public ProductSyncStrategyFactoryImpl(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 获取适合的策略
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>同步策略</returns>
    public override IProductSyncStrategy GetStrategy(getOnSaleProductRequest request)
    {
        // 延迟初始化策略列表，避免循环依赖
        if (_strategies == null)
        {
            InitializeStrategies();
        }

        return _strategies.FirstOrDefault(s => s.CanHandle(request))
               ?? throw new InvalidOperationException("没有找到适合的同步策略");
    }

    /// <summary>
    /// 初始化策略列表
    /// </summary>
    private void InitializeStrategies()
    {
        var productService = _serviceProvider.GetRequiredService<ProductService>();

        _strategies = new List<IProductSyncStrategy>
        {
            new TimeRangeSyncStrategy(productService.AutoDivideAndProcessByTime),
            new DirectSyncStrategy(productService.ProcessProducts)
        };
    }
}
