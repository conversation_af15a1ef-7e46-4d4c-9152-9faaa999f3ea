using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;

namespace BPM.Application;

/// <summary>
/// 本地任务-同步订单.
/// </summary>
[JobDetail("job_sync_order", Description = "同步订单", GroupName = "BuiltIn", Concurrent = true)]
public class OrderSyncJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 订单服务.
    /// </summary>
    private readonly OrderService _orderService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public OrderSyncJobService(IServiceScopeFactory serviceScopeFactory, OrderService orderService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _orderService = orderService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _orderService.GetTradesSold(new {});
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 