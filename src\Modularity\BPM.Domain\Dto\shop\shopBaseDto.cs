﻿namespace BPM.Business.Entitys.Dto.shop;

public class shopBaseDto
{

    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_code { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public string shop_name { get; set; }
    /// <summary>
    /// 省份
    /// </summary>
    public string province { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string city { get; set; }

    /// <summary>
    /// 区县
    /// </summary>
    public string area { get; set; }


    /// <summary>
    /// 精度
    /// </summary>
    public decimal? longitude { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string address { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public decimal? enabled { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public decimal? latitude { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string tel { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string email { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string intro { get; set; }
}

