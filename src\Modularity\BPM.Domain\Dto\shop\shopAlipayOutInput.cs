﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.shop;

/// <summary>
/// 门店支付宝输出.
/// </summary>
[SuppressSniffer]
public class shopAlipayOutInput
{

    /// <summary>
    /// 应用编号.
    /// </summary>
    public string app_id { get; set; }

    /// <summary>
    /// 商户私钥
    /// </summary>
    public string app_private_key { get; set; }

    /// <summary>
    /// 阿里公钥
    /// </summary>
    public string alipay_public_key { get; set; }

    /// <summary>
    /// 签名类型
    /// </summary>
    public string sign_type { get; set; }

}