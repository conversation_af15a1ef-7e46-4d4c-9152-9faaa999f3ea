﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.product;

/// <summary>
/// 门店商品库存跟新响应
/// </summary>
[SuppressSniffer]
public class shopProcuctStockResponse
{
    /// <summary>
    ///  错误码
    /// </summary>
    public int code { get; set; }

    /// <summary>
    ///  错误信息
    /// </summary>
    public string message { get; set; }

    /// <summary>
    /// 总店商品的skuId，如果操作的是无规格商品则为null
    /// </summary>
    public long sku_id { get; set; }

    /// <summary>
    /// 分店店铺id
    /// </summary>
    public long node_kdt_id { get; set; }

    /// <summary>
    ///是否更新成功
    /// </summary>
    public bool is_success { get; set; }
}
