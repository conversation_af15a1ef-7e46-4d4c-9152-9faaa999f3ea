﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.customer;

/// <summary>
/// 客户请求
/// </summary>
[SuppressSniffer]
public class createCustomerRequest
{
    /// <summary>
    ///  手机号
    /// </summary>
    public string mobile { get; set; }

    /// <summary>
    /// 客户基础信息
    /// </summary>
    public customer_info customer_create { get; set; }

    /// <summary>
    ///  scrm渠道类型
    /// </summary>
    public int scrm_channel_type { get; set; } = 2;

    /// <summary>
    /// 客户标识信息
    /// </summary>
    public customer_label_info label_info { get; set; }

    /// <summary>
    /// 国家码（默认为+86）
    /// </summary>
    public string country_code { get; set; } = "+86";

    /// <summary>
    /// 是否授权手机号 true-授权手机号 false-不授权手机号 不传默认为false（目前创建客户时授权手机号后不会计算客户等级，需调等级设置接口设置等级）
    /// </summary>
    public bool is_mobile_auth { get; set; } = false;

    /// <summary>
    /// 是否需要走扩展点，不传参数默认为true，true-走扩展点 false-不走扩展点 （其中扩展点为第三方创建客户）
    /// </summary>
    public bool is_do_ext_point { get; set; } = false;

    /// <summary>
    ///  用户创建时间(日期格式:yyyy-MM-dd HH:mm:ss)
    /// </summary>
    public string create_date { get; set; }
}


public class customer_info
{
    /// <summary>
    ///  微信
    /// </summary>
    public string wei_xin { get; set; }

    /// <summary>
    ///  性别，0：未知；1：男；2：女
    /// </summary>
    public int? gender { get; set; } = 0;

    /// <summary>
    /// 备注
    /// </summary>
    public string remark { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public string birthday { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 所属门店
    /// </summary>
    public long ascription_kdt_id { get; set; }
}


public class customer_label_info
{
    /// <summary>
    /// 客户来源方式（不传或0：其他，2008：系统打通）
    /// </summary>
    public int src_way { get; set; } = 2008;

    /// <summary>
    /// 客户来源渠道（不传或0：其他，2000：三方门店）
    /// </summary>
    public int src_channel { get; set; } = 2000;
}


public class CustomerPhoneRequest
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string phone { get; set; }
}