﻿using SqlSugar;

namespace BPM.Domain.Entity.order;

/// <summary>
/// 订单主表
/// </summary>
[SugarTable("ORDER")]
[Tenant("IPOS-ORDER")]
public class orderEntity
{
    /// <summary>
    /// 订单编号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string order_no { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string kdt_id { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public string kdt_name { get; set; }

    /// <summary>
    /// 客户编号
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 会员手机号
    /// </summary>
    public string buyer_phone { get; set; }

    /// <summary>
    /// 订单总金额
    /// </summary>
    public decimal total_fee { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string status { get; set; }

    /// <summary>
    /// 下单时间，格式:yyyy-MM-dd HH:mm:ss
    /// </summary>
    public string created { get; set; }

    /// <summary>
    /// 付款时间，格式:yyyy-MM-dd HH:mm:ss
    /// </summary>
    public string pay_time { get; set; }

    /// <summary>
    /// 付款方式
    /// </summary>
    public int pay_type { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    public int item_type { get; set; }

    /// <summary>
    /// 是否分销
    /// </summary>
    public bool is_fenxiao_order { get; set; }
}
