﻿using SqlSugar;

namespace BPM.Domain.Entity.product;


/// <summary>
/// 版 本 BPM敏捷开发框架
/// Copyright (c) 2018-2022 深圳市中畅源科技开发有限公司
/// 创建人：Aarons
/// 日 期：2022.03.21
/// 描 述：商品实体
/// </summary>
[SugarTable("PDT_SKU_STOCK")]
[Tenant("IPOS-PRODUCT")]
public class productStockEntity
{
    /// <summary> 
    /// 主键编号 
    /// </summary> 
    /// <returns></returns> 
    public string id { get; set; }
    /// <summary> 
    /// 商品编号（pdt_product表id） 
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "product_id", IsPrimaryKey = true)]
    public long product_id { get; set; }
    /// <summary> 
    /// 规格编号（pdt_sku表id） 
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "sku_id", IsPrimaryKey = true)]
    public long sku_id { get; set; }
    /// <summary> 
    /// 门店编号（sto_store表id） 
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "store_id", IsPrimaryKey = true)]
    public string store_id { get; set; }
    /// <summary> 
    /// 总收货库存 
    /// </summary> 
    /// <returns></returns> 
    public int? stock { get; set; }

    /// <summary> 
    ///  是否多规格 
    /// </summary> 
    /// <returns></returns> 
    public int? has_sku { get; set; }

    /// <summary> 
    /// 最后更新库存日期 
    /// </summary> 
    /// <returns></returns> 
    public DateTime? modify_date { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }

    /// <summary>
    /// 同步主体
    /// </summary>
    public string tag_body { get; set; }

    
}

