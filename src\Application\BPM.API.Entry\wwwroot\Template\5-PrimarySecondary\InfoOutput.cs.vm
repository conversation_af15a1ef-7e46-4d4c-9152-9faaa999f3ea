﻿@if(Model.IsUploading)
{
@:using BPM.Common.Models;
}
@foreach(var table in Model.TableRelations)
{
@*循环出子表的命名空间*@
@:using BPM.@(Model.NameSpace).Entitys.Dto.@(table.ClassName);
}

namespace BPM.@(Model.NameSpace).<EMAIL>;

/// <summary>
/// @(Model.BusName)输出参数.
/// </summary>
public class @(Model.ClassName)InfoOutput
{
@foreach (var column in Model.TableField)
{
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@if (column.PrimaryKey)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}
else if(column.bpmKey != null)
{
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> @(parameterName) { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "int" : "string") @(parameterName) { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<List<string>> @(parameterName) { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<@(column.NetType == "int?" ? "int" : "string")> @(parameterName) { get; set; }
@:
}
break;
case "checkbox":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<@(column.NetType == "int?" ? "int" : "string")> @(parameterName) { get; set; }
@:
break;
case "createTime":
case "modifyTime":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName) { get; set; }
@:
break;
case "radio":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "int" : "string") @(parameterName) { get; set; }
@:
break;
case "slider":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public int @(parameterName) { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<FileControlsModel> @(parameterName) { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(parameterName) { get; set; }
@:
break;
}
}
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
    @:/// <summary>
    @:/// 流程真实ID.
    @:/// </summary>
    @:public string flowTaskId { get; set; }
@:
}
@if(Model.ConcurrencyLock)
{
    @:/// <summary>
    @:/// 乐观锁.
    @:/// </summary>
    @:public string version { get; set; }
@:
}
@if(Model.EnableFlow || Model.Type == 3)
{
    @:/// <summary>
    @:/// 流程引擎ID.
    @:/// </summary>
    @:public string flowId { get; set; }
@:
}
@foreach (var table in Model.TableRelations)
{
    @:/// <summary>
    @:/// @(table.TableComment).
    @:/// </summary>
    @:public List<@(table.ClassName)InfoOutput> @table.ControlModel { get; set; }
@:
}
}