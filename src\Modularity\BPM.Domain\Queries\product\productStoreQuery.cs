﻿using BPM.Common.Filter;
using BPM.DependencyInjection;
namespace BPM.Domain.Queries.product;

/// <summary>
/// 商品库存查询参数
/// </summary>
[SuppressSniffer]
public class productStoreQuery : PageInputBase
{
    /// <summary>
    /// 标记状态
    /// </summary>
    public string tag_status { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string store_id { get; set; }

    /// <summary>
    /// 商品编号
    /// </summary>
    public long product_id { get; set; }
}
