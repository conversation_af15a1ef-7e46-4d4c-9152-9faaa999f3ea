﻿@if(Model.IsUploading)
{
@:using BPM.Common.Models;
@:
}
namespace BPM.@(@Model.NameSpace).<EMAIL>;
 
/// <summary>
/// @(@Model.BusName)输出参数.
/// </summary>
public class @(@Model.ClassName)InfoOutput
{
@foreach (var column in Model.TableField){
@if (column.PrimaryKey){
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}else if(column.bpmKey != null){
switch(column.bpmKey)
{
case "slider":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public int @column.LowerColumnName { get; set; }
@:
break;
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> @column.LowerColumnName { get; set; }
@:
}else{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int" ? "int" : "string") @column.LowerColumnName { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<List<string>> @column.LowerColumnName { get; set; }
@:
}else{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<@(column.NetType == "int" ? "int" : "string")> @column.LowerColumnName { get; set; }
@:
}
break;
case "checkbox":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<@(column.NetType == "int" ? "int" : "string")> @column.LowerColumnName { get; set; }
@:
break;
case "createTime":
case "modifyTime":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @column.LowerColumnName { get; set; }
@:
break;
case "radio":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int" ? "int" : "string") @column.LowerColumnName { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<FileControlsModel> @column.LowerColumnName { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
break;
}
}
}
}