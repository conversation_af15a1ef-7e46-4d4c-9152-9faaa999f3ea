﻿using BPM.DependencyInjection;
using BPM.Domain.Requests.customer;

namespace BPM.Domain.Dto.trade;

/// <summary>
/// 交易基础信息结构体.
/// </summary>
[SuppressSniffer]
public class FullOrderInfo
{
    /// <summary>
    /// 订单收货地址信息结构体.
    /// </summary>
    public AddressInfo address_info { get; set; }

    /// <summary>
    /// 订单买家信息结构体.
    /// </summary>
    public BuyerInfo buyer_info { get; set; }

    /// <summary>
    /// 交易送礼子单.
    /// </summary>
    public ChildInfo child_info { get; set; }

    /// <summary>
    /// 订单明细结构体.
    /// </summary>
    public List<Orders> orders { get; set; }

    /// <summary>
    /// 订单明细结构体.
    /// </summary>
    public OrderInfo order_info { get; set; }

    /// <summary>
    /// 交易支付信息结构体.
    /// </summary>
    public PayInfo pay_info { get; set; }

    /// <summary>
    /// 订单标记信息结构体.
    /// </summary>
    public RemarkInfo remark_info { get; set; }

    /// <summary>
    /// 订单来源.
    /// </summary>
    public SourceInfo source_info { get; set; }
}

public class AddressInfo
{
    /// <summary>
    /// 地址扩展信息经纬度信息和省编码.
    /// </summary>
    public string address_extra { get; set; }

    /// <summary>
    /// 详细地址.
    /// </summary>
    public string delivery_address { get; set; }

    /// <summary>
    /// 市.
    /// </summary>
    public string delivery_city { get; set; }

    /// <summary>
    /// 区.
    /// </summary>
    public string delivery_district { get; set; }

    /// <summary>
    /// 同城送预计送达时间-结束时间.
    /// </summary>
    public string delivery_end_time { get; set; }

    /// <summary>
    /// 邮政编码.
    /// </summary>
    public string delivery_postal_code { get; set; }

    /// <summary>
    /// 省.
    /// </summary>
    public string delivery_province { get; set; }

    /// <summary>
    /// 同城送预计送达时间-开始时间.
    /// </summary>
    public string delivery_start_time { get; set; }

    /// <summary>
    /// 收货人姓名.
    /// </summary>
    public string receiver_name { get; set; }

    /// <summary>
    /// 收货人手机号.
    /// </summary>
    public string receiver_tel { get; set; }

    /// <summary>
    /// 到店自提信息 json格式.
    /// </summary>
    public string self_fetch_info { get; set; }
}

/// <summary>
/// 订单买家信息结构体.
/// </summary>
public class BuyerInfo
{
    /// <summary>
    /// 有赞用户id.
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 买家手机号.
    /// </summary>
    public string buyer_phone { get; set; }

    /// <summary>
    /// 微信H5和微信小程序用户id.
    /// </summary>
    public string outer_user_id { get; set; }
}

/// <summary>
/// 交易送礼子单.
/// </summary>
public class ChildInfo
{
    /// <summary>
    /// 送礼编号.
    /// </summary>
    public string gift_no { get; set; }

    /// <summary>
    /// 送礼标记.
    /// </summary>
    public string gift_sign { get; set; }
}

/// <summary>
/// 订单明细结构体.
/// </summary>
public class Orders
{
    /// <summary>
    /// 商品别名.
    /// </summary>
    public string alias { get; set; }

    /// <summary>
    /// 商品留言.
    /// </summary>
    public string buyer_messages { get; set; }

    /// <summary>
    /// 海淘商品贸易模式.
    /// </summary>
    public string cross_border_trade_mode { get; set; }

    /// <summary>
    /// 海淘口岸编码.
    /// </summary>
    public string customs_code { get; set; }

    /// <summary>
    /// 非现金抵扣金额.
    /// </summary>
    public string discount { get; set; }

    /// <summary>
    /// 单商品现价，减去了商品的优惠金额.
    /// </summary>
    public string discount_price { get; set; }

    /// <summary>
    /// 分销非现金抵扣金额.
    /// </summary>
    public string fenxiao_discount { get; set; }

    /// <summary>
    /// 分销商品金额.
    /// </summary>
    public string fenxiao_discount_price { get; set; }

    /// <summary>
    /// 分销运杂费.
    /// </summary>
    public string fenxiao_freight { get; set; }

    /// <summary>
    /// 分销实付金额.
    /// </summary>
    public string fenxiao_payment { get; set; }

    /// <summary>
    /// 分销价格.
    /// </summary>
    public string fenxiao_price { get; set; }

    /// <summary>
    /// 分销税费.
    /// </summary>
    public string fenxiao_tax_total { get; set; }

    /// <summary>
    /// 运杂费.
    /// </summary>
    public string freight { get; set; }

    /// <summary>
    /// 商品详情链接.
    /// </summary>
    public string goods_url { get; set; }

    /// <summary>
    /// 是否海淘订单, 1是海淘.
    /// </summary>
    public string is_cross_border { get; set; }

    /// <summary>
    /// 是否赠品.
    /// </summary>
    public string is_present { get; set; }

    /// <summary>
    /// 商品条码.
    /// </summary>
    public string item_barcode { get; set; }

    /// <summary>
    /// 商品id.
    /// </summary>
    public string item_id { get; set; }

    /// <summary>
    /// 商品留言.
    /// </summary>
    public string item_message { get; set; }

    /// <summary>
    /// 商品编码.
    /// </summary>
    public string item_no { get; set; }

    /// <summary>
    /// 订单类型 0:普通类型商品.
    /// </summary>
    public int item_type { get; set; }

    /// <summary>
    /// 商品数量.
    /// </summary>
    public int num { get; set; }

    /// <summary>
    /// 订单明细id.
    /// </summary>
    public string oid { get; set; }

    /// <summary>
    /// 商品编码.
    /// </summary>
    public string outer_item_id { get; set; }

    /// <summary>
    /// 商品规格编码.
    /// </summary>
    public string outer_sku_id { get; set; }

    /// <summary>
    /// 商品最终均摊价.
    /// </summary>
    public string payment { get; set; }

    /// <summary>
    /// 商品图片地址.
    /// </summary>
    public string pic_path { get; set; }

    /// <summary>
    /// 商品积分价.
    /// </summary>
    public string points_price { get; set; }

    /// <summary>
    /// 单商品原价.
    /// </summary>
    public string price { get; set; }

    /// <summary>
    /// 规格条码.
    /// </summary>
    public string sku_barcode { get; set; }

    /// <summary>
    /// 商品规格id.
    /// </summary>
    public string sku_id { get; set; }

    /// <summary>
    /// 规格编码.
    /// </summary>
    public string sku_no { get; set; }

    /// <summary>
    /// 规格信息（无规格商品为空）.
    /// </summary>
    public string sku_properties_name { get; set; }

    /// <summary>
    /// 报关单号.
    /// </summary>
    public string sub_order_no { get; set; }

    /// <summary>
    /// 税费.
    /// </summary>
    public string tax_total { get; set; }

    /// <summary>
    /// 商品名称.
    /// </summary>
    public string title { get; set; }

    /// <summary>
    /// 商品优惠后总价.
    /// </summary>
    public string total_fee { get; set; }
}

/// <summary>
/// 交易明细详情.
/// </summary>
public class OrderInfo
{
    /// <summary>
    /// 关闭类型 0:未关闭.
    /// </summary>
    public int close_type { get; set; }

    /// <summary>
    /// 订单确认时间.
    /// </summary>
    public string confirm_time { get; set; }

    /// <summary>
    /// 订单发货时间.
    /// </summary>
    public string consign_time { get; set; }

    /// <summary>
    /// 订单创建时间.
    /// </summary>
    public string created { get; set; }

    /// <summary>
    /// 订单过期时间.
    /// </summary>
    public string expired_time { get; set; }

    /// <summary>
    /// 物流类型 0:快递发货; 1:到店自提; 2:同城配送; 9:无需发货.
    /// </summary>
    public int express_type { get; set; }

    /// <summary>
    /// 是否零售订单.
    /// </summary>
    public bool is_retail_order { get; set; }

    /// <summary>
    /// 网点id.
    /// </summary>
    public int offline_id { get; set; }

    /// <summary>
    /// 订单扩展字段.
    /// </summary>
    public OrderExtra order_extra { get; set; }

    /// <summary>
    /// 订单信息打标.
    /// </summary>
    public OrderTags order_tags { get; set; }

    /// <summary>
    /// 订单支付时间.
    /// </summary>
    public string pay_time { get; set; }

    /// <summary>
    /// 支付类型.
    /// </summary>
    public int pay_type { get; set; }

    /// <summary>
    /// 退款状态 0:未退款.
    /// </summary>
    public int refund_state { get; set; }

    /// <summary>
    /// 主订单状态.
    /// </summary>
    public string status { get; set; }

    /// <summary>
    /// 主订单状态描述.
    /// </summary>
    public string status_str { get; set; }

    /// <summary>
    /// 订单成功时间.
    /// </summary>
    public string success_time { get; set; }

    /// <summary>
    /// 店铺类型.
    /// </summary>
    public int team_type { get; set; }

    /// <summary>
    /// 订单号.
    /// </summary>
    public string tid { get; set; }

    /// <summary>
    /// 主订单类型.
    /// </summary>
    public int type { get; set; }

    /// <summary>
    /// 订单更新时间.
    /// </summary>
    public string update_time { get; set; }
}

/// <summary>
/// 订单扩展字段.
/// </summary>
public class OrderExtra
{
    /// <summary>
    /// 收银员id.
    /// </summary>
    public string cashier_id { get; set; }

    /// <summary>
    /// 收银员名字.
    /// </summary>
    public string cashier_name { get; set; }

    /// <summary>
    /// 下单设备号.
    /// </summary>
    public string create_device_id { get; set; }

    /// <summary>
    /// 美业分店id.
    /// </summary>
    public string dept_id { get; set; }

    /// <summary>
    /// 分销单外部支付流水号.
    /// </summary>
    public string fx_inner_transaction_no { get; set; }

    /// <summary>
    /// 分销店铺id.
    /// </summary>
    public string fx_kdt_id { get; set; }

    /// <summary>
    /// 分销单订单号.
    /// </summary>
    public string fx_order_no { get; set; }

    /// <summary>
    /// 分销单内部支付流水号.
    /// </summary>
    public string fx_outer_transaction_no { get; set; }

    /// <summary>
    /// 海淘身份证信息.
    /// </summary>
    public string id_card_name { get; set; }

    /// <summary>
    /// 海淘身份证信息.
    /// </summary>
    public string id_card_number { get; set; }

    /// <summary>
    /// 发票抬头.
    /// </summary>
    public string invoice_title { get; set; }

    /// <summary>
    /// 是否来自购物车.
    /// </summary>
    public string is_from_cart { get; set; }

    /// <summary>
    /// 是否父单.
    /// </summary>
    public string is_parent_order { get; set; }

    /// <summary>
    /// 是否是积分订单.
    /// </summary>
    public string is_points_order { get; set; }

    /// <summary>
    /// 是否子单.
    /// </summary>
    public string is_sub_order { get; set; }

    /// <summary>
    /// ISV打标信息.
    /// </summary>
    public string merchant_customized_special_order { get; set; }

    /// <summary>
    /// 父单号.
    /// </summary>
    public string parent_order_no { get; set; }

    /// <summary>
    /// 采购单号.
    /// </summary>
    public string purchase_order_no { get; set; }

    /// <summary>
    /// 零售特有字段.
    /// </summary>
    public string retail_pick_up_code { get; set; }

    /// <summary>
    /// 零售特有字段.
    /// </summary>
    public string retail_site_no { get; set; }

    /// <summary>
    /// 结算时间.
    /// </summary>
    public string settle_time { get; set; }
}

/// <summary>
/// 订单信息打标.
/// </summary>
public class OrderTags
{
    /// <summary>
    /// 是否有维权.
    /// </summary>
    public bool is_feedback { get; set; }

    /// <summary>
    /// 是否分销单.
    /// </summary>
    public bool is_fenxiao_order { get; set; }

    /// <summary>
    /// 是否会员订单.
    /// </summary>
    public bool is_member { get; set; }

    /// <summary>
    /// 是否多门店订单.
    /// </summary>
    public bool is_multi_store { get; set; }

    /// <summary>
    /// 是否线下订单.
    /// </summary>
    public bool is_offline_order { get; set; }

    /// <summary>
    /// 是否支付.
    /// </summary>
    public bool is_payed { get; set; }

    /// <summary>
    /// 是否享受免邮.
    /// </summary>
    public bool is_postage_free { get; set; }

    /// <summary>
    /// 是否采购单.
    /// </summary>
    public bool is_purchase_order { get; set; }

    /// <summary>
    /// 是否有退款.
    /// </summary>
    public bool is_refund { get; set; }

    /// <summary>
    /// 是否担保交易.
    /// </summary>
    public bool is_secured_transactions { get; set; }

    /// <summary>
    /// 是否结算.
    /// </summary>
    public bool is_settle { get; set; }

    /// <summary>
    /// 是否虚拟订单.
    /// </summary>
    public bool is_virtual { get; set; }
}

/// <summary>
/// 交易支付信息结构体.
/// </summary>
public class PayInfo
{
    /// <summary>
    /// 外部支付单号.
    /// </summary>
    public List<string> outer_transactions { get; set; }

    /// <summary>
    /// 最终支付价格.
    /// </summary>
    public string payment { get; set; }

    /// <summary>
    /// 邮费.
    /// </summary>
    public string post_fee { get; set; }

    /// <summary>
    /// 优惠前商品总价.
    /// </summary>
    public string total_fee { get; set; }

    /// <summary>
    /// 有赞支付流水号.
    /// </summary>
    public List<string> transaction { get; set; }
}

/// <summary>
/// 订单标记信息结构体.
/// </summary>
public class RemarkInfo
{
    /// <summary>
    /// 订单买家留言.
    /// </summary>
    public string buyer_message { get; set; }

    /// <summary>
    /// 订单标星等级 0-5.
    /// </summary>
    public int star { get; set; }

    /// <summary>
    /// 订单商家备注.
    /// </summary>
    public string trade_memo { get; set; }
}

/// <summary>
/// 订单来源.
/// </summary>
public class SourceInfo
{
    /// <summary>
    /// 下单唯一标识.
    /// </summary>
    public string book_key { get; set; }

    /// <summary>
    /// 是否来自线下订单.
    /// </summary>
    public bool is_offline_order { get; set; }

    /// <summary>
    /// 订单标记.
    /// </summary>
    public string order_mark { get; set; }

    /// <summary>
    /// 订单来源平台.
    /// </summary>
    public Source source { get; set; }
}

/// <summary>
/// 订单来源平台.
/// </summary>
public class Source
{
    /// <summary>
    /// 平台 wx:微信.
    /// </summary>
    public string platform { get; set; }

    /// <summary>
    /// 微信平台细分.
    /// </summary>
    public string wx_entrance { get; set; }
}
