﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 客户实体
/// </summary>
[SugarTable("CUSTOMER")]
[Tenant("IPOS-CRM")]
public class customerEntity
{
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string store_id { get; set; }

    /// <summary>
    ///  客户编号
    /// </summary>

    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string customer_sn { get; set; }


    /// <summary>
    /// 卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string phone { get; set; }
    /// <summary>
    /// 昵称
    /// </summary>
    public string nick_name { get; set; }
    /// <summary>
    /// 名称
    /// </summary>
    public string real_name { get; set; }
    /// <summary>
    /// 邮箱
    /// </summary>
    public string email { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? birth_day { get; set; }

    /// <summary>
    ///  性别
    /// </summary>
    public int? gender { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? state { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public string source { get; set; }

    /// <summary>
    /// 图像
    /// </summary>
    public string avatar { get; set; }

    /// <summary>
    /// openId
    /// </summary>
    public string open_id { get; set; }

    /// <summary>
    /// unionId
    /// </summary>
    public string union_id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_date { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime modify_date { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string remark { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }

    /// <summary>
    /// 同步内容
    /// </summary>
    public string tag_body { get; set; }

    /// <summary>
    /// 原手机号
    /// </summary>
    public string org_phone { get; set; }

    /// <summary>
    /// 外部来源id
    /// </summary>
    public string request_id { get; set; }

    /// <summary>
    /// mall同步标记
    /// </summary>
    public string tag_mall_status { get; set; }

    /// <summary>
    /// 有赞版本号
    /// </summary>
    public long? yzVersion { get; set; }




}

