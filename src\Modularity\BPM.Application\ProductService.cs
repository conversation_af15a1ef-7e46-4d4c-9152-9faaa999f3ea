using BPM.Application.Configuration;
using BPM.Application.DTOs;
using BPM.Application.Services;
using BPM.Common.Manager;
using BPM.Domain.Entity.product;
using BPM.Domain.Entity.shop;
using BPM.Domain.Entitys.Dto;
using BPM.Domain.Queries.product;
using BPM.Domain.Requests.product;
using BPM.Domain.Requests.subscriber;
using BPM.Domain.Responses.product;
using BPM.Extras.Youzan.Dto;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Result;
using BPM.Extras.Youzan.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using SqlSugar;
using SqlSugar.Extensions;
using System.Collections.Concurrent;
using System.Globalization;
using System.Text;
using static BPM.Domain.Requests.product.updateProductStoreRequest;
using static BPM.Domain.Requests.product.updateProductStoreRequest.multiSkuList;

namespace BPM.Application;

/// <summary>
/// 产品管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11
/// </summary>
[ApiDescriptionSettings(Tag = "产品管理", Name = "Product", Order = 600)]
[Route("api/[controller]")]
public class ProductService : IDynamicApiController, ITransient
{
    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly SqlSugarProvider _repository;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 缓存管理器
    /// </summary>
    private readonly ICacheManager _cache;

    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    private readonly ShopCacheService _shopCacheService;
    private readonly ProductSyncConfiguration _config;
    private readonly ProductSyncResultHandler _resultHandler;
    private readonly ProductBatchProcessor _batchProcessor;
    private readonly ProductCompensationService _compensationService;
    private ProductSyncStrategyFactory? _strategyFactory;

    #region 常量定义

    // 时间相关常量
    private const long MINUTE = 60 * 1000;      // 1分钟的毫秒数
    private const long HOUR = 60 * MINUTE;      // 1小时的毫秒数
    private const long DAY = 24 * HOUR;         // 1天的毫秒数

    // 数据处理相关常量
    private const int MIN_DATA_THRESHOLD = 200;  // 最小数据量阈值
    private const int MAX_RETRIES_SMALL = 5;     // 小数据量最大重试次数
    private const int MAX_RETRIES_MEDIUM = 3;    // 中等数据量最大重试次数
    private const int MAX_BATCH_SIZE = 20;       // 最大批处理大小
    private const int BATCH_SIZE = 500;          // 数据库批处理大小

    // 兼容性常量（使用配置值）
    private int MAX_PAGE_SIZE => _config?.MaxPageSize ?? 200;
    private int MAX_DATA_THRESHOLD => _config?.MaxDataThreshold ?? 4000;
    private int MAX_PAGE_NO => _config?.MaxPageNo ?? 100;

    #endregion

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="youzanService">有赞服务</param>
    /// <param name="cache">缓存管理器</param>
    /// <param name="shopCacheService">店铺缓存服务</param>
    /// <param name="configOptions">商品同步配置选项</param>
    /// <param name="resultHandler">结果处理器</param>
    /// <param name="batchProcessor">批量处理器</param>
    /// <param name="compensationService">补偿服务</param>
    public ProductService(
        ISqlSugarClient context,
        IYouzanService youzanService,
        ICacheManager cache,
        ShopCacheService shopCacheService,
        IOptions<ProductSyncConfiguration> configOptions,
        ProductSyncResultHandler resultHandler,
        ProductBatchProcessor batchProcessor,
        ProductCompensationService compensationService)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<productEntity>();
        _youzanService = youzanService;
        _cache = cache;
        _db = _repository.AsTenant();
        _shopCacheService = shopCacheService;
        _config = configOptions.Value;
        _resultHandler = resultHandler;
        _batchProcessor = batchProcessor;
        _compensationService = compensationService;

        // 策略工厂将在第一次使用时延迟初始化
        _strategyFactory = null;
    }

    // 兼容性属性（保持向后兼容）
    public int succeed => _resultHandler?.Succeed ?? 0;
    public int fail => _resultHandler?.Fail ?? 0;

    /// <summary>
    /// 获取策略工厂（延迟初始化）
    /// </summary>
    private ProductSyncStrategyFactory GetStrategyFactory()
    {
        if (_strategyFactory == null)
        {
            // 创建一个简单的服务提供者来避免循环依赖
            var serviceCollection = new Microsoft.Extensions.DependencyInjection.ServiceCollection();
            serviceCollection.AddSingleton(this);
            var serviceProvider = serviceCollection.BuildServiceProvider();

            _strategyFactory = new ProductSyncStrategyFactoryImpl(serviceProvider);
        }
        return _strategyFactory;
    }

    /// <summary>
    /// 获取出售中的商品列表
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("getOnSale")]
    public async Task<dynamic> getOnSaleProduct([FromBody] getOnSaleProductRequest request)
    {

        try
        {
            // 重置结果处理器
            _resultHandler.Reset();

            // 1. 参数预处理
            await PreprocessRequestParameters(request);

            // 2. 选择并执行同步策略
            var strategy = GetStrategyFactory().GetStrategy(request);
            var syncResult = await strategy.SyncProductsAsync(request, _resultHandler);

            // 3. 合并同步结果
            _resultHandler.MergeResult(syncResult);

            // 4. 执行补偿逻辑（根据请求类型决定是否执行）
            if (_compensationService.ShouldExecuteCompensation(request))
            {
                await _compensationService.ExecuteCompensationAsync(async req => await ProcessProducts(req));
            }

            // 5. 返回最终结果
            return _resultHandler.GetFinalResult();
        }
        catch (Exception ex)
        {
            Log.Error($"获取出售中的商品列表时发生错误: {ex.Message}");
            _resultHandler.AddError($"系统错误: {ex.Message}");
            _resultHandler.AddFail(1);
            return _resultHandler.GetFinalResult();
        }
    }

    /// <summary>
    /// 预处理请求参数
    /// </summary>
    /// <param name="request">请求参数</param>
    private async Task PreprocessRequestParameters(getOnSaleProductRequest request)
    {
        // 1. 处理时间格式转换
        ProcessTimeParameters(request);

        // 2. 设置默认时间范围
        await SetDefaultTimeRangeIfNeeded(request);

        // 3. 验证参数有效性
        ValidateRequestParameters(request);
    }

    /// <summary>
    /// 处理时间参数格式转换
    /// </summary>
    /// <param name="request">请求参数</param>
    private void ProcessTimeParameters(getOnSaleProductRequest request)
    {
        // 定义支持的时间格式
        string[] supportedFormats = {
            "yyyy-M-d", "yyyy-MM-dd", "yyyy/M/d", "yyyy/MM/dd", // 仅日期
            "yyyy-M-d H:mm", "yyyy-MM-dd HH:mm", "yyyy/M/d H:mm", "yyyy/MM/dd HH:mm" // 带时间
        };

        // 处理开始时间
        if (!string.IsNullOrEmpty(request.update_time_start_str))
        {
            request.update_time_start = ParseTimeParameter(request.update_time_start_str, supportedFormats, "update_time_start");
        }

        // 处理结束时间
        if (!string.IsNullOrEmpty(request.update_time_end_str))
        {
            request.update_time_end = ParseTimeParameter(request.update_time_end_str, supportedFormats, "update_time_end");
        }
    }

    /// <summary>
    /// 解析时间参数
    /// </summary>
    /// <param name="timeStr">时间字符串</param>
    /// <param name="supportedFormats">支持的格式</param>
    /// <param name="paramName">参数名称</param>
    /// <returns>Unix时间戳</returns>
    private long ParseTimeParameter(string timeStr, string[] supportedFormats, string paramName)
    {
        // 尝试解析为数字格式（时间戳）
        if (long.TryParse(timeStr, out long timestamp))
        {
            return timestamp;
        }

        // 尝试解析为日期字符串格式
        if (DateTime.TryParseExact(timeStr, supportedFormats,
            System.Globalization.CultureInfo.InvariantCulture,
            System.Globalization.DateTimeStyles.None, out DateTime dateTime))
        {
            return ConvertToUnixTimestamp(dateTime);
        }

        throw new ArgumentException($"无效的 {paramName} 格式：{timeStr}。支持格式：时间戳或 yyyy-MM-dd [HH:mm]");
    }

    /// <summary>
    /// 验证请求参数有效性
    /// </summary>
    /// <param name="request">请求参数</param>
    private void ValidateRequestParameters(getOnSaleProductRequest request)
    {
        // 验证时间范围
        if (request.update_time_start.HasValue && request.update_time_end.HasValue &&
            request.update_time_start.Value > request.update_time_end.Value)
        {
            throw new ArgumentException("开始时间不能大于结束时间");
        }

        // 验证页面参数
        if (request.page_size <= 0 || request.page_size > _config.MaxPageSize)
        {
            request.page_size = _config.MaxPageSize;
            Log.Information($"页面大小超出限制，已调整为: {request.page_size}");
        }
    }

    /// <summary>
    /// 如果需要，设置默认时间范围
    /// </summary>
    /// <param name="request">请求参数</param>
    private Task SetDefaultTimeRangeIfNeeded(getOnSaleProductRequest request)
    {
        // 检查是否有时间参数
        bool hasTimeParams = HasTimeParameters(request);
        bool hasQueryParam = !string.IsNullOrEmpty(request.q);

        // 如果只传了q参数而没有传时间参数，则不设置默认时间范围
        if (hasQueryParam && !hasTimeParams)
        {
            Log.Information($"仅查询参数模式 - q={request.q}，跳过默认时间范围设置");
            return Task.CompletedTask;
        }

        // 设置默认开始时间（5天前的0点）
        if (!request.update_time_start.HasValue || request.update_time_start.Value == 0)
        {
            var defaultStartTime = DateTime.UtcNow.Date.AddDays(-_config.DefaultTimeRangeDays);
            request.update_time_start = ((DateTimeOffset)defaultStartTime).ToUnixTimeMilliseconds();

            if (_config.EnableDetailedLogging)
            {
                Log.Information($"设置默认开始时间: {ConvertFromUnixTimestamp(request.update_time_start.Value)}");
            }
        }

        // 设置默认结束时间（当前时间）
        if (!request.update_time_end.HasValue || request.update_time_end.Value == 0)
        {
            request.update_time_end = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            if (_config.EnableDetailedLogging)
            {
                Log.Information($"设置默认结束时间: {ConvertFromUnixTimestamp(request.update_time_end.Value)}");
            }
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 检查是否有时间参数
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>是否有时间参数</returns>
    private bool HasTimeParameters(getOnSaleProductRequest request)
    {
        return !string.IsNullOrEmpty(request.update_time_start_str) ||
               !string.IsNullOrEmpty(request.update_time_end_str) ||
               (request.update_time_start.HasValue && request.update_time_start.Value != 0) ||
               (request.update_time_end.HasValue && request.update_time_end.Value != 0);
    }

    // 封装原有逻辑为一个独立方法
    public async Task<resultLogDto> ProcessProducts(getOnSaleProductRequest request)
    {
        Log.Information($"开始处理商品数据 - channel={request.channel}, q={request.q}, order_by={request.order_by}");

        request.page_no = 1;
        request.page_size = MAX_PAGE_SIZE;

        try
        {
            var token = await _youzanService.GetTokenAsync();
            if (_config.EnableDetailedLogging)
            {
                Log.Information($"获取有赞Token成功，开始处理商品数据");
            }

            var localSucceed = 0;
            var localFail = 0;
            var errorData = new List<string>();

            // 创建集合用于批量插入
            var productList = new List<productEntity>();
            var allProductSkuList = new List<productSkuEntity>();

            // 创建批量获取SKU的数据结构
            var pendingItems = new List<dynamic>(); // 待处理的商品对象
            var maxBatchSize = _config.BatchSize; // 每批处理的最大商品数

            if (_config.EnableDetailedLogging)
            {
                Log.Information($"初始化完成 - 页大小: {request.page_size}, 批处理大小: {maxBatchSize}");
            }

            while (true)
            {
                if (_config.EnableDetailedLogging)
                {
                    Log.Information($"开始处理第 {request.page_no} 页数据");
                }

                var param = new YouzanParameter();
                param.url = "youzan.items.onsale.get/3.0.0";
                param.method = "POST";

                // 确保请求对象中所有必要属性不为空
                if (request.q == null) request.q = string.Empty;

                // 尝试将请求对象序列化为JSON
                try
                {
                    param.body = request.ToJsonString();
                    if (_config.EnableDetailedLogging)
                    {
                        Log.Information($"请求参数序列化成功，准备调用有赞API");
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning($"序列化请求对象时出错: {ex.Message}");
                    var errorMsg = $"处理请求数据时序列化失败";
                    errorData.Add(errorMsg);
                    localFail++;
                    break;
                }

                var res = await _youzanService.GetData(param);
                if (_config.EnableDetailedLogging)
                {
                    Log.Information($"有赞API调用完成 - 成功: {res.success}");
                }

                if (!res.success)
                {
                    Log.Warning($"有赞API调用失败，结束处理");
                    break;
                }

                var result = res.data.ToObject<onSaleProcuctResponse>();
                var total = result.count;

                Log.Information($"处理页码: {request.page_no}, 页大小: {request.page_size}, 总数: {total}, 当前页商品数: {result.items?.Count ?? 0}");

                // 第一步：预处理所有符合条件的商品
                if (result.items == null || result.items.Count == 0)
                {
                    Log.Information("当前页没有商品数据");
                    continue;
                }

                var itemIds = result.items.Where(x => x.item_type == 0).Select(x => x.item_id).ToList();
                Log.Information($"筛选出符合条件的商品ID数量: {itemIds.Count} (item_type=0)");

                var existingProducts = await _repository.Queryable<productEntity>()
                    .Where(x => itemIds.Contains(x.product_id))
                    .ToListAsync();
                Log.Information($"查询到已存在的商品数量: {existingProducts.Count}");

                // 批量写入缓存 - 使用智能缓存时间
                if (existingProducts.Count > 0)
                {
                    var cacheExpiration = GetOptimalCacheTime(existingProducts.First());
                    var cacheTasks = existingProducts.Select(async product =>
                    {
                        var cacheKey = $"product:{product.product_id}";
                        await _cache.SetAsync(cacheKey, product, cacheExpiration);
                    });

                    await Task.WhenAll(cacheTasks);

                    if (_config.EnableDetailedLogging)
                    {
                        Log.Information($"已将 {existingProducts.Count} 个商品写入缓存，过期时间: {cacheExpiration.TotalMinutes}分钟");
                    }
                }

                // 收集待处理的商品
                Log.Information($"开始收集待处理商品，当前页商品总数: {result.items.Count}");
                foreach (var item in result.items)
                {
                    try
                    {
                        if (item.item_type != 0)
                        {
                            Log.Information($"跳过非普通商品 - barcode: {item.barcode}, item_id: {item.item_id}, item_type: {item.item_type}");
                            localFail++;
                            continue;
                        }

                        // 查询商品是否存在 - 直接从缓存获取
                        var cacheKey = $"product:{item.item_id}";
                        var product_info = await _cache.GetAsync<productEntity>(cacheKey);

                        if (product_info != null)
                        {
                            Log.Information($"商品已存在 - barcode: {item.barcode}, item_id: {item.item_id}, 缓存修改时间: {product_info.modify_date}, 有赞更新时间: {item.update_time}");

                            if (product_info.modify_date < item.update_time)
                            {
                                Log.Information($"商品需要更新 - barcode: {item.barcode}, item_id: {item.item_id}, 开始删除旧数据");
                                var deleteResult = await removeProduct(product_info.product_id);
                                if (deleteResult)
                                {
                                    await _cache.DelAsync($"product:{product_info.product_id}");
                                    Log.Information($"成功删除商品和缓存 - barcode: {item.barcode}, item_id: {item.item_id}, product_id: {product_info.product_id}");

                                    // 移除跨渠道删除逻辑，让每个商品根据自己的时间对比独立处理
                                    // 这样可以避免不必要的删除操作，提高处理效率
                                }
                                else
                                {
                                    // 删除失败，记录错误但继续处理
                                    Log.Warning($"删除商品失败，但继续处理流程 - barcode: {item.barcode}, item_id: {item.item_id}, product_id: {product_info.product_id}");
                                }
                            }
                            else
                            {
                                Log.Information($"商品无需更新，跳过 - barcode: {item.barcode}, item_id: {item.item_id}, title: {item.title}");
                                localFail++;
                                errorData.Add($"当前记录存在:{item.barcode} {item.title}");
                                continue;
                            }
                        }
                        else
                        {
                            Log.Information($"新商品，准备处理 - barcode: {item.barcode}, item_id: {item.item_id}, title: {item.title}");
                        }

                        // 创建新商品实体
                        var product = new productEntity()
                        {
                            product_id = item.item_id,
                            bar_code = item.barcode,
                            product_name = item.title,
                            make_price = item.price,
                            created_user_id = "yz",
                            product_code = item.item_no,
                            root_product_id = item.root_item_id,
                            state = 1,
                            channel = item.channel,
                            store_id = item.root_kdt_id,
                            created_date = item.created_time,
                            modify_date =  item.update_time,
                            goods_type = item.item_type.ToString()
                        };

                        // 收集待处理的商品信息
                        pendingItems.Add(new { Item = item, Product = product });
                        Log.Information($"商品已加入待处理队列 - barcode: {item.barcode}, item_id: {item.item_id}, 当前队列长度: {pendingItems.Count}");
                    }
                    catch (Exception ex)
                    {
                        Log.Warning($"预处理商品时出错 - barcode: {item.barcode}, item_id: {item.item_id}, 错误: {ex.Message}");
                        localFail++;
                        errorData.Add($"处理商品 {item.title} 出错: {ex.Message}");
                    }
                }

                // 第二步：批量处理收集到的商品
                if (pendingItems.Count > 0)
                {
                    Log.Information($"开始批量处理商品，总数: {pendingItems.Count}, 批大小: {maxBatchSize}");

                    // 按批次处理待处理商品
                    for (int i = 0; i < pendingItems.Count; i += maxBatchSize)
                    {
                        try
                        {
                            // 获取当前批次 - 使用 GetRange 代替 Skip/Take 提高性能
                            int currentBatchSize = Math.Min(maxBatchSize, pendingItems.Count - i);
                            var batch = pendingItems.GetRange(i, currentBatchSize);

                            Log.Information($"批量处理第 {i / maxBatchSize + 1} 批商品，数量: {batch.Count}");

                            // 根据channel分组商品
                            var channelGroups = batch.GroupBy(x => x.Item.channel).ToList();
                            Log.Information($"当前批次按channel分组，共 {channelGroups.Count} 个channel");

                            foreach (var channelGroup in channelGroups)
                            {
                                var channel = channelGroup.Key;
                                var channelItems = channelGroup.ToList();

                                Log.Information($"处理channel={channel}的商品，数量: {channelItems.Count}");

                                foreach (var pendingItem in channelItems)
                                {
                                    try
                                    {
                                        Log.Information($"开始获取商品SKU - barcode: {pendingItem.Item.barcode}, item_id: {pendingItem.Item.item_id}, channel: {channel}");

                                        // 获取商品SKU列表
                                        var product_sku = await getYzProductSkuList(pendingItem.Item.item_id, channel);
                                        Log.Information($"获取到SKU数量: {product_sku.Count} - barcode: {pendingItem.Item.barcode}, item_id: {pendingItem.Item.item_id}");

                                        // 设置是否多规格
                                        pendingItem.Product.has_sku = product_sku.Count == 1 ? 0 : 1;

                                        // 添加到批量操作列表
                                        productList.Add(pendingItem.Product);
                                        allProductSkuList.AddRange(product_sku);

                                        localSucceed++;
                                        Log.Information($"商品处理成功 - barcode: {pendingItem.Item.barcode}, item_id: {pendingItem.Item.item_id}, 累计成功: {localSucceed}");
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.Warning($"处理批量SKU中的商品时出错 - barcode: {pendingItem.Item.barcode}, item_id: {pendingItem.Item.item_id}, 错误: {ex.Message}");
                                        localFail++;
                                        errorData.Add($"处理商品 {pendingItem.Item.title} 时出错: {ex.Message}");
                                    }
                                }
                            }

                            // 批量插入收集到的商品数据
                            if (productList.Count > 0)
                            {
                                Log.Information($"开始批量插入数据 - 商品数: {productList.Count}, SKU数: {allProductSkuList.Count}");
                                await batchCreateProducts(productList, allProductSkuList);
                                Log.Information($"批量插入完成 - {productList.Count} 个商品和 {allProductSkuList.Count} 个SKU");

                                // 清空列表，准备下一批
                                productList.Clear();
                                allProductSkuList.Clear();
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning($"批量处理商品时出错: {ex.Message}");
                            // 记录错误但继续处理下一批
                        }
                    }

                    // 清空待处理列表
                    pendingItems.Clear();
                    Log.Information($"当前页处理完成，清空待处理列表");
                }
                else
                {
                    Log.Information($"当前页无待处理商品");
                }

                // 判断是否还有下一页
                if (request.page_no * request.page_size >= total)
                {
                    Log.Information($"已处理完所有页面 - 当前页: {request.page_no}, 页大小: {request.page_size}, 总数: {total}");
                    break;
                }

                request.page_no++;
                Log.Information($"准备处理下一页 - 页码: {request.page_no}");
            }

            Log.Information($"ProcessProducts方法执行完成 - 成功: {localSucceed}, 失败: {localFail}, 错误数据条数: {errorData.Count}");

            return new resultLogDto
            {
                succeed = localSucceed,
                fail = localFail,
                fail_data = errorData
            };
        }
        catch (Exception ex)
        {
            Log.Error($"ProcessProducts方法发生系统错误: {ex.Message}");
            Log.Error($"错误堆栈: {ex.StackTrace}");
            throw;
        }
    }

    /// <summary>
    /// 自动递归分割时间段并处理数据
    /// </summary>
    /// <param name="originalRequest">原始请求</param>
    /// <param name="startTime">开始时间戳</param>
    /// <param name="endTime">结束时间戳</param>
    /// <returns>处理结果</returns>
    public async Task<resultLogDto> AutoDivideAndProcessByTime(getOnSaleProductRequest originalRequest, long startTime, long endTime)
    {
        // 使用类级别的时间常量

        // 计算时间总跨度
        long timeSpan = endTime - startTime;

        // 创建请求的副本
        var request = new getOnSaleProductRequest
        {
            channel = originalRequest.channel,
            page_no = 1,
            page_size = MAX_PAGE_SIZE,
            order_by = originalRequest.order_by,
            q = originalRequest.q,
            update_time_start_str = originalRequest.update_time_start_str,
            update_time_end_str = originalRequest.update_time_end_str
        };

        // 使用类级别的数据量阈值常量

        // 设置请求的起止时间
        request.update_time_start = startTime;
        request.update_time_end = endTime;

        // 记录处理开始
        Log.Information($"开始处理时间段: {ConvertFromUnixTimestamp(startTime)} 至 {ConvertFromUnixTimestamp(endTime)}，" +
                       $"总跨度: {timeSpan / HOUR:F1} 小时");

        // 如果时间跨度较大，先查询一下数据量
        if (timeSpan > DAY)
        {
            try
            {
                // 创建一个只查询数量的请求
                var countRequest = new getOnSaleProductRequest
                {
                    channel = originalRequest.channel,
                    page_no = 1,
                    page_size = 1, // 只需要获取总数，所以使用最小页大小
                    order_by = originalRequest.order_by,
                    q = originalRequest.q,
                    update_time_start = startTime,
                    update_time_end = endTime
                };

                var countParam = new YouzanParameter();
                countParam.url = "youzan.items.onsale.get/3.0.0";
                countParam.method = "POST";

                // 确保请求对象中所有必要属性不为空
                if (countRequest.q == null) countRequest.q = string.Empty;

                // 尝试将请求对象序列化为JSON
                try
                {
                    countParam.body = countRequest.ToJsonString();
                }
                catch (Exception ex)
                {
                    Log.Warning($"序列化计数请求对象时出错: {ex.Message}");
                    // 捕获异常后直接继续，使用原始请求处理，而不是进行分割
                    return await ProcessProducts(originalRequest);
                }

                var countRes = await _youzanService.GetData(countParam);

                if (countRes.success && countRes.data != null)
                {
                    var countResult = countRes.data.ToObject<onSaleProcuctResponse>();
                    var dataCount = countResult.count;

                    Log.Information($"时间段内数据量预检: {dataCount} 条");

                    // 如果数据量很少，直接处理不分割
                    if (dataCount > 0 && dataCount <= MIN_DATA_THRESHOLD)
                    {
                        Log.Information($"检测到数据量较少 ({dataCount} 条)，直接处理不分割");
                        return await ProcessTimeSegment(request);
                    }

                    // 如果数据量超过阈值，直接进行分割
                    if (dataCount > MAX_DATA_THRESHOLD)
                    {
                        Log.Information($"检测到数据量超过限制 ({dataCount} 条)，直接进行分割");
                        // 计算中间时间点
                        long midTime = (startTime + endTime) / 2;

                        // 递归处理前半段和后半段
                        var firstHalfResult = await AutoDivideAndProcessByTime(originalRequest, startTime, midTime);
                        var secondHalfResult = await AutoDivideAndProcessByTime(originalRequest, midTime, endTime);

                        // 合并结果
                        return new resultLogDto
                        {
                            succeed = (firstHalfResult.succeed ?? 0) + (secondHalfResult.succeed ?? 0),
                            fail = (firstHalfResult.fail ?? 0) + (secondHalfResult.fail ?? 0),
                            fail_data = MergeAndDeduplicate(firstHalfResult.fail_data, secondHalfResult.fail_data)
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // 预检查失败，继续正常流程
                Log.Warning($"数据量预检查失败，将继续正常处理: {ex.Message}");
            }
        }

        // 尝试直接处理整个时间段
        var result = await ProcessTimeSegment(request);

        // 如果数据量超过阈值，需要分割时间段
        if (result.exceed_limit)
        {
            Log.Information($"时间段数据量超过限制({MAX_DATA_THRESHOLD}条)，开始使用二分法处理");

            // 计算中间时间点
            long midTime = (startTime + endTime) / 2;

            // 递归处理前半段和后半段
            var firstHalfResult = await AutoDivideAndProcessByTime(originalRequest, startTime, midTime);
            var secondHalfResult = await AutoDivideAndProcessByTime(originalRequest, midTime, endTime);

            // 合并结果
            return new resultLogDto
            {
                succeed = (firstHalfResult.succeed ?? 0) + (secondHalfResult.succeed ?? 0),
                fail = (firstHalfResult.fail ?? 0) + (secondHalfResult.fail ?? 0),
                fail_data = MergeAndDeduplicate(firstHalfResult.fail_data, secondHalfResult.fail_data)
            };
        }
        // 如果没有返回数据，可能是这段时间没有商品更新，直接返回结果
        else if (result.total_count == 0)
        {
            // 如果时间跨度较大且没有数据，记录一下
            if (timeSpan > DAY)
            {
                Log.Information($"大时间段({timeSpan / DAY:F1}天)内没有数据");
            }
            return result;
        }
        // 如果查询出错，进行指数回退重试
        else if (result.succeed == 0 && result.fail > 0 && timeSpan > HOUR)
        {
            // 检查失败原因是否都是"当前记录存在"
            var failureList = result.fail_data as List<string>;
            bool allFailuresAreExisting = failureList != null &&
                failureList.Count > 0 &&
                failureList.All(x => x.StartsWith("当前记录存在:"));

            // 如果所有失败都是因为记录已存在，这不是真正的错误
            if (allFailuresAreExisting)
            {
                Log.Information($"所有记录({result.total_count}条)都已存在，无需更新");
                return result;
            }

            // 对于小数据量，不进行时间分割，多次重试
            if (result.total_count <= MIN_DATA_THRESHOLD)
            {
                // 使用类级别的重试次数常量
                TimeSegmentResult? retryResult = null;

                Log.Information($"处理时间段出错但数据量很小 ({result.total_count} 条)，多次重试而不分割时间段");

                for (int i = 0; i < MAX_RETRIES_SMALL; i++)
                {
                    // 等待时间随重试次数增加
                    int delay = 1000 * (i + 1);
                    Log.Information($"等待 {delay / 1000} 秒后进行第 {i + 1} 次重试");
                    await Task.Delay(delay);

                    retryResult = await ProcessTimeSegment(request);

                    // 如果重试有部分成功或没有数据需要处理
                    if (retryResult.succeed > 0 || retryResult.total_count == 0)
                    {
                        Log.Information($"重试成功，处理了 {retryResult.succeed} 条数据");
                        return retryResult;
                    }
                }

                // 即使多次重试失败，对于很小的数据量也不分割
                Log.Warning($"小数据量 ({result.total_count} 条) 多次重试仍失败，但由于数据量小，不进行时间分割");
                return result;
            }

            // 尝试简单重试，对于中等数据量不立即分割
            if (result.total_count < 100 && timeSpan < DAY * 7) // 一周内的中等数据量
            {
                // 使用类级别的重试次数常量
                TimeSegmentResult? retryResult = null;
                bool retrySuccess = false;

                Log.Information($"处理时间段出错但数据量较小 ({result.total_count} 条)，尝试简单重试而不是分割时间段");

                for (int i = 0; i < MAX_RETRIES_MEDIUM; i++)
                {
                    // 等待一段时间后重试
                    await Task.Delay(1000 * (i + 1));

                    Log.Information($"第 {i + 1} 次重试处理时间段");
                    retryResult = await ProcessTimeSegment(request);

                    // 如果重试成功
                    if (retryResult.succeed > 0 || retryResult.total_count == 0)
                    {
                        retrySuccess = true;
                        Log.Information($"重试成功，不需要分割时间段");
                        break;
                    }
                }

                // 如果重试成功，直接返回结果
                if (retrySuccess)
                {
                    return retryResult;
                }

                // 重试失败，继续下面的分割处理
                Log.Information($"重试 {MAX_RETRIES_MEDIUM} 次失败，尝试用更小的时间段重试");
            }
            else
            {
                Log.Information($"处理时间段出错 (数据量: {result.total_count} 条)，尝试用更小的时间段重试");
            }

            // 尝试使用四等分法（比二分法更激进地减小时间段）
            long quarterSpan = timeSpan / 4;
            long firstQuarter = startTime + quarterSpan;
            long secondQuarter = firstQuarter + quarterSpan;
            long thirdQuarter = secondQuarter + quarterSpan;

            // 递归处理各个子段
            var result1 = await AutoDivideAndProcessByTime(originalRequest, startTime, firstQuarter);
            var result2 = await AutoDivideAndProcessByTime(originalRequest, firstQuarter, secondQuarter);
            var result3 = await AutoDivideAndProcessByTime(originalRequest, secondQuarter, thirdQuarter);
            var result4 = await AutoDivideAndProcessByTime(originalRequest, thirdQuarter, endTime);

            // 合并四个子段的结果
            return new resultLogDto
            {
                succeed = (result1.succeed ?? 0) + (result2.succeed ?? 0) + (result3.succeed ?? 0) + (result4.succeed ?? 0),
                fail = (result1.fail ?? 0) + (result2.fail ?? 0) + (result3.fail ?? 0) + (result4.fail ?? 0),
                fail_data = MergeAndDeduplicate(
                    MergeAndDeduplicate(result1.fail_data, result2.fail_data),
                    MergeAndDeduplicate(result3.fail_data, result4.fail_data))
            };
        }

        // 处理成功，直接返回结果
        return result;
    }

    /// <summary>
    /// 记录处理错误，更新计数器并添加错误消息
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="localFailCount">本地失败计数器引用</param>
    /// <param name="localErrorData">本地错误数据列表引用</param>
    /// <param name="addToGlobal">是否同时添加到全局错误列表</param>
    private void RecordError(string errorMessage, ref int localFailCount, List<string> localErrorData, bool addToGlobal = true)
    {
        localFailCount++;

        if (addToGlobal)
        {
            _resultHandler.AddFail(1);
            _resultHandler.AddError(errorMessage);
        }

        if (localErrorData != null)
        {
            localErrorData.Add(errorMessage);
        }

        // 记录日志
        Log.Warning(errorMessage);
    }

    /// <summary>
    /// 处理时间段内的商品
    /// </summary>
    /// <param name="request">时间段请求</param>
    /// <returns>处理结果</returns>
    private async Task<TimeSegmentResult> ProcessTimeSegment(getOnSaleProductRequest request)
    {
        Log.Information($"开始处理时间段商品数据 - 请求参数: channel={request.channel}, q={request.q}, order_by={request.order_by}");
        Log.Information($"时间范围: {ConvertFromUnixTimestamp(request.update_time_start ?? 0)} 至 {ConvertFromUnixTimestamp(request.update_time_end ?? 0)}");

        request.page_no = 1;
        request.page_size = MAX_PAGE_SIZE;
        var localSucceed = 0;
        var localFail = 0;
        var localErrorData = new List<string>();
        var exceedLimit = false;
        var total = 0;

        Log.Information($"ProcessTimeSegment初始化完成 - 页大小: {request.page_size}");

        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.items.onsale.get/3.0.0";
            param.method = "POST";

            // 确保请求对象中所有必要属性不为空
            if (request.q == null) request.q = string.Empty;

            // 尝试将请求对象序列化为JSON
            try
            {
                param.body = request.ToJsonString();
                Log.Information($"ProcessTimeSegment请求参数序列化成功，准备调用有赞API");
            }
            catch (Exception ex)
            {
                Log.Warning($"ProcessTimeSegment序列化请求对象时出错: {ex.Message}");
                var errorMsg = $"序列化请求对象时出错: {ex.Message}";
                RecordError(errorMsg, ref localFail, localErrorData);
                return new TimeSegmentResult
                {
                    succeed = 0,
                    fail = localFail,
                    fail_data = localErrorData,
                    exceed_limit = false,
                    total_count = 0
                };
            }

            // 异常处理：记录进入API调用阶段
            Log.Information($"开始调用有赞API获取商品数据，时间段: {ConvertFromUnixTimestamp(request.update_time_start ?? 0)} 至 {ConvertFromUnixTimestamp(request.update_time_end ?? 0)}");

            var res = await _youzanService.GetData(param);

            if (!res.success)
            {
                var errorMsg = $"API请求失败: {res.message}";
                RecordError(errorMsg, ref localFail, localErrorData);
                return new TimeSegmentResult
                {
                    succeed = 0,
                    fail = localFail,
                    fail_data = localErrorData,
                    exceed_limit = false,
                    total_count = 0
                };
            }

            // 检查返回的数据是否为空
            if (res.data == null)
            {
                var errorMsg = "API返回成功但数据为空";
                RecordError(errorMsg, ref localFail, localErrorData);
                return new TimeSegmentResult
                {
                    succeed = 0,
                    fail = localFail,
                    fail_data = localErrorData,
                    exceed_limit = false,
                    total_count = 0
                };
            }

            try
            {
                var result = res.data.ToObject<onSaleProcuctResponse>();
                total = result.count;

                Log.Information($"时间段 {ConvertFromUnixTimestamp(request.update_time_start ?? 0)} 至 {ConvertFromUnixTimestamp(request.update_time_end ?? 0)} 获取商品数量: {total}");

                // 检查是否超过最大数据量限制
                if (total > MAX_DATA_THRESHOLD)
                {
                    Log.Information($"检测到数据量较大 (数据量: {total} 条)，系统将自动优化为更小时间段处理");
                    exceedLimit = true;
                    return new TimeSegmentResult { exceed_limit = true, total_count = total };
                }

                // 检查返回的商品列表是否为空
                if (result.items == null || result.items.Count == 0)
                {
                    Log.Information($"API返回成功，但商品列表为空，总数: {total}");
                    return new TimeSegmentResult
                    {
                        succeed = 0,
                        fail = 0,
                        fail_data = new List<string>(),
                        exceed_limit = false,
                        total_count = total
                    };
                }

                // 创建集合用于批量插入
                var productList = new List<productEntity>();
                var allProductSkuList = new List<productSkuEntity>();

                // 创建批量获取SKU的数据结构
                var pendingItems = new List<dynamic>(); // 待处理的商品对象
                var maxBatchSize = 20; // 每批处理的最大商品数，可根据API限制调整

                // 处理商品数据
                while (true)
                {
                    try
                    {
                        // 重新设置请求参数，因为分页变化
                        param = new YouzanParameter();
                        param.url = "youzan.items.onsale.get/3.0.0";
                        param.method = "POST";

                        // 确保请求对象中所有必要属性不为空
                        if (request.q == null) request.q = string.Empty;

                        // 尝试将请求对象序列化为JSON
                        try
                        {
                            param.body = request.ToJsonString();
                        }
                        catch (Exception ex)
                        {
                            Log.Warning($"序列化请求对象时出错: {ex.Message}");
                            var errorMsg = $"处理第 {request.page_no} 页数据时序列化失败";
                            RecordError(errorMsg, ref localFail, localErrorData);
                            break;
                        }

                        res = await _youzanService.GetData(param);

                        if (!res.success)
                        {
                            Log.Warning($"获取第 {request.page_no} 页数据失败: {res.message}");
                            break;
                        }

                        if (res.data == null)
                        {
                            Log.Warning($"获取第 {request.page_no} 页数据成功，但数据为空");
                            break;
                        }

                        result = res.data.ToObject<onSaleProcuctResponse>();
                        total = result.count;

                        Log.Information($"处理页码: {request.page_no}, 页大小: {request.page_size}, 总数: {total}");

                        if (result.items == null || result.items.Count == 0)
                        {
                            Log.Warning($"第 {request.page_no} 页返回的商品列表为空");
                            break;
                        }

                        // 第一步：预处理所有符合条件的商品
                        // 先批量查询所有商品ID对应的商品信息
                        var itemIds = result.items.Where(x => x.item_type == 0).Select(x => x.item_id).ToList();
                        Log.Information($"ProcessTimeSegment第 {request.page_no} 页筛选出符合条件的商品ID数量: {itemIds.Count} (item_type=0)");

                        if (itemIds.Count == 0)
                        {
                            Log.Information($"ProcessTimeSegment第 {request.page_no} 页没有符合条件的商品 (item_type=0)");

                            // 页码加一并继续循环
                            request.page_no++;
                            // 检查是否超过最大页码限制
                            if (request.page_no > MAX_PAGE_NO || request.page_no * request.page_size >= total)
                                break;

                            continue;
                        }

                        var existingProducts = await _repository.Queryable<productEntity>()
                            .Where(x => itemIds.Contains(x.product_id))
                            .ToListAsync();
                        Log.Information($"ProcessTimeSegment查询到已存在的商品数量: {existingProducts.Count}");

                        // 批量写入缓存
                        foreach (var product in existingProducts)
                        {
                            var cacheKey = $"product:{product.product_id}";
                            await _cache.SetAsync(cacheKey, product, TimeSpan.FromMinutes(1));
                        }
                        Log.Information($"ProcessTimeSegment已将 {existingProducts.Count} 个商品写入缓存");

                        Log.Information($"ProcessTimeSegment开始处理第 {request.page_no} 页商品，总数: {result.items.Count}");
                        foreach (var item in result.items)
                        {
                            try
                            {
                                // 如果item的item_type不等于0，跳过
                                if (item.item_type != 0)
                                {
                                    Log.Information($"ProcessTimeSegment跳过非普通商品 - barcode: {item.barcode}, item_id: {item.item_id}, item_type: {item.item_type}");
                                    localFail++;
                                    _resultHandler.AddFail(1);
                                    continue;
                                }

                                // 查询商品是否存在 - 直接从缓存获取
                                var cacheKey = $"product:{item.item_id}";
                                var product_info = await _cache.GetAsync<productEntity>(cacheKey);

                                // 处理已存在的商品
                                if (product_info != null)
                                {
                                    Log.Information($"ProcessTimeSegment商品已存在 - barcode: {item.barcode}, item_id: {item.item_id}, 缓存修改时间: {product_info.modify_date}, 有赞更新时间: {item.update_time}");

                                    try
                                    {
                                        // 如果需要更新，则删除旧记录
                                        if (product_info.modify_date < item.update_time)
                                        {
                                            Log.Information($"ProcessTimeSegment商品需要更新 - barcode: {item.barcode}, item_id: {item.item_id}, 开始删除旧数据");
                                            var deleteResult = await removeProduct(product_info.product_id);
                                            if (deleteResult)
                                            {
                                                await _cache.DelAsync($"product:{product_info.product_id}");
                                                Log.Information($"ProcessTimeSegment成功删除商品和缓存 - barcode: {item.barcode}, item_id: {item.item_id}, product_id: {product_info.product_id}");

                                                // 移除跨渠道删除逻辑，让每个商品根据自己的时间对比独立处理
                                                // 这样可以避免不必要的删除操作，提高处理效率
                                            }
                                            else
                                            {
                                                // 删除失败，记录错误但继续处理
                                                Log.Warning($"ProcessTimeSegment删除商品失败，但继续处理流程 - barcode: {item.barcode}, item_id: {item.item_id}, product_id: {product_info.product_id}");
                                            }
                                        }
                                        else
                                        {
                                            // 商品存在且不需要更新
                                            Log.Information($"ProcessTimeSegment商品无需更新，跳过 - barcode: {item.barcode}, item_id: {item.item_id}, title: {item.title}");
                                            localFail++;
                                            _resultHandler.AddFail(1);
                                            localErrorData.Add("当前记录存在:" + item.barcode + " " + item.title);
                                            continue;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.Error($"ProcessTimeSegment处理已存在商品时出错 - barcode: {item.barcode}, item_id: {item.item_id}, 错误: {ex.Message}");
                                        localFail++;
                                        _resultHandler.AddFail(1);
                                        localErrorData.Add($"处理商品 {item.title} 时出错: {ex.Message}");
                                        continue;
                                    }
                                }
                                else
                                {
                                    Log.Information($"ProcessTimeSegment新商品，准备处理 - barcode: {item.barcode}, item_id: {item.item_id}, title: {item.title}");
                                }

                                DateTime? ParseDateTime(string value)
                                {
                                    if (string.IsNullOrWhiteSpace(value))
                                        return null;

                                    if (DateTime.TryParseExact(
                                            value.Trim(),
                                            "yyyy-MM-dd HH:mm:ss",
                                            CultureInfo.InvariantCulture,
                                            DateTimeStyles.None,
                                            out var result))
                                    {
                                        return result;
                                    }

                                    return null; // 或者抛出异常，根据业务逻辑决定
                                }
                                // 创建新商品实体
                                var product = new productEntity()
                                {
                                    product_id = item.item_id,
                                    bar_code = item.barcode,
                                    product_name = item.title,
                                    make_price = item.price,
                                    created_user_id = "yz",
                                    product_code = item.item_no,
                                    root_product_id = item.root_item_id,
                                    state = 1,
                                    channel = item.channel,
                                    store_id = item.root_kdt_id,
                                    created_date = item.created_time,
                                    modify_date = item.update_time,
                                    //created_date = ParseDateTime(item.created_time),
                                    //modify_date = ParseDateTime(item.update_time),
                                    goods_type = item.item_type.ToString()
                                };

                                // 收集待处理的商品信息
                                pendingItems.Add(new { Item = item, Product = product });
                                Log.Information($"ProcessTimeSegment商品已加入待处理队列 - barcode: {item.barcode}, item_id: {item.item_id}, 当前队列长度: {pendingItems.Count}");
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"ProcessTimeSegment预处理商品时出错 - barcode: {item.barcode}, item_id: {item.item_id}, 错误: {ex.Message}");
                                localFail++;
                                _resultHandler.AddFail(1);
                                localErrorData.Add($"处理商品 {item.title} 时出错: {ex.Message}");
                            }
                        }

                        // 如果请求的页码乘以每页大小大于等于总条数，则跳出循环
                        if (request.page_no * request.page_size >= total)
                        {
                            Log.Information($"ProcessTimeSegment已处理完所有页面 - 当前页: {request.page_no}, 页大小: {request.page_size}, 总数: {total}");
                            break;
                        }

                        // 页码加一
                        request.page_no++;
                        Log.Information($"ProcessTimeSegment准备处理下一页 - 页码: {request.page_no}");
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"ProcessTimeSegment处理第 {request.page_no} 页数据时出错: {ex.Message}");

                        // 即使出错，也尝试继续处理下一页
                        request.page_no++;

                        // 如果达到总页数，跳出循环
                        if (request.page_no * request.page_size >= total)
                            break;
                    }
                }

                // 第二步：批量处理收集到的商品
                if (pendingItems.Count > 0)
                {
                    Log.Information($"ProcessTimeSegment开始批量处理商品，总数: {pendingItems.Count}, 批大小: {maxBatchSize}");

                    // 按批次处理待处理商品
                    for (int i = 0; i < pendingItems.Count; i += maxBatchSize)
                    {
                        try
                        {
                            // 获取当前批次 - 使用 GetRange 代替 Skip/Take 提高性能
                            int currentBatchSize = Math.Min(maxBatchSize, pendingItems.Count - i);
                            var batch = pendingItems.GetRange(i, currentBatchSize);

                            Log.Information($"ProcessTimeSegment批量处理第 {i / maxBatchSize + 1} 批商品，数量: {batch.Count}");

                            // 根据channel分组商品
                            var channelGroups = batch.GroupBy(x => x.Item.channel).ToList();
                            Log.Information($"ProcessTimeSegment当前批次按channel分组，共 {channelGroups.Count} 个channel");

                            foreach (var channelGroup in channelGroups)
                            {
                                try
                                {
                                    var channel = channelGroup.Key;
                                    var channelItems = channelGroup.ToList();

                                    Log.Information($"ProcessTimeSegment处理channel={channel}的商品，数量: {channelItems.Count}");

                                    // 收集当前分组的商品ID
                                    var itemIds = new List<string>();
                                    foreach (var item in channelItems)
                                    {
                                        if (!string.IsNullOrEmpty(item.Item.item_id))
                                            itemIds.Add(item.Item.item_id.ToString());
                                    }
                                    Log.Information($"ProcessTimeSegment收集到商品ID数量: {itemIds.Count} (channel={channel})");

                                    if (itemIds.Count == 0)
                                    {
                                        Log.Warning($"此批次中channel={channel}的商品没有有效的item_id");
                                        continue;
                                    }

                                    // 批量获取当前channel的商品SKU
                                    Dictionary<string, List<productSkuEntity>> skuDict;
                                    try
                                    {
                                        skuDict = await getBatchYzProductSkuList(itemIds, channel.ToString());
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.Error($"批量获取channel={channel}的商品SKU失败: {ex.Message}");
                                        // 继续处理下一个channel组
                                        continue;
                                    }

                                    // 处理当前channel的每个商品
                                    foreach (var pendingItem in channelItems)
                                    {
                                        try
                                        {
                                            var curItem = pendingItem.Item;
                                            var curProduct = pendingItem.Product;

                                            // 获取商品的SKU列表
                                            List<productSkuEntity>? product_sku = null;

                                            // 尝试从批量结果中获取
                                            if (skuDict != null && skuDict.TryGetValue(curItem.item_id, out product_sku))
                                            {
                                                if (product_sku != null && product_sku.Count > 0)
                                                {
                                                    // 设置是否多规格
                                                    curProduct.has_sku = product_sku.Count == 1 ? 0 : 1;
                                                }
                                                else
                                                {
                                                    Log.Warning($"商品 {curItem.item_id} 在批量结果中返回了空的SKU列表");
                                                    // 使用单个查询作为备选
                                                    product_sku = await getYzProductSkuList(curItem.item_id, curItem.channel);
                                                    curProduct.has_sku = product_sku.Count == 1 ? 0 : 1;
                                                }
                                            }
                                            else
                                            {
                                                // 批量查询中没有找到，使用单个查询作为备选
                                                Log.Information($"商品 {curItem.item_id} 在批量查询中未找到SKU，尝试单独查询");
                                                product_sku = await getYzProductSkuList(curItem.item_id, curItem.channel);
                                                curProduct.has_sku = product_sku.Count == 1 ? 0 : 1;
                                            }

                                            if (product_sku != null && product_sku.Count > 0)
                                            {
                                                // 添加到批量操作列表
                                                productList.Add(curProduct);
                                                allProductSkuList.AddRange(product_sku);

                                                // 成功数量加1
                                                localSucceed++;
                                                _resultHandler.AddSucceed(1);
                                            }
                                            else
                                            {
                                                Log.Warning($"商品 {curItem.item_id} 未能获取到有效的SKU信息");
                                                localFail++;
                                                _resultHandler.AddFail(1);
                                                localErrorData.Add($"商品 {curItem.title} 未能获取到有效的SKU信息");
                                                _resultHandler.AddError($"商品 {curItem.title} 未能获取到有效的SKU信息");
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Log.Error($"处理批量SKU中的商品 {pendingItem.Item.item_id} 时出错: {ex.Message}");
                                            localFail++;
                                            _resultHandler.AddFail(1);
                                            localErrorData.Add($"处理商品 {pendingItem.Item.title} 时出错: {ex.Message}");
                                            _resultHandler.AddError($"处理商品 {pendingItem.Item.title} 时出错: {ex.Message}");
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Log.Error($"处理channel分组时出错: {ex.Message}");
                                    // 继续处理下一个channel组
                                }
                            }

                            // 批量插入收集到的商品数据
                            if (productList.Count > 0)
                            {
                                try
                                {
                                    await batchCreateProducts(productList, allProductSkuList);
                                    Log.Information($"批量插入 {productList.Count} 个商品和 {allProductSkuList.Count} 个SKU");
                                }
                                catch (Exception ex)
                                {
                                    Log.Error($"批量插入商品数据失败: {ex.Message}");
                                    // 记录失败的商品数量
                                    localFail += productList.Count;
                                    _resultHandler.AddFail(productList.Count);

                                    // 将失败的商品添加到错误列表中
                                    foreach (var product in productList)
                                    {
                                        localErrorData.Add($"批量插入商品 {product.product_name} 失败: {ex.Message}");
                                        _resultHandler.AddError($"批量插入商品 {product.product_name} 失败: {ex.Message}");
                                    }
                                }
                                finally
                                {
                                    // 不管成功或失败，都清空列表，准备下一批
                                    productList.Clear();
                                    allProductSkuList.Clear();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"批量处理商品时出错: {ex.Message}");
                            // 记录错误但继续处理下一批
                        }
                    }

                    // 清空待处理列表
                    pendingItems.Clear();
                }

                // 处理剩余未提交的商品
                if (productList.Count > 0)
                {
                    try
                    {
                        await batchCreateProducts(productList, allProductSkuList);
                        Log.Information($"处理剩余 {productList.Count} 个商品");
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"处理剩余商品时出错: {ex.Message}");
                        // 记录失败的商品数量
                        localFail += productList.Count;
                        _resultHandler.AddFail(productList.Count);
                    }
                }

                // 将本次处理的错误数据添加到累积列表中
                if (localErrorData != null && localErrorData.Count > 0)
                {
                    _resultHandler.AddErrors(localErrorData);
                }

                return new TimeSegmentResult
                {
                    succeed = localSucceed,
                    fail = localFail,
                    fail_data = localErrorData ?? new List<string>(),
                    exceed_limit = exceedLimit,
                    total_count = total
                };
            }
            catch (Exception ex)
            {
                Log.Error($"处理API返回数据时出错: {ex.Message}");
                return new TimeSegmentResult
                {
                    succeed = 0,
                    fail = localFail + 1,
                    fail_data = new List<string>(localErrorData) { $"处理API返回数据时出错: {ex.Message}" },
                    exceed_limit = false,
                    total_count = 0 // 出错时无法获取总数，设为0
                };
            }
        }
        catch (Exception ex)
        {
            Log.Error($"处理时间段 {ConvertFromUnixTimestamp(request.update_time_start ?? 0)} 至 {ConvertFromUnixTimestamp(request.update_time_end ?? 0)} 出错: {ex.Message}");

            return new TimeSegmentResult
            {
                succeed = 0,
                fail = localFail + 1,
                fail_data = new List<string>(localErrorData) { $"处理时间段出错: {ex.Message}" },
                exceed_limit = false,
                total_count = 0 // 出错时无法获取总数，设为0
            };
        }
    }

  
  
    /// <summary>
    /// 合并并去重两个错误列表
    /// </summary>
    private List<string> MergeAndDeduplicate(object list1, object list2)
    {
        var processedItems = new HashSet<string>();
        var result = new List<string>();

        // 处理第一个列表
        var errorList1 = list1 as List<string>;
        if (errorList1 != null)
        {
            foreach (var error in errorList1)
            {
                // 从错误消息中提取商品编码
                var parts = error.Split(new[] { ' ' }, 2);
                if (parts.Length >= 2)
                {
                    var codeWithPrefix = parts[0]; // 包含"当前记录存在:"的部分
                    var code = codeWithPrefix.Substring(codeWithPrefix.LastIndexOf(':') + 1);

                    // 使用商品编码作为唯一标识
                    if (!processedItems.Contains(code))
                    {
                        processedItems.Add(code);
                        result.Add(error);
                    }
                }
                else
                {
                    // 如果无法解析，则保留原始错误消息
                    result.Add(error);
                }
            }
        }

        // 处理第二个列表
        var errorList2 = list2 as List<string>;
        if (errorList2 != null)
        {
            foreach (var error in errorList2)
            {
                // 从错误消息中提取商品编码
                var parts = error.Split(new[] { ' ' }, 2);
                if (parts.Length >= 2)
                {
                    var codeWithPrefix = parts[0]; // 包含"当前记录存在:"的部分
                    var code = codeWithPrefix.Substring(codeWithPrefix.LastIndexOf(':') + 1);

                    // 使用商品编码作为唯一标识
                    if (!processedItems.Contains(code))
                    {
                        processedItems.Add(code);
                        result.Add(error);
                    }
                }
                else
                {
                    // 如果无法解析，则保留原始错误消息
                    result.Add(error);
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 时间段处理结果类
    /// </summary>
    private class TimeSegmentResult : resultLogDto
    {
        /// <summary>
        /// 是否超过API限制（4000条）
        /// </summary>
        public bool exceed_limit { get; set; } = false;

        /// <summary>
        /// 当前时间段查询到的数据总量
        /// </summary>
        public int total_count { get; set; } = 0;
    }




    /// <summary>
    /// 将 DateTime 转换为 13 位 Unix 时间戳
    /// </summary>
    /// <param name="date">DateTime 对象</param>
    /// <returns>13 位 Unix 时间戳</returns>
    private long ConvertToUnixTimestamp(DateTime date)
    {
        // 定义一个表示1970年1月1日0时0分0秒的DateTime对象
        var epochStart = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        // 返回date对象转换为UTC时间后与epochStart对象的时间差，以毫秒为单位
        return (long)(date.ToUniversalTime() - epochStart).TotalMilliseconds;
    }

    /// <summary>
    /// 将 13 位 Unix 时间戳转换为北京时间 DateTime
    /// </summary>
    /// <param name="unixTimestamp">13 位 Unix 时间戳</param>
    /// <returns>对应的北京时间 DateTime 对象</returns>
    private DateTime ConvertFromUnixTimestamp(long unixTimestamp)
    {
        var epochStart = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        // 转换为 UTC 时间，再转换为北京时间 (UTC+8)
        return TimeZoneInfo.ConvertTimeFromUtc(epochStart.AddMilliseconds(unixTimestamp), TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"));
    }






    /// <summary>
    /// 更新商品库存
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("updateStore")]
    public async Task<dynamic> updateProductStore()
    {
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();

        while (true)
        {
            var query = new productStoreQuery();
            query.currentPage = page_index;
            query.pageSize = 100;
            query.tag_status = "add";

            var stockGroups = await getGroupedStockList(query);
            if (stockGroups == null || !stockGroups.Any())
                break;

            foreach (var stockGroup in stockGroups)
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.item.update.branch.sku/1.0.0";
                    param.method = "POST";
                    param.body = stockGroup.ToJsonString();
                    var res = await _youzanService.GetData(param);

                    // 获取所有涉及的店铺ID，确保无论成功与否都更新状态
                    var allStoreIds = ExtractAllStoreIds(stockGroup);

                    if (res.success)
                    {
                        // 处理每个店铺的返回结果
                        var results = res.data.ToObject<List<shopProcuctStockResponse>>();

                        if (results != null && results.Any())
                        {
                            var successStoreIds = new List<string>();
                            var failStoreIds = new List<string>();
                            var failMessages = new List<string>();

                            foreach (var result in results)
                            {
                                if (result.is_success)
                                {
                                    successStoreIds.Add(result.node_kdt_id.ToString());
                                }
                                else
                                {
                                    failStoreIds.Add(result.node_kdt_id.ToString());
                                    failMessages.Add($"店铺ID:{result.node_kdt_id}, 错误信息:{result.message}");
                                }
                            }

                            // 更新成功的店铺状态
                            if (successStoreIds.Any())
                            {
                                succeed += successStoreIds.Count;
                                await UpdateStockStatusForStores(stockGroup, successStoreIds, "select");
                            }

                            // 更新失败的店铺状态
                            if (failStoreIds.Any())
                            {
                                fail += failStoreIds.Count;
                                errorData.AddRange(failMessages);
                                await UpdateStockStatusForStores(stockGroup, failStoreIds, "add-error", string.Join("; ", failMessages));
                            }

                            // 检查是否有店铺未在结果中返回
                            var processedStoreIds = successStoreIds.Concat(failStoreIds).ToList();
                            var unprocessedStoreIds = allStoreIds.Except(processedStoreIds).ToList();

                            if (unprocessedStoreIds.Any())
                            {
                                fail += unprocessedStoreIds.Count;
                                var errorMsg = "店铺在API返回结果中未找到";
                                errorData.Add($"未处理的店铺数：{unprocessedStoreIds.Count}, 错误：{errorMsg}");
                                await UpdateStockStatusForStores(stockGroup, unprocessedStoreIds, "add-error", errorMsg);
                            }
                        }
                        else
                        {
                            // API返回成功但没有结果详情，按成功处理所有店铺
                            succeed += allStoreIds.Count;
                            await UpdateStockStatusForStores(stockGroup, allStoreIds, "select");
                        }
                    }
                    else
                    {
                        // 如果整个请求失败，确保所有店铺都被标记为错误
                        fail += allStoreIds.Count;
                        errorData.Add(res.message);
                        await UpdateStockStatusForStores(stockGroup, allStoreIds, "add-error", res.message);
                    }
                }
                catch (Exception ex)
                {
                    fail++;
                    errorData.Add(ex.Message);

                    var allStoreIds = ExtractAllStoreIds(stockGroup);
                    if (allStoreIds.Any())
                    {
                        await UpdateStockStatusForStores(stockGroup, allStoreIds, "add-error", ex.Message);
                    }
                }
            }
            page_index++;
        }

        return new resultLogDto
        {
            succeed = succeed,
            fail = fail,
            fail_data = errorData
        };
    }

    /// <summary>
    /// 批量更新指定店铺的商品库存状态
    /// </summary>
    /// <param name="stockGroup">包含商品库存更新信息的请求对象</param>
    /// <param name="storeIds">需要更新状态的店铺ID列表</param>
    /// <param name="status">要更新的状态值（如："select"表示成功，"add-error"表示失败）</param>
    /// <param name="message">可选的错误消息，用于记录失败原因</param>
    /// <returns>异步任务</returns>
    /// <remarks>
    /// 此方法主要用于商品库存同步过程中，用于标记处理状态。
    /// 成功时标记为"select"，失败时标记为"add-error"并记录错误信息。
    /// 通过调用BatchUpdateStockStatus方法实现批量更新操作。
    /// </remarks>
    private async Task UpdateStockStatusForStores(updateProductStoreRequest stockGroup, List<string> storeIds, string status, string? message = null)
    {
        Console.WriteLine($"调试: UpdateStockStatusForStores被调用 - product_id: {stockGroup.root_item_id}, storeIds: [{string.Join(", ", storeIds)}], status: {status}");

        await BatchUpdateStockStatus(
            stockGroup.root_item_id,
            stockGroup.multi_sku_update_open_param_list?.FirstOrDefault()?.root_sku_id ?? 0,
            storeIds,
            status,
            message ?? ""
        );
    }

    /// <summary>
    /// 从请求中提取所有店铺ID
    /// </summary>
    private List<string> ExtractAllStoreIds(updateProductStoreRequest stockGroup)
    {
        var storeIds = new List<string>();

        // 处理无规格商品
        if (stockGroup.no_sku_update_open_param_list != null && stockGroup.no_sku_update_open_param_list.Any())
        {
            storeIds.AddRange(stockGroup.no_sku_update_open_param_list.Select(x => x.node_kdt_id.ToString()));
        }

        // 处理多规格商品
        if (stockGroup.multi_sku_update_open_param_list != null && stockGroup.multi_sku_update_open_param_list.Any())
        {
            foreach (var skuGroup in stockGroup.multi_sku_update_open_param_list)
            {
                if (skuGroup.update_price_stock_open_param_list != null && skuGroup.update_price_stock_open_param_list.Any())
                {
                    storeIds.AddRange(skuGroup.update_price_stock_open_param_list.Select(x => x.node_kdt_id.ToString()));
                }
            }
        }

        return storeIds.Distinct().ToList();
    }

    /// <summary>
    /// 获取分组后的库存列表
    /// </summary>
    private async Task<List<updateProductStoreRequest>> getGroupedStockList(productStoreQuery query)
    {
        // 使用类级别的常量
        const int LOCAL_MAX_DATA_THRESHOLD = 1000; // 本方法特定的数据量限制
        var stockList = new List<updateProductStoreRequest>();

        // 使用 With(NoLock) 提高查询性能，添加索引提示
        var stockData = await _repository.Queryable<productStockEntity, shopEntity, productEntity>((a, b, c) =>
            new JoinQueryInfos(
                JoinType.Left, a.store_id == b.id,
                JoinType.Left, a.product_id.ToString() == c.product_id))
            .With(SqlWith.NoLock)
            .Where((a, b, c) => a.tag_status == query.tag_status && c.modify_user_id == "sync") // 使用索引
            .WhereIF(!string.IsNullOrEmpty(query.store_id), (a, b, c) => a.store_id == query.store_id)
            .WhereIF(query.product_id > 0, (a, b, c) => a.product_id == query.product_id)
            .Select((a, b, c) => new {
                product_id = a.product_id,
                sku_id = a.sku_id,
                store_id = b.net_source_no,
                stock = a.stock,
                has_sku = a.has_sku
            })
            .Take(LOCAL_MAX_DATA_THRESHOLD)
            .ToListAsync();

        if (!stockData.Any())
            return stockList;

        // 使用 Dictionary 优化查找性能
        var productGroups = stockData
            .GroupBy(x => new { x.product_id, x.has_sku })
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var productGroup in productGroups)
        {
            if (productGroup.Key.has_sku == 0) // 无规格商品
            {
                var storeItems = productGroup.Value
                    .Select(x => new updateProductStoreRequest.noSkuList
                    {
                        node_kdt_id = x.store_id,
                        stock_num = x.stock ?? 0
                    })
                    .ToList();

                // 使用更高效的分批方法
                for (int i = 0; i < storeItems.Count; i += MAX_BATCH_SIZE)
                {
                    var chunk = storeItems.Skip(i).Take(MAX_BATCH_SIZE).ToList();
                    stockList.Add(new updateProductStoreRequest
                    {
                        root_item_id = productGroup.Key.product_id,
                        no_sku_update_open_param_list = chunk,
                        multi_sku_update_open_param_list = new List<updateProductStoreRequest.multiSkuList>()
                    });
                }
            }
            else // 多规格商品
            {
                var skuGroups = productGroup.Value
                    .GroupBy(x => x.sku_id)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var skuGroup in skuGroups)
                {
                    var storeItems = skuGroup.Value
                        .Select(x => new updateProductStoreRequest.multiSkuList.updateStockList
                        {
                            node_kdt_id = x.store_id,
                            stock_num = x.stock ?? 0
                        })
                        .ToList();

                    // 使用更高效的分批方法
                    for (int i = 0; i < storeItems.Count; i += MAX_BATCH_SIZE)
                    {
                        var chunk = storeItems.Skip(i).Take(MAX_BATCH_SIZE).ToList();
                        stockList.Add(new updateProductStoreRequest
                        {
                            root_item_id = productGroup.Key.product_id,
                            no_sku_update_open_param_list = new List<updateProductStoreRequest.noSkuList>(),
                            multi_sku_update_open_param_list = new List<updateProductStoreRequest.multiSkuList>
                            {
                                new updateProductStoreRequest.multiSkuList
                                {
                                    root_sku_id = skuGroup.Key,
                                    update_price_stock_open_param_list = chunk
                                }
                            }
                        });
                    }

                }

            }
        }

        return stockList;
    }

    /// <summary>
    /// 批量更新库存状态
    /// </summary>
    private async Task BatchUpdateStockStatus(List<(long product_id, long sku_id, List<string> storeIds)> updateItems, string tag_status = "select", string tag_body = "")
    {
        Console.WriteLine($"调试: BatchUpdateStockStatus被调用 - updateItems数量: {updateItems?.Count ?? 0}, tag_status: {tag_status}");

        if (updateItems == null || !updateItems.Any())
        {
            Console.WriteLine("调试: updateItems为空，直接返回");
            return;
        }

        // 安全检查：确保所有product_id都有效
        if (updateItems.Any(x => x.product_id <= 0))
        {
            Console.WriteLine("警告: 存在无效的product_id，已阻止可能的全表更新操作");
            return;
        }

        try
        {
            // 开启事务
            await _db.BeginTranAsync();

            // 按商品ID分组
            var productGroups = updateItems.GroupBy(x => x.product_id);

            foreach (var productGroup in productGroups)
            {
                var product_id = productGroup.Key;
                var storeIds = productGroup.SelectMany(x => x.storeIds).Distinct().ToList();

                // 从缓存获取店铺信息
                var shopInfos = await _shopCacheService.GetShopsFromCache(storeIds);

                // 调试信息：打印店铺映射信息
                Console.WriteLine($"调试: 商品ID {product_id}, 输入storeIds: [{string.Join(", ", storeIds)}]");
                foreach (var shop in shopInfos)
                {
                    Console.WriteLine($"调试: 店铺信息 - id: {shop.id}, net_source_no: {shop.net_source_no}, store_no: {shop.store_no}");
                }

                // 构建店铺ID匹配方式（转换为bigint类型）
                var storeLongIds = new List<long>();
                foreach (var shop in shopInfos)
                {
                    // 添加id（主要匹配方式，数据库store_id是bigint类型）
                    if (!string.IsNullOrEmpty(shop.id) && long.TryParse(shop.id, out long storeId))
                    {
                        storeLongIds.Add(storeId);
                    }

                    // 添加store_no（备用匹配方式）
                    if (!string.IsNullOrEmpty(shop.store_no) && shop.store_no != shop.id && long.TryParse(shop.store_no, out long storeNo))
                    {
                        storeLongIds.Add(storeNo);
                    }
                }

                // 去重
                storeLongIds = storeLongIds.Distinct().ToList();

                if (!storeLongIds.Any())
                {
                    Console.WriteLine($"警告: 商品ID {product_id} 未找到有效的店铺映射");
                    continue;
                }

                Console.WriteLine($"调试: 用于匹配的storeIds: [{string.Join(", ", storeLongIds)}]");

                // 获取该商品的所有SKU ID
                var skuIds = productGroup.Where(x => x.sku_id > 0).Select(x => x.sku_id).Distinct().ToList();

                // 构建批量更新条件
                var updateable = _repository.Updateable<productStockEntity>()
                    .SetColumns(it => new productStockEntity
                    {
                        tag_status = tag_status,
                        modify_date = DateTime.Now,
                        tag_body = tag_body
                    })
                    .Where(x => x.product_id == product_id);

                // 如果skuIds有效，添加到条件中    
                if (skuIds.Any())
                {
                    updateable = updateable.Where(x => skuIds.Contains(x.sku_id));
                }

                // 调试信息：打印更新条件
                Console.WriteLine($"调试: 更新条件 - product_id: {product_id}, skuIds: [{string.Join(", ", skuIds)}], storeIds: [{string.Join(", ", storeLongIds)}]");

                // 分批处理店铺ID以优化性能
                const int batchSize = 20; // 每批最多20个店铺ID
                var totalResult = 0;

                for (int i = 0; i < storeLongIds.Count; i += batchSize)
                {
                    var batchStoreIds = storeLongIds.Skip(i).Take(batchSize).ToList();

                    // 为每批创建更新条件
                    var batchUpdateable = _repository.Updateable<productStockEntity>()
                        .SetColumns(it => new productStockEntity
                        {
                            tag_status = tag_status,
                            modify_date = DateTime.Now,
                            tag_body = tag_body
                        })
                        .Where(x => x.product_id == product_id);

                    // 如果skuIds有效，添加到条件中
                    if (skuIds.Any())
                    {
                        batchUpdateable = batchUpdateable.Where(x => skuIds.Contains(x.sku_id));
                    }

                    // 添加当前批次的店铺ID条件（使用bigint类型）
                    batchUpdateable = batchUpdateable.Where(x => batchStoreIds.Contains(x.store_id));

                    // 执行当前批次的更新
                    var batchResult = await batchUpdateable.ExecuteCommandAsync();
                    totalResult += batchResult;

                    if (batchStoreIds.Count > 1)
                    {
                        Console.WriteLine($"调试: 批次 {i/batchSize + 1} 更新完成，店铺数: {batchStoreIds.Count}, 更新记录数: {batchResult}");
                    }
                }

                var result = totalResult;

                Console.WriteLine($"商品ID {product_id} 批量更新完成。SKU数量: {skuIds.Count}, 更新记录数: {result}, 匹配店铺ID数量: {storeLongIds.Count}");
            }

            // 提交事务
            await _db.CommitTranAsync();
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            Console.WriteLine($"更新库存状态失败: {ex.Message}");
            throw;
        }
    }

    // 重载方法，保持向后兼容
    private async Task BatchUpdateStockStatus(long product_id, long sku_id, List<string> storeIds, string tag_status = "select", string tag_body = "")
    {
        await BatchUpdateStockStatus(
            new List<(long, long, List<string>)> { (product_id, sku_id, storeIds) },
            tag_status,
            tag_body
        );
    }

    /// <summary>
    /// 更新商品价格
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("updatePrice")]
    public async Task<dynamic> updateProductPrice()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();
        while (true)
        {
            var query = new productPriceQuery();
            query.currentPage = page_index;
            query.pageSize = 20;
            query.tag_status = "add";
            var pageData = await getPageUpdateProductPriceList(query);
            //共计多少页
            var Total = pageData == null ? 0 : pageData.Count();
            if (Total == 0)
                break;
            foreach (var item in pageData ?? Enumerable.Empty<dynamic>())
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.item.incremental.update/1.0.1";
                    param.method = "POST";
                    param.body = new { param = item }.ToJsonString();
                    var res = await _youzanService.GetData(param);
                    if (res.success == true)
                    {
                        succeed++;
                        string sku_id = "";
                        if (item.sku_list != null && item.sku_list.Any())
                            sku_id = item.sku_list[0].sku_id.ToString();
                        await modifySyncPriceTag(sku_id, item.item_id, "select");
                    }
                    else
                    {
                        string sku_id = "";
                        if (item.sku_list != null && item.sku_list.Any())
                            sku_id = item.sku_list[0].sku_id.ToString();
                        var errorJson = res.message;
                        errorData.Add(errorJson);
                        fail++;
                        await modifySyncPriceTag(sku_id, item.item_id, "update-error", res.message);
                    }
                }
                catch (Exception ex)
                {
                    errorData.Add(ex.Message);
                }
            }
            page_index++;
        }
        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }

    #region 单元测试

    /// <summary>
    /// 获取批量商家编码的商品列表
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("getBatch")]
    public async Task<dynamic> getBatchProduct()
    {
        var request = new getBatchProductRequest();
        request.item_no_list.Add("10010001");
        var token = await _youzanService.GetTokenAsync();
        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.item.custom.batch.get/1.0.0";
            param.method = "POST";
            param.body = request.ToJsonString();
            var res = await _youzanService.GetData(param);
            if (res.success == true)
            {
                var jsonData = res.data.ToJsonString();
                var result = res.data.ToObject<batchProcuctResponse>();

            }
            return "";
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    /// <summary>
    /// 获取商品详情
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("getDetail")]
    public async Task<dynamic> getDetailProduct()
    {
        var request = new getDetailProductRequest();
        request.item_id = "3462231329";
        var token = await _youzanService.GetTokenAsync();
        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.item.detail.get/1.0.0";
            param.method = "POST";
            param.body = request.ToJsonString();
            var res = await _youzanService.GetData(param);
            if (res.success == true)
            {
                var product_sku_list = new List<productSkuEntity>();
                var jsonData = res.data.ToJsonString();
                var result = res.data.ToObject<detailProdctResponse>();
                // 判断 无规格/多规格
                if (result.sku_list.Count > 0)
                {
                    foreach (var item in result.sku_list)
                    {

                        product_sku_list.Add(new productSkuEntity()
                        {
                            id = item.sku_id,
                            product_id = result.item_id,
                            sale_price = item.price,
                            cost_price = item.cost_price,
                            make_price = item.min_guide_price,
                            sku_sn = item.sku_no,
                            sku_state = 1
                        });
                    }

                    // await crateEquity(list);
                }
            }
            return res.message;
        }
        catch (Exception ex)
        {

            throw;
        }
    }
    #endregion

    #region Private Method

    #region  商品

    /// <summary>
    /// 获取有赞商品多规格SKU - 单个商品使用批量方法优化实现
    /// </summary>
    /// <param name="item_id">商品ID</param>
    /// <param name="channel">渠道</param>
    /// <returns>SKU列表</returns>
    private async Task<List<productSkuEntity>> getYzProductSkuList(string item_id, int channel)
    {
        // 参数校验
        if (string.IsNullOrEmpty(item_id))
        {
            Log.Warning("获取SKU信息时商品ID为空");
            return new List<productSkuEntity>();
        }

        try
        {
            // 调用批量方法获取单个商品的SKU信息
            var skuDict = await getBatchYzProductSkuList(new List<string> { item_id }, channel.ToString());

            // 从结果字典中获取该商品的SKU列表
            if (skuDict != null && skuDict.TryGetValue(item_id, out var skuList) && skuList != null)
            {
                return skuList;
            }

            // 如果批量方法未能获取到，则回退到原始的单品获取逻辑
            var request = new getDetailProductRequest();
            request.item_id = item_id;

            var param = new YouzanParameter();
            param.url = "youzan.item.detail.get/1.0.0";
            param.method = "POST";
            param.body = request.ToJsonString();
            var res = await _youzanService.GetData(param);

            if (res.success && res.data != null)
            {
                var product_sku_list = new List<productSkuEntity>();
                var result = res.data.ToObject<detailProdctResponse>();

                // 判断 无规格/多规格
                if (result.sku_list != null && result.sku_list.Count > 0)
                {
                    foreach (var item in result.sku_list)
                    {
                        product_sku_list.Add(new productSkuEntity()
                        {
                            id = item.sku_id,
                            product_id = result.item_id,
                            sale_price = item.price,
                            cost_price = item.price,
                            make_price = item.price.ObjToDecimal(),
                            sku_sn = item.sku_no,
                            sku_state = 1,
                            channel = channel,
                            sku_name = string.Join(" ", item.sku_props?.Select(x => x.prop_value_name) ?? new List<string>())
                        });
                    }
                    return product_sku_list;
                }
                else if (!string.IsNullOrEmpty(result.item_no))
                {
                    // 调用无规格商品的单个获取方法
                    // 这与原始方法getYzProductSkuList的逻辑保持一致
                    return await getYzNoProductSkuList(result.item_no, channel);
                }
            }

            Log.Warning($"获取商品 {item_id} 的SKU信息未成功，响应消息：{res?.message ?? "无响应"}");
            return new List<productSkuEntity>();
        }
        catch (Exception ex)
        {
            Log.Error($"获取商品 {item_id} 的SKU信息时出错: {ex.Message}, 异常类型: {ex.GetType().Name}, 堆栈: {ex.StackTrace}");
            throw new ApplicationException($"处理商品 {item_id} 的SKU信息时失败", ex);
        }
    }

    /// <summary>
    /// 获取有赞商品多规格SKU - 支持批量处理
    /// </summary>
    /// <param name="item_ids">商品ID列表</param>
    /// <param name="channel">渠道</param>
    /// <returns>每个商品的SKU列表字典</returns>
    private async Task<Dictionary<string, List<productSkuEntity>>> getYzProductSkuBatchList(List<string> item_ids, int channel)
    {
        if (item_ids == null || !item_ids.Any())
            return new Dictionary<string, List<productSkuEntity>>();

        var results = new Dictionary<string, List<productSkuEntity>>();
        var itemNoToItemIdMap = new Dictionary<string, string>(); // 用于映射item_no到item_id

        // 首先尝试获取每个商品的详情，以确定是否为多规格商品
        foreach (var item_id in item_ids)
        {
            try
            {
                var request = new getDetailProductRequest();
                request.item_id = item_id;

                var param = new YouzanParameter();
                param.url = "youzan.item.detail.get/1.0.0";
                param.method = "POST";
                param.body = request.ToJsonString();
                var res = await _youzanService.GetData(param);

                if (res.success == true)
                {
                    var result = res.data.ToObject<detailProdctResponse>();

                    // 如果是多规格商品，处理SKU列表
                    if (result.sku_list != null && result.sku_list.Count > 0)
                    {
                        var product_sku_list = new List<productSkuEntity>();
                        foreach (var item in result.sku_list)
                        {
                            product_sku_list.Add(new productSkuEntity()
                            {
                                id = item.sku_id,
                                product_id = result.item_id,
                                sale_price = item.price,
                                cost_price = item.price,
                                make_price = item.price.ObjToDecimal(),
                                sku_sn = item.sku_no,
                                sku_state = 1,
                                channel = channel,
                                sku_name = string.Join(" ", item.sku_props?.Select(x => x.prop_value_name) ?? new List<string>())
                            });
                        }

                        results[item_id] = product_sku_list;
                    }
                    else
                    {
                        // 对于无规格商品，调用getYzNoProductSkuList方法获取SKU信息
                        // 这与原始方法getYzProductSkuList的逻辑保持一致
                        if (!string.IsNullOrEmpty(result.item_no))
                        {
                            try
                            {
                                // 调用原有的无规格商品获取方法
                                var noSkuList = await getYzNoProductSkuList(result.item_no, channel);
                                if (noSkuList != null && noSkuList.Any())
                                {
                                    // 确保product_id使用原始的item_id
                                    foreach (var sku in noSkuList)
                                    {
                                        sku.product_id = item_id;
                                    }
                                    results[item_id] = noSkuList;
                                }
                            }
                            catch (Exception ex)
                            {
                                Log.Information($"获取无规格商品 {item_id} 的SKU时出错: {ex.Message}");
                            }
                        }
                        else
                        {
                            Log.Information($"商品 {item_id} 没有item_no，无法处理无规格商品");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Information($"获取商品 {item_id} 的详情时出错: {ex.Message}");
            }
        }

        return results;
    }

    /// <summary>
    /// 获取有赞商品无规格SKU
    /// </summary>
    /// <returns></returns>
    private async Task<List<productSkuEntity>> getYzNoProductSkuList(string item_no, int channel)
    {
        Log.Information($"开始获取无规格商品SKU - item_no: {item_no}, channel: {channel}");

        var request = new getBatchProductRequest();
        request.item_no_list.Add(item_no);
        request.channel = channel;
        var token = await _youzanService.GetTokenAsync();
        Log.Information($"获取有赞Token成功，准备调用无规格SKU API - item_no: {item_no}");

        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.item.custom.batch.get/1.0.0";
            param.method = "POST";
            param.body = request.ToJsonString();
            Log.Information($"请求参数序列化成功，调用无规格SKU API - item_no: {item_no}");

            var res = await _youzanService.GetData(param);
            Log.Information($"无规格SKU API调用完成 - item_no: {item_no}, 成功: {res.success}");

            if (res.success == true)
            {
                var product_sku_list = new List<productSkuEntity>();
                var result = res.data.ToObject<batchProcuctResponse>();

                // 检查返回的items是否为空或为空集合
                if (result.items == null || result.items.Count == 0)
                {
                    Log.Warning($"获取商品SKU信息时，API返回了空的items集合 - item_no: {item_no}");
                    return new List<productSkuEntity>();
                }

                var product_info = result.items[0];
                var sku_id = SnowflakeIdHelper.NextId();
                Log.Information($"获取到商品信息 - item_no: {item_no}, item_id: {product_info.item_id}, title: {product_info.title}");

                // 添加对skuExtensionAttributes的null检查
                if (product_info.skuExtensionAttributes != null && product_info.skuExtensionAttributes.Count > 0)
                {
                    sku_id = product_info.skuExtensionAttributes[0].sku_id;
                    Log.Information($"使用扩展属性中的SKU ID - item_no: {item_no}, item_id: {product_info.item_id}, sku_id: {sku_id}");
                }
                else
                {
                    Log.Information($"使用生成的SKU ID - item_no: {item_no}, item_id: {product_info.item_id}, sku_id: {sku_id}");
                }

                product_sku_list.Add(new productSkuEntity()
                {
                    id = sku_id,
                    product_id = product_info.item_id,
                    sale_price = product_info.price,
                    cost_price = product_info.price,
                    make_price = product_info.price.ObjToDecimal(),
                    sku_sn = product_info.item_no,
                    sku_state = 1,
                    channel = channel,
                    sku_name = product_info.title
                });

                Log.Information($"成功创建无规格SKU - item_no: {item_no}, item_id: {product_info.item_id}, sku_id: {sku_id}, price: {product_info.price}");
                return product_sku_list;
            }

            Log.Warning($"获取商品SKU信息请求成功，但未返回成功标识 - item_no: {item_no}");
            return new List<productSkuEntity>();
        }
        catch (Exception ex)
        {
            Log.Error($"获取商品 {item_no} 的SKU信息时出错: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 获取有赞商品无规格SKU - 支持批量处理
    /// </summary>
    /// <param name="item_no_list">商品编码列表</param>
    /// <param name="channel">渠道</param>
    /// <returns>商品的SKU列表</returns>
    private async Task<List<productSkuEntity>> getYzNoProductSkuList(List<string> item_no_list, int channel)
    {
        if (item_no_list == null || !item_no_list.Any())
            return new List<productSkuEntity>();

        var request = new getBatchProductRequest();
        request.item_no_list.AddRange(item_no_list);
        request.channel = channel;
        var token = await _youzanService.GetTokenAsync();

        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.item.custom.batch.get/1.0.0";
            param.method = "POST";
            param.body = request.ToJsonString();
            var res = await _youzanService.GetData(param);

            if (res.success == true)
            {
                var product_sku_list = new List<productSkuEntity>();
                var result = res.data.ToObject<batchProcuctResponse>();

                // 检查返回的items是否为空或为空集合
                if (result.items == null || result.items.Count == 0)
                {
                    Log.Warning($"批量获取商品SKU信息时，API返回了空的items集合，请求包含 {item_no_list.Count} 个商品编码");
                    return new List<productSkuEntity>();
                }

                foreach (var product_info in result.items)
                {
                    var sku_id = SnowflakeIdHelper.NextId();

                    // 添加对skuExtensionAttributes的null检查
                    if (product_info.skuExtensionAttributes != null && product_info.skuExtensionAttributes.Count > 0)
                        sku_id = product_info.skuExtensionAttributes[0].sku_id;

                    product_sku_list.Add(new productSkuEntity()
                    {
                        id = sku_id,
                        product_id = product_info.item_id,
                        sale_price = product_info.price,
                        cost_price = product_info.price,
                        make_price = product_info.price.ObjToDecimal(),
                        sku_sn = product_info.item_no,
                        sku_state = 1,
                        channel = channel,
                        sku_name = product_info.title
                    });
                }

                Log.Information($"批量获取无规格SKU成功，处理了 {result.items.Count} 个商品");
                return product_sku_list;
            }

            Log.Warning($"批量获取无规格SKU请求成功，但未返回商品数据");
            return new List<productSkuEntity>();
        }
        catch (Exception ex)
        {
            Log.Error($"批量获取无规格SKU时出错: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 批量获取多个商品的SKU信息
    /// </summary>
    /// <param name="itemIdList">商品ID列表</param>
    /// <param name="channel">渠道</param>
    /// <returns>以商品ID为键，SKU列表为值的字典</returns>
    private async Task<Dictionary<string, List<productSkuEntity>>> getBatchYzProductSkuList(List<string> itemIdList, string channel)
    {
        Log.Information($"开始批量获取商品SKU信息 - 商品数量: {itemIdList?.Count ?? 0}, channel: {channel}");

        var result = new Dictionary<string, List<productSkuEntity>>();

        try
        {
            // 检查输入列表
            if (itemIdList == null || itemIdList.Count == 0)
            {
                Log.Warning("getBatchYzProductSkuList批量获取SKU信息时，商品ID列表为空");
                return result;
            }

            // 记录开始批量查询SKU
            Log.Information($"getBatchYzProductSkuList开始批量获取商品的SKU信息，商品数量: {itemIdList.Count}, channel: {channel}");

            // 收集所有需要批量处理的无规格商品的item_no
            var multiSkuItems = new List<string>(); // 有规格商品ID列表
            var noSkuItemMap = new Dictionary<string, string>(); // 无规格商品的item_no到item_id的映射

            // 第一步：将商品分为多规格和无规格两组
            Log.Information($"getBatchYzProductSkuList开始分析商品规格类型，总数: {itemIdList.Count}");
            foreach (var itemId in itemIdList)
            {
                try
                {
                    Log.Information($"getBatchYzProductSkuList获取商品详情 - item_id: {itemId}");

                    var request = new getDetailProductRequest();
                    request.item_id = itemId;

                    var param = new YouzanParameter();
                    param.url = "youzan.item.detail.get/1.0.0";
                    param.method = "POST";
                    param.body = request.ToJsonString();

                    var res = await _youzanService.GetData(param);

                    if (!res.success)
                    {
                        Log.Warning($"getBatchYzProductSkuList获取商品详情失败 - item_id: {itemId}, 错误: {res.message}");
                        continue;
                    }

                    // 校验返回数据
                    if (res.data == null)
                    {
                        Log.Warning($"getBatchYzProductSkuList获取商品详情成功，但数据为空 - item_id: {itemId}");
                        continue;
                    }

                    try
                    {
                        // 解析响应数据，使用与getYzProductSkuList方法相同的逻辑
                        var resultData = res.data.ToObject<detailProdctResponse>();
                        Log.Information($"getBatchYzProductSkuList成功解析商品详情 - item_id: {itemId}, item_no: {resultData.item_no}");

                        // 判断 无规格/多规格
                        if (resultData.sku_list != null && resultData.sku_list.Count > 0)
                        {
                            // 多规格商品，直接处理
                            Log.Information($"getBatchYzProductSkuList检测到多规格商品 - item_id: {itemId}, SKU数量: {resultData.sku_list.Count}");
                            multiSkuItems.Add(itemId);

                            var product_sku_list = new List<productSkuEntity>();
                            foreach (var item in resultData.sku_list)
                            {
                                var skuName = string.Join(" ", item.sku_props?.Select(x => x.prop_value_name) ?? new List<string>());
                                product_sku_list.Add(new productSkuEntity()
                                {
                                    id = item.sku_id,
                                    product_id = resultData.item_id,
                                    sale_price = item.price,
                                    cost_price = item.price,
                                    make_price = item.price.ObjToDecimal(),
                                    sku_sn = item.sku_no,
                                    sku_state = 1,
                                    channel = int.Parse(channel),
                                    sku_name = skuName
                                });
                                Log.Information($"getBatchYzProductSkuList创建多规格SKU - item_id: {itemId}, sku_id: {item.sku_id}, sku_no: {item.sku_no}, price: {item.price}");
                            }

                            result[itemId] = product_sku_list;
                            Log.Information($"getBatchYzProductSkuList成功获取商品多规格SKU信息 - item_id: {itemId}, 数量: {product_sku_list.Count}");
                        }
                        else
                        {
                            // 对于无规格商品，先收集所有的商品编码，稍后一次性批量获取
                            Log.Information($"getBatchYzProductSkuList检测到无规格商品 - item_id: {itemId}, item_no: {resultData.item_no}");
                            if (!string.IsNullOrEmpty(resultData.item_no))
                            {
                                noSkuItemMap[resultData.item_no] = itemId;
                                Log.Information($"getBatchYzProductSkuList无规格商品已加入批量处理队列 - item_id: {itemId}, item_no: {resultData.item_no}");
                            }
                            else
                            {
                                Log.Warning($"getBatchYzProductSkuList商品没有item_no，无法处理无规格商品 - item_id: {itemId}");
                            }
                        }
                    }
                    catch (Exception parseEx)
                    {
                        Log.Error($"getBatchYzProductSkuList解析商品SKU返回数据时出错 - item_id: {itemId}, 错误: {parseEx.Message}");
                    }
                }
                catch (Exception itemEx)
                {
                    Log.Error($"getBatchYzProductSkuList处理商品SKU信息时出错 - item_id: {itemId}, 错误: {itemEx.Message}");
                }
            }

            // 第二步：批量处理所有无规格商品
            if (noSkuItemMap.Count > 0)
            {
                Log.Information($"getBatchYzProductSkuList开始批量处理无规格商品 - 数量: {noSkuItemMap.Count}");
                try
                {
                    // 获取所有无规格商品的编码列表
                    var noSkuItemCodes = noSkuItemMap.Keys.ToList();
                    Log.Information($"getBatchYzProductSkuList开始批量获取{noSkuItemCodes.Count}个无规格商品的SKU信息");

                    // 调用批量处理方法一次性获取所有无规格商品的SKU
                    var allNoSkuList = await getYzNoProductSkuList(noSkuItemCodes, int.Parse(channel));
                    Log.Information($"getBatchYzProductSkuList批量获取无规格SKU完成 - 返回SKU数量: {allNoSkuList?.Count ?? 0}");

                    if (allNoSkuList != null && allNoSkuList.Any())
                    {
                        // 根据item_no将SKU分配到对应的商品ID下
                        Log.Information($"getBatchYzProductSkuList开始分配无规格SKU到对应商品");
                        foreach (var skuItem in allNoSkuList)
                        {
                            // 查找对应的商品ID
                            var matchingItemIds = noSkuItemMap.Where(kv => kv.Key == skuItem.sku_sn)
                                                           .Select(kv => kv.Value)
                                                           .ToList();

                            if (matchingItemIds.Any())
                            {
                                Log.Information($"getBatchYzProductSkuList找到匹配的商品ID - sku_sn: {skuItem.sku_sn}, 匹配数量: {matchingItemIds.Count}");
                                foreach (var matchId in matchingItemIds)
                                {
                                    // 创建该SKU的副本，确保每个商品ID有独立的SKU对象
                                    var skuCopy = new productSkuEntity
                                    {
                                        id = skuItem.id,
                                        product_id = matchId, // 使用对应的商品ID
                                        sale_price = skuItem.sale_price,
                                        cost_price = skuItem.cost_price,
                                        make_price = skuItem.make_price,
                                        sku_sn = skuItem.sku_sn,
                                        sku_state = skuItem.sku_state,
                                        channel = skuItem.channel,
                                        sku_name = skuItem.sku_name
                                    };

                                    // 将SKU添加到结果字典中
                                    if (!result.ContainsKey(matchId))
                                    {
                                        result[matchId] = new List<productSkuEntity>();
                                    }

                                    result[matchId].Add(skuCopy);
                                    Log.Information($"getBatchYzProductSkuList成功将无规格SKU匹配到商品 - item_id: {matchId}, sku_sn: {skuItem.sku_sn}, sku_id: {skuItem.id}");
                                }
                            }
                            else
                            {
                                Log.Warning($"getBatchYzProductSkuList无法找到无规格SKU对应的商品ID - sku_sn: {skuItem.sku_sn}");
                            }
                        }
                    }
                    else
                    {
                        Log.Warning("getBatchYzProductSkuList批量获取无规格商品SKU信息时返回了空结果");
                    }
                }
                catch (Exception batchEx)
                {
                    Log.Error($"getBatchYzProductSkuList批量获取无规格商品SKU时出错: {batchEx.Message}");
                }
            }
            else
            {
                Log.Information($"getBatchYzProductSkuList没有无规格商品需要批量处理");
            }

            Log.Information($"getBatchYzProductSkuList批量获取所有商品SKU完成，成功处理 {result.Count} 个商品的SKU信息");

            // 记录每个商品的SKU数量统计
            foreach (var kvp in result)
            {
                Log.Information($"getBatchYzProductSkuList商品SKU统计 - item_id: {kvp.Key}, SKU数量: {kvp.Value.Count}");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"getBatchYzProductSkuList批量获取SKU信息过程中出错: {ex.Message}");
            Log.Error($"getBatchYzProductSkuList错误堆栈: {ex.StackTrace}");
        }

        return result;
    }

    /// <summary>
    /// 同步商品记录
    /// </summary>
    /// <returns></returns>
    private async Task createProduct(productEntity product, List<productSkuEntity> prpdouct_sku_list)
    {
        try
        {
            // 开启事务
            await _db.BeginTranAsync();
            // 插入商品
            await _repository.Insertable(product).ExecuteCommandAsync();
            // 插入商品sku
            await _repository.Insertable(prpdouct_sku_list).ExecuteCommandAsync();
            // 提交事务
            await _db.CommitTranAsync();
        }
        catch (Exception)
        {
            await _db.RollbackTranAsync();
        }

    }

    /// <summary>
    /// 批量同步商品记录，提高处理效率
    /// </summary>
    /// <param name="products">商品列表</param>
    /// <param name="productSkus">商品SKU列表</param>
    /// <returns></returns>
    private async Task batchCreateProducts(List<productEntity> products, List<productSkuEntity> productSkus)
    {
        // 参数校验
        if (products == null || !products.Any())
        {
            Log.Warning("批量创建商品时商品列表为空");
            return;
        }

        // 性能优化：只在有数据时才开启事务
        var productsCount = products?.Count ?? 0;
        var productSkusCount = productSkus?.Count ?? 0;
        if (productsCount == 0 && productSkusCount == 0)
        {
            Log.Information("批量创建商品时没有任何数据需要处理");
            return;
        }

        try
        {
            // 记录开始处理
            Log.Information($"开始批量插入 {productsCount} 个商品和 {productSkusCount} 个SKU");

            // 开启事务
            await _db.BeginTranAsync();

            // 批量插入商品 - 分批处理以提高性能
            // 使用类级别的批处理大小常量

            // 分批处理商品
            for (int i = 0; i < productsCount; i += BATCH_SIZE)
            {
                int currentBatchSize = Math.Min(BATCH_SIZE, productsCount - i);
                var batchProducts = products.GetRange(i, currentBatchSize);
                await _repository.Insertable(batchProducts).ExecuteCommandAsync();
                Log.Information($"已插入商品批次 {i / BATCH_SIZE + 1}/{Math.Ceiling(productsCount / (double)BATCH_SIZE)}，本批次 {batchProducts.Count} 条");
            }

            // 批量插入商品sku
            if (productSkus != null && productSkus.Any())
            {
                // 分批处理SKU
                for (int i = 0; i < productSkusCount; i += BATCH_SIZE)
                {
                    int currentBatchSize = Math.Min(BATCH_SIZE, productSkusCount - i);
                    var batchSkus = productSkus.GetRange(i, currentBatchSize);
                    await _repository.Insertable(batchSkus).ExecuteCommandAsync();
                    Log.Information($"已插入SKU批次 {i / BATCH_SIZE + 1}/{Math.Ceiling(productSkusCount / (double)BATCH_SIZE)}，本批次 {batchSkus.Count} 条");
                }
            }

            // 提交事务
            await _db.CommitTranAsync();
            Log.Information($"批量插入完成：{productsCount} 个商品和 {productSkusCount} 个SKU");
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            Log.Error($"批量插入商品失败: {ex.Message}, 异常类型: {ex.GetType().Name}, 堆栈: {ex.StackTrace}");
            throw new ApplicationException($"批量插入 {productsCount} 个商品和 {productSkusCount} 个SKU失败", ex);
        }
    }

    /// <summary>
    /// 删除商品记录及相关SKU数据
    /// </summary>
    /// <param name="product_id">商品编号</param>
    /// <returns>删除是否成功</returns>
    private async Task<bool> removeProduct(string product_id)
    {
        // 参数校验
        if (string.IsNullOrEmpty(product_id))
        {
            Log.Warning("尝试删除商品时商品ID为空");
            return false;
        }

        // 开启数据库事务，确保数据一致性
        await _db.BeginTranAsync();
        try
        {
            Log.Information($"开始删除商品ID: {product_id}");

            // 1. 删除商品主表数据
            // 根据商品ID删除productEntity表中的记录
            var productDeleted = await _repository.Deleteable<productEntity>()
                .Where(x => x.product_id == product_id)
                .ExecuteCommandAsync();

            // 2. 删除商品SKU表数据
            // 根据商品ID删除所有关联的SKU记录
            var skuDeleted = await _repository.Deleteable<productSkuEntity>()
                .Where(x => x.product_id == product_id)
                .ExecuteCommandAsync();

            // 3. 提交事务
            await _db.CommitTranAsync();

            // 4. 从缓存中删除商品信息
            await _cache.DelAsync($"product:{product_id}");

            Log.Information($"成功删除商品 {product_id}，删除了 {productDeleted} 条商品记录和 {skuDeleted} 条SKU记录");
            return true;
        }
        catch (Exception ex)
        {
            // 发生异常时回滚事务
            await _db.RollbackTranAsync();
            Log.Error($"删除商品 {product_id} 失败: {ex.Message}, 异常类型: {ex.GetType().Name}");
            return false;
        }
    }

    // 使用Redis缓存商品信息
    private async Task<productEntity> GetProductFromCache(string productId)
    {
        // 参数校验
        if (string.IsNullOrEmpty(productId))
        {
            Log.Warning("获取商品缓存时商品ID为空");
            return null;
        }

        // 构建缓存key
        var cacheKey = $"product:{productId}";

        // 首先尝试从Redis缓存获取数据
        var product = await _cache.GetAsync<productEntity>(cacheKey);

        // 如果缓存中没有数据
        if (product == null)
        {
            // 从数据库查询商品数据
            product = await _repository.Queryable<productEntity>()
                .Where(x => x.product_id == productId)
                .FirstAsync();

            // 如果数据库中存在该商品
            if (product != null)
            {
                // 动态设置缓存过期时间：最近修改的商品缓存时间较短，老商品缓存更久
                TimeSpan cacheTime = GetOptimalCacheTime(product);

                // 将查询结果存入Redis缓存
                await _cache.SetAsync(cacheKey, product, cacheTime);
                Log.Debug($"商品 {productId} 已加入缓存，过期时间: {cacheTime.TotalMinutes} 分钟");
            }
        }
        return product;
    }

    // 批量获取商品信息（使用Redis缓存）
    private async Task<Dictionary<string, productEntity>> GetProductsFromCache(List<string> productIds)
    {
        // 参数校验
        if (productIds == null || !productIds.Any())
        {
            return new Dictionary<string, productEntity>();
        }

        var result = new Dictionary<string, productEntity>();
        var uncachedIds = new List<string>();

        // 尝试从缓存获取
        foreach (var id in productIds)
        {
            if (string.IsNullOrEmpty(id))
                continue;

            var cacheKey = $"product:{id}";
            var product = await _cache.GetAsync<productEntity>(cacheKey);
            if (product != null)
            {
                result[id] = product;
            }
            else
            {
                uncachedIds.Add(id);
            }
        }

        // 查询未缓存的商品 - 分批处理大量ID以减轻数据库压力
        if (uncachedIds.Any())
        {
            // 使用类级别的批处理大小常量

            for (int i = 0; i < uncachedIds.Count; i += BATCH_SIZE)
            {
                // 获取当前批次的ID
                int currentBatchSize = Math.Min(BATCH_SIZE, uncachedIds.Count - i);
                var batchIds = uncachedIds.GetRange(i, currentBatchSize);

                // 查询数据库
                var batchProducts = await _repository.Queryable<productEntity>()
                    .Where(x => batchIds.Contains(x.product_id))
                    .ToListAsync();

                Log.Information($"查询数据库获取商品信息，批次：{i / BATCH_SIZE + 1}，查询ID数：{batchIds.Count}，返回结果数：{batchProducts.Count}");

                // 将结果添加到字典并缓存
                foreach (var product in batchProducts)
                {
                    var cacheKey = $"product:{product.product_id}";
                    TimeSpan cacheTime = GetOptimalCacheTime(product);
                    await _cache.SetAsync(cacheKey, product, cacheTime);
                    result[product.product_id] = product;
                }
            }
        }

        return result;
    }

    #endregion

    #region 库存

    /// <summary>
    /// 获取商品库存列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<List<updateProductStoreRequest>> getPageUpdateStoreStockList(productStoreQuery query)
    {
        var stockList = new List<updateProductStoreRequest>();
        var pageData = await _repository.Queryable<productStockEntity, shopEntity>((a, b) => new JoinQueryInfos(JoinType.Left, a.store_id == b.id))
            .Where((a, b) => a.tag_status == query.tag_status)
            .Select((a, b) => new updateProducStockDto
            {
                product_id = a.product_id,
                sku_id = a.sku_id,
                store_id = b.net_source_no,
                stock = a.stock,
                has_sku = a.has_sku
            }).ToPagedListAsync(query.currentPage, query.pageSize);
        foreach (var item in pageData.list)
        {
            var model = new updateProductStoreRequest();
            model.root_item_id = item.product_id;
            // 无 sku
            model.no_sku_update_open_param_list = item.has_sku == 0 ? new List<noSkuList>
                {
                    new noSkuList() {  node_kdt_id=item.store_id, stock_num=item.stock.ParseToLong() }
                } : null;
            // 多 sku 
            model.multi_sku_update_open_param_list = item.has_sku == 1 ? new List<multiSkuList>
                {
                    new multiSkuList()
                    {
                        root_sku_id=item.sku_id,
                        update_price_stock_open_param_list=new  List<multiSkuList.updateStockList> (){

                 new updateStockList() {  node_kdt_id=item.store_id, stock_num=item.stock.ParseToLong() }
                  }
                    }
                } : null;
            stockList.Add(model);
        }
        return stockList;
    }


    /// <summary>
    ///  修改库存同步标记
    /// </summary>
    /// <param name="product_id">商品id</param>
    /// <param name="sku_id">规格id</param>
    /// <param name="store_id">门店id</param>
    /// <param name="tag_status">同步状态</param>
    /// <param name="tag_body">内容</param>
    /// <returns></returns>
    private async Task modifySyncStoreStock(long product_id, long sku_id, string store_id, string tag_status = "select", string tag_body = "")
    {
        // 安全检查：确保product_id有效，避免无条件的全表更新
        if (product_id <= 0)
        {
            Console.WriteLine("警告: 无效的product_id，已阻止可能的全表更新操作");
            return;
        }

        // 门店信息 - 使用缓存获取
        var shop_info = await _shopCacheService.GetShopFromCache(store_id);
        if (shop_info == null)
            return;

        // 直接使用SetColumns批量更新
        var updateable = _repository.Updateable<productStockEntity>()
            .SetColumns(it => new productStockEntity
            {
                tag_status = tag_status,
                modify_date = DateTime.Now,
                tag_body = tag_body
            })
            .Where(x => x.product_id == product_id);

        // 如果sku_id有效，添加到条件中    
        if (sku_id > 0)
        {
            updateable = updateable.Where(x => x.sku_id == sku_id);
        }

        // 添加店铺条件
        updateable = updateable.Where(x => x.store_id == shop_info.store_no);

        await updateable.ExecuteCommandAsync();
    }

    #endregion

    #region 价格

    /// <summary>
    /// 获取商品单价列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<List<updateProducPriceRequest>> getPageUpdateProductPriceList(productPriceQuery query)
    {
        var priceList = new List<updateProducPriceRequest>();
        var pageData = await _repository.Queryable<productSkuShopPriceEntity>()
       .Where(a => a.tag_status == query.tag_status)
       .Select((a) => new updateProducPriceDto
       {
           product_id = a.product_id,
           sku_id = a.sku_id,
           has_sku = a.has_sku,
           sale_price = a.sale_price
       }).ToPagedListAsync(query.currentPage, query.pageSize);
        foreach (var item in pageData.list)
        {
            var model = new updateProducPriceRequest();
            model.item_id = item.product_id;

            // 构造基础价格参数
            var basePrice = new NoSukPriceParam
            {
                price = item.sale_price,
                origin = (item.sale_price / 100.0).ToString("0.00")
            };

            if (item.has_sku == 1)
            {
                // 多规格商品
                model.sku_list = new List<SkuPriceParam>
                {
                    new SkuPriceParam() {
                        price = item.sale_price,
                        sku_id = item.sku_id
                    }
                };
            }

            // 设置默认价格参数
            model.item_price_param = basePrice;

            // 设置多渠道价格参数
            model.item_channel_params = new List<ItemChannelParam> {
                    new ItemChannelParam {
                        channel = 0,
                    item_price_param = basePrice
                    },
                    new ItemChannelParam {
                        channel = 1,
                    item_price_param = basePrice
                }
            };

            priceList.Add(model);
        }
        return priceList;
    }

    /// <summary>
    /// 更新同步价格标记
    /// </summary>
    /// <param name="sku_id">规格id</param>
    /// <param name="tag_status">标记状态</param>
    /// <param name="tag_body">标记内容</param>
    /// <returns></returns>

    /// <summary>
    /// 更新同步价格标记
    /// </summary>
    /// <param name="sku_id">规格id</param>
    /// <param name="tag_status">标记状态</param>
    /// <param name="tag_body">标记内容</param>
    /// <returns></returns>
    private async Task modifySyncPriceTag(string sku_id, long product_id, string tag_status = "select", string tag_body = "")
    {
        // 安全检查：确保至少有一个有效的标识符，避免无条件的全表更新
        if (string.IsNullOrEmpty(sku_id) && product_id <= 0)
        {
            Console.WriteLine("警告: 无效的sku_id和product_id，已阻止可能的全表更新操作");
            return;
        }

        var query = _repository.Queryable<productSkuShopPriceEntity>();

        // 添加查询条件，确保至少有一个条件
        if (!string.IsNullOrEmpty(sku_id))
        {
            long sku_id_long;
            if (long.TryParse(sku_id, out sku_id_long))
            {
                query = query.Where(x => x.sku_id == sku_id_long);
            }
        }

        if (product_id > 0)
        {
            query = query.Where(x => x.product_id == product_id);
        }

        var sku_price_info = await query.FirstAsync();

        // 添加空值检查，避免数组越界异常
        if (sku_price_info == null)
        {
            // 记录日志或处理未找到价格信息的情况
            Console.WriteLine($"未找到价格信息。SKU ID: {sku_id}, 商品ID: {product_id}");
            return;
        }

        try
        {
            // 开启事务
            await _db.BeginTranAsync();
            // 更新价格表
            sku_price_info.tag_status = tag_status;
            sku_price_info.modify_date = DateTime.Now;
            sku_price_info.tag_body = tag_body;
            await _repository.Updateable<productSkuShopPriceEntity>(sku_price_info).ExecuteCommandAsync();

            // 只有在sku_id为空，无规格商品才更新pdt_product表价格
            if (string.IsNullOrEmpty(sku_id) && !string.IsNullOrEmpty(sku_price_info.PLUCODE))
            {
                await _repository.Updateable<productEntity>()
                    .SetColumns(it => new productEntity()
                    {
                        make_price = sku_price_info.sale_price
                    })
                    .Where(x => x.bar_code == sku_price_info.PLUCODE)
                    .ExecuteCommandAsync();
            }

            // 更新sku表价格
            if (!string.IsNullOrEmpty(sku_price_info.PLUCODE))
            {
                // 不再需要尝试转换为long，因为现在实体使用string类型
                await _repository.Updateable<productSkuEntity>()
                    .SetColumns(it => new productSkuEntity()
                    {
                        sale_price = sku_price_info.sale_price,
                        cost_price = sku_price_info.sale_price,
                        make_price = sku_price_info.sale_price.ObjToDecimal()
                    })
                    .Where(x => x.sku_sn == sku_price_info.PLUCODE)
                    .ExecuteCommandAsync();
            }

            // 提交事务
            await _db.CommitTranAsync();
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            // 记录具体异常信息，帮助诊断问题
            Console.WriteLine($"modifySyncPriceTag错误: {ex.Message}, StackTrace: {ex.StackTrace}");
            throw;
        }
    }



    #endregion

    #endregion

    /// <summary>
    /// 计算商品的最佳缓存时间
    /// </summary>
    /// <param name="product">商品实体</param>
    /// <returns>缓存过期时间</returns>
    private TimeSpan GetOptimalCacheTime(productEntity product)
    {
        // 基础缓存时间（从配置获取）
        var baseCacheMinutes = _config.CacheExpirationMinutes;

        // 根据商品特性调整缓存时间
        var cacheMinutes = baseCacheMinutes;

        // 1. 根据商品更新频率调整
        if (product.modify_date.HasValue)
        {
            var daysSinceUpdate = (DateTime.Now - product.modify_date.Value).TotalDays;

            if (daysSinceUpdate > 30) // 超过30天未更新的商品，延长缓存时间
            {
                cacheMinutes = Math.Min(cacheMinutes * 3, 60); // 最多60分钟
            }
            else if (daysSinceUpdate < 1) // 最近1天内更新的商品，缩短缓存时间
            {
                cacheMinutes = Math.Max(cacheMinutes / 2, 1); // 最少1分钟
            }
        }

        // 2. 根据商品类型调整
        if (!string.IsNullOrEmpty(product.goods_type))
        {
            // 特殊商品类型可能需要更频繁的更新
            if (product.goods_type.Contains("限时") || product.goods_type.Contains("促销"))
            {
                cacheMinutes = Math.Max(cacheMinutes / 3, 1); // 促销商品缓存时间更短
            }
        }

        // 3. 根据商品状态调整
        if (product.state == 0) // 上架商品
        {
            // 上架商品保持正常缓存时间
        }
        else // 下架或其他状态商品
        {
            cacheMinutes = Math.Min(cacheMinutes * 2, 30); // 下架商品可以缓存更久
        }

        return TimeSpan.FromMinutes(cacheMinutes);
    }





    /// <summary>
    /// 补全只有单条记录的商品资料（缺少门店或网店版本）
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("completeIncompleteProducts")]
    public async Task<dynamic> CompleteIncompleteProductsApi()
    {
        try
        {
            await _compensationService.ExecuteProductCompletionAsync(async req => await ProcessProducts(req));
            return new { success = true, message = "商品补全完成" };
        }
        catch (Exception ex)
        {
            Log.Error($"API调用补全商品失败: {ex.Message}");
            return new { success = false, message = ex.Message };
        }
    }

    /// <summary>
    /// 补全只有单条记录的商品资料（可配置处理数量）
    /// </summary>
    /// <param name="request">请求参数，包含最大处理数量</param>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("completeIncompleteProductsWithLimit")]
    public async Task<dynamic> CompleteIncompleteProductsWithLimitApi([FromBody] CompleteProductsRequest request)
    {
        try
        {
            await _compensationService.ExecuteProductCompletionAsync(async req => await ProcessProducts(req));
            return new { success = true, message = $"商品补全完成，最大处理数量: {request?.maxCount ?? 100}" };
        }
        catch (Exception ex)
        {
            Log.Error($"API调用补全商品失败: {ex.Message}");
            return new { success = false, message = ex.Message };
        }
    }


}



