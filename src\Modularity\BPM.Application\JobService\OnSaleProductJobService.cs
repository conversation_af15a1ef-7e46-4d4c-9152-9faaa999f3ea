using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;
using BPM.Domain.Requests.product;

namespace BPM.Application;

/// <summary>
/// 本地任务-获取在售商品.
/// </summary>
[JobDetail("job_get_onsale_products", Description = "获取在售商品信息", GroupName = "BuiltIn", Concurrent = true)]
public class OnSaleProductJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 商品服务.
    /// </summary>
    private readonly ProductService _productService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public OnSaleProductJobService(IServiceScopeFactory serviceScopeFactory, ProductService productService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _productService = productService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var request = new getOnSaleProductRequest();
        var result = await _productService.getOnSaleProduct(request);
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 