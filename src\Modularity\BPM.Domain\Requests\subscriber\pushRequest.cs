﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.subscriber;

/// <summary>
///  推送请求.
/// </summary>
[SuppressSniffer]
public class pushRequest
{
    /// <summary>
    /// 对应开发者后台的app_id.
    /// </summary>
    public string app_id { get; set; }

    /// <summary>
    /// 对应开发者后台的client_id.
    /// </summary>
    public string client_id { get; set; }

    /// <summary>
    /// 消息标识.
    /// </summary>
    public string id { get; set; }

    /// <summary>
    /// 有赞用户id.
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 店铺id.
    /// </summary>
    public long kdt_id { get; set; }

    /// <summary>
    /// 店铺名称.
    /// </summary>
    public string kdt_name { get; set; }

    /// <summary>
    /// 信息体.
    /// </summary>
    public string msg { get; set; }

    /// <summary>
    /// 信息体id.
    /// </summary>
    public string msg_id { get; set; }

    /// <summary>
    /// 消息状态，对应消息业务类型。CUSTOMER_CARD_TAKEN-用户领取获得会员卡；CUSTOMER_CARD_BOUGHT-用户购买获得会员卡；CUSTOMER_CARD_UPGRADED-用户升级获得会员卡；CUSTOMER_CARD_GIVEN-用户通过商家发放获得会员卡；CUSTOMER_CARD_DELETED-用户删除会员卡；CUSTOMER_CARD_ACTIVATED-用户激活会员卡；CUSTOMER_CARD_SYNC_WECHAT-用户领卡到微信卡包；CUSTOMER_CARD_GRANTED_FROM_OPEN-三方通过开放平台发卡.
    /// </summary>
    public string status { get; set; }

    /// <summary>
    /// 消息类型.
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 消息版本号.
    /// </summary>
    public long version { get; set; } 
}
