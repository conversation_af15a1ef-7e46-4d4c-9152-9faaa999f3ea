<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BPM.Domain</name>
    </assembly>
    <members>
        <member name="T:BPM.Domain.Dto.customerCard.customerCardDto">
            <summary>
            客户会员卡信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.card_alias">
            <summary>
            会员卡别名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.card_no">
            <summary>
            用户领取到的会员卡卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.event_time">
            <summary>
            事件发生时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.fans_id">
            <summary>
            用户粉丝id
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.fans_type">
            <summary>
            用户粉丝类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.mobile">
            <summary>
            用户手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.user_id">
            <summary>
            用户的有赞userId
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardDto.yz_open_id">
            <summary>
            有赞openId
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.customerCard.customerCardItemDto">
            <summary>
            客户会员卡有效期信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardItemDto.page">
            <summary>
            当前页码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardItemDto.page_size">
            <summary>
            每页的最大记录条数
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardItemDto.items">
            <summary>
            权益卡集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.customerCardItemDto.total">
            <summary>
            会员拥有的权益卡总数
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.CardItem.card_start_time">
            <summary>
            有效期开始时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.CardItem.card_no">
            <summary>
            权益卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.CardItem.card_alias">
            <summary>
            权益卡别名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.CardItem.card_state">
            <summary>
            权益卡状态(0-未激活;1-使用中;2-已退款;3-已过期;4-用户已删除;5-商家已禁用;6-管理员删除;7-系统删除;8-未生效)
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customerCard.CardItem.card_end_time">
            <summary>
            有效期结束时间
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.customer.customerDto">
            <summary>
            客户信息 - API返回结构
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.account_id">
            <summary>
            帐号ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.ascription_kdt_id">
            <summary>
            所属门店
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.member_source_kdt_id">
            <summary>
            来源门店ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.cards">
            <summary>
            会员卡列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.gender">
            <summary>
            性别（0：未知；1：男；2：女）
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.source_channel">
            <summary>
            来源渠道
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.level_infos">
            <summary>
            等级信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.member_source_channel_desc">
            <summary>
            会员来源渠道描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.show_name">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.latest_nickname">
            <summary>
            最新昵称
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.latest_avatar">
            <summary>
            最新头像
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.wx_avatar">
            <summary>
            微信头像
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.mobile">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.created_at">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.member_created_at">
            <summary>
            会员创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.yz_open_id">
            <summary>
            有赞开放ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.county_name">
            <summary>
            区县名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.mobile_country_code">
            <summary>
            手机国家代码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.source_channel_desc">
            <summary>
            来源渠道描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.province_name">
            <summary>
            省份名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.tags">
            <summary>
            标签列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.areaCode">
            <summary>
            区域代码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.city_name">
            <summary>
            城市名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.updated_at">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.member_source_channel">
            <summary>
            来源渠道
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.rights">
            <summary>
            权限列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.wx_nickname">
            <summary>
            微信昵称
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerDto.customer_attrInfos">
            <summary>
            客户属性信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.customer.customerPushDto">
            <summary>
            客户推送信息 - 推送消息结构
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerPushDto.account_type">
            <summary>
            帐号类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerPushDto.account_id">
            <summary>
            帐号ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerPushDto.src">
            <summary>
            客户来源
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerPushDto.mobile">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerPushDto.name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.customer.customerPushDto.is_log_off">
            <summary>
            判断账号是否被注销
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.points.pointsDto">
            <summary>
            积分信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.amount">
            <summary>
             变动的积分值
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.biz_token">
            <summary>
            外部业务唯一标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.biz_value">
            <summary>
            积分变动业务标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.client_hash">
            <summary>
            client_id做md5操作后的值
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.create_time">
            <summary>
            积分变动时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.description">
            <summary>
            积分变动描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.event_type">
            <summary>
            事件类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.is_protected">
            <summary>
            是否是保护期
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.mobile">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.node_kdt_id">
            <summary>
            分店kdt_id
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.open_user_id">
            <summary>
            开放平台用户Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.operate_source_type">
            <summary>
            这笔积分操作的来源类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.third_biz_value">
            <summary>
            第三方业务标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.total">
            <summary>
             用户当前积分值
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.unique_id">
            <summary>
            唯一标识位
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.pointsDto.yz_open_id">
            <summary>
            客户在有赞的唯一id
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.points.tradeDto">
            <summary>
            交易信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.points.tradeDto.full_order_info">
            <summary>
             交易基础信息结构体.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.refund.refundDto">
            <summary>
            交易退款信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.oids">
            <summary>
            退款交易明细信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.refunded_fee">
            <summary>
            退款金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.refund_id">
            <summary>
            退款id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.refund_reason">
            <summary>
            退款原因.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.refund_type">
            <summary>
            退款类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.tid">
            <summary>
            订单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundDto.update_time">
            <summary>
            更新时间.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.refund.refundItemDto">
            <summary>
            交易退款明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.status">
            <summary>
            退款状态.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.refund_type">
            <summary>
            退款方式.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.created">
            <summary>
            退款申请时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.refund_postage">
            <summary>
            运费.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.modified">
            <summary>
            修改日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.refund_account_time">
            <summary>
            退款时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.refund_order_item">
            <summary>
            退货明细.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.refundItemDto.refund_fund_list">
            <summary>
            退款金额详情.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.refund.Item">
            <summary>
            退货明细.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Item.refund_fee">
            <summary>
            退款金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Item.oid">
            <summary>
            退款明细id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Item.item_num">
            <summary>
            退款数量.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.refund.Payment">
            <summary>
            退货付款明细.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Payment.refund_id">
            <summary>
            退款ID.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Payment.refund_no">
            <summary>
            退款流水号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Payment.refund_fee">
            <summary>
            退款金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Payment.pay_way">
            <summary>
            支付渠道.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.refund.Payment.refund_mode">
            <summary>
            退款方式.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopAlipayBillOutput">
            <summary>
            支付宝对账单输出
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.shop_name">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.transaction_id">
            <summary>
            支付宝交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.out_trade_no">
            <summary>
            商户订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.terminal_id">
            <summary>
            终端号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.operator_id">
            <summary>
            业务员编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.trade_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.charge">
            <summary>
            手续费
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.trade_type">
            <summary>
            交易类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.trade_time">
            <summary>
            交易时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.finish_time">
            <summary>
            完成时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.body">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.bill_date">
            <summary>
            对账日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillOutput.out_refund_no">
            <summary>
            商户退款交易号
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopAlipayBillDaySummaryOutput">
            <summary>
            支付宝对账单汇总输出
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillDaySummaryOutput.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillDaySummaryOutput.shop_name">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillDaySummaryOutput.trade_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayBillDaySummaryOutput.trade_time">
            <summary>
            对账日期
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopAlipayInput">
            <summary>
            门店支付宝输出.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayInput.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayInput.app_id">
            <summary>
            应用编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayInput.app_private_key">
            <summary>
            商户私钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayInput.app_public_key">
            <summary>
            商户公钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayInput.alipay_public_key">
            <summary>
            阿里公钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayInput.sign_type">
            <summary>
            签名类型
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopAlipayOutInput">
            <summary>
            门店支付宝输出.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayOutInput.app_id">
            <summary>
            应用编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayOutInput.app_private_key">
            <summary>
            商户私钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayOutInput.alipay_public_key">
            <summary>
            阿里公钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopAlipayOutInput.sign_type">
            <summary>
            签名类型
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopWechatBillOutput">
            <summary>
            微信对账单输出
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.shop_id">
            <summary>
            门店编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.shop_name">
            <summary>
            门店名称.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.transaction_id">
            <summary>
            微信交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.out_trade_no">
            <summary>
            商户订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.terminal_id">
            <summary>
            终端号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.trade_time">
            <summary>
            交易时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.trade_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.trade_type">
            <summary>
            交易类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.charge">
            <summary>
            手续费
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.trade_state">
            <summary>
            交易状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.refund_id">
            <summary>
            退款交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.out_refund_no">
            <summary>
            退款商户号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.refund_fee">
            <summary>
            退款交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.refund_state">
            <summary>
            退款状态
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:BPM.Domain.Dto.shop.shopWechatBillOutput.body" -->
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillOutput.bill_date">
            <summary>
            对账日期
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopWechatBillDaySummaryOutput">
            <summary>
            微信对账单日汇总输出
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillDaySummaryOutput.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillDaySummaryOutput.shop_name">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillDaySummaryOutput.trade_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatBillDaySummaryOutput.trade_time">
            <summary>
            对账日期
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.shop.shopWechatInput">
            <summary>
            门店微信输出.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.app_id">
            <summary>
            应用编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.mch_id">
            <summary>
            商户号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.sub_app_id">
            <summary>
            子商户应用编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.sub_mch_id">
            <summary>
            子商户号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.pay_key">
            <summary>
            支付密钥.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.shop.shopWechatInput.cert_path">
            <summary>
            证书路径.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.DeliveryOrder">
            <summary>
            交易物流信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.DeliveryOrder.express_state">
            <summary>
            物流状态 0:待发货; 1:已发货.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.DeliveryOrder.express_type">
            <summary>
            物流类型 0:手动发货; 1:系统自动发货.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.DeliveryOrder.pk_id">
            <summary>
            包裹id.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.FullOrderInfo">
            <summary>
            交易基础信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.address_info">
            <summary>
            订单收货地址信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.buyer_info">
            <summary>
            订单买家信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.child_info">
            <summary>
            交易送礼子单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.orders">
            <summary>
            订单明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.order_info">
            <summary>
            订单明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.pay_info">
            <summary>
            交易支付信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.remark_info">
            <summary>
            订单标记信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.FullOrderInfo.source_info">
            <summary>
            订单来源.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.address_extra">
            <summary>
            地址扩展信息经纬度信息和省编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_address">
            <summary>
            详细地址.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_city">
            <summary>
            市.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_district">
            <summary>
            区.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_end_time">
            <summary>
            同城送预计送达时间-结束时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_postal_code">
            <summary>
            邮政编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_province">
            <summary>
            省.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.delivery_start_time">
            <summary>
            同城送预计送达时间-开始时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.receiver_name">
            <summary>
            收货人姓名.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.receiver_tel">
            <summary>
            收货人手机号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.AddressInfo.self_fetch_info">
            <summary>
            到店自提信息 json格式.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.BuyerInfo">
            <summary>
            订单买家信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.BuyerInfo.yz_open_id">
            <summary>
            有赞用户id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.BuyerInfo.buyer_phone">
            <summary>
            买家手机号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.BuyerInfo.outer_user_id">
            <summary>
            微信H5和微信小程序用户id.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.ChildInfo">
            <summary>
            交易送礼子单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.ChildInfo.gift_no">
            <summary>
            送礼编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.ChildInfo.gift_sign">
            <summary>
            送礼标记.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.Orders">
            <summary>
            订单明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.alias">
            <summary>
            商品别名.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.buyer_messages">
            <summary>
            商品留言.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.cross_border_trade_mode">
            <summary>
            海淘商品贸易模式.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.customs_code">
            <summary>
            海淘口岸编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.discount">
            <summary>
            非现金抵扣金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.discount_price">
            <summary>
            单商品现价，减去了商品的优惠金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.fenxiao_discount">
            <summary>
            分销非现金抵扣金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.fenxiao_discount_price">
            <summary>
            分销商品金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.fenxiao_freight">
            <summary>
            分销运杂费.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.fenxiao_payment">
            <summary>
            分销实付金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.fenxiao_price">
            <summary>
            分销价格.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.fenxiao_tax_total">
            <summary>
            分销税费.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.freight">
            <summary>
            运杂费.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.goods_url">
            <summary>
            商品详情链接.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.is_cross_border">
            <summary>
            是否海淘订单, 1是海淘.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.is_present">
            <summary>
            是否赠品.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.item_barcode">
            <summary>
            商品条码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.item_id">
            <summary>
            商品id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.item_message">
            <summary>
            商品留言.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.item_no">
            <summary>
            商品编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.item_type">
            <summary>
            订单类型 0:普通类型商品.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.num">
            <summary>
            商品数量.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.oid">
            <summary>
            订单明细id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.outer_item_id">
            <summary>
            商品编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.outer_sku_id">
            <summary>
            商品规格编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.payment">
            <summary>
            商品最终均摊价.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.pic_path">
            <summary>
            商品图片地址.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.points_price">
            <summary>
            商品积分价.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.price">
            <summary>
            单商品原价.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.sku_barcode">
            <summary>
            规格条码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.sku_id">
            <summary>
            商品规格id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.sku_no">
            <summary>
            规格编码.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.sku_properties_name">
            <summary>
            规格信息（无规格商品为空）.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.sub_order_no">
            <summary>
            报关单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.tax_total">
            <summary>
            税费.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.title">
            <summary>
            商品名称.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Orders.total_fee">
            <summary>
            商品优惠后总价.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.OrderInfo">
            <summary>
            交易明细详情.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.close_type">
            <summary>
            关闭类型 0:未关闭.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.confirm_time">
            <summary>
            订单确认时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.consign_time">
            <summary>
            订单发货时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.created">
            <summary>
            订单创建时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.expired_time">
            <summary>
            订单过期时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.express_type">
            <summary>
            物流类型 0:快递发货; 1:到店自提; 2:同城配送; 9:无需发货.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.is_retail_order">
            <summary>
            是否零售订单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.offline_id">
            <summary>
            网点id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.order_extra">
            <summary>
            订单扩展字段.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.order_tags">
            <summary>
            订单信息打标.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.pay_time">
            <summary>
            订单支付时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.pay_type">
            <summary>
            支付类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.refund_state">
            <summary>
            退款状态 0:未退款.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.status">
            <summary>
            主订单状态.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.status_str">
            <summary>
            主订单状态描述.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.success_time">
            <summary>
            订单成功时间.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.team_type">
            <summary>
            店铺类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.tid">
            <summary>
            订单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.type">
            <summary>
            主订单类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderInfo.update_time">
            <summary>
            订单更新时间.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.OrderExtra">
            <summary>
            订单扩展字段.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.cashier_id">
            <summary>
            收银员id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.cashier_name">
            <summary>
            收银员名字.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.create_device_id">
            <summary>
            下单设备号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.dept_id">
            <summary>
            美业分店id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.fx_inner_transaction_no">
            <summary>
            分销单外部支付流水号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.fx_kdt_id">
            <summary>
            分销店铺id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.fx_order_no">
            <summary>
            分销单订单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.fx_outer_transaction_no">
            <summary>
            分销单内部支付流水号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.id_card_name">
            <summary>
            海淘身份证信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.id_card_number">
            <summary>
            海淘身份证信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.invoice_title">
            <summary>
            发票抬头.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.is_from_cart">
            <summary>
            是否来自购物车.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.is_parent_order">
            <summary>
            是否父单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.is_points_order">
            <summary>
            是否是积分订单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.is_sub_order">
            <summary>
            是否子单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.merchant_customized_special_order">
            <summary>
            ISV打标信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.parent_order_no">
            <summary>
            父单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.purchase_order_no">
            <summary>
            采购单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.retail_pick_up_code">
            <summary>
            零售特有字段.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.retail_site_no">
            <summary>
            零售特有字段.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderExtra.settle_time">
            <summary>
            结算时间.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.OrderTags">
            <summary>
            订单信息打标.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_feedback">
            <summary>
            是否有维权.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_fenxiao_order">
            <summary>
            是否分销单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_member">
            <summary>
            是否会员订单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_multi_store">
            <summary>
            是否多门店订单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_offline_order">
            <summary>
            是否线下订单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_payed">
            <summary>
            是否支付.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_postage_free">
            <summary>
            是否享受免邮.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_purchase_order">
            <summary>
            是否采购单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_refund">
            <summary>
            是否有退款.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_secured_transactions">
            <summary>
            是否担保交易.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_settle">
            <summary>
            是否结算.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderTags.is_virtual">
            <summary>
            是否虚拟订单.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.PayInfo">
            <summary>
            交易支付信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.PayInfo.outer_transactions">
            <summary>
            外部支付单号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.PayInfo.payment">
            <summary>
            最终支付价格.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.PayInfo.post_fee">
            <summary>
            邮费.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.PayInfo.total_fee">
            <summary>
            优惠前商品总价.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.PayInfo.transaction">
            <summary>
            有赞支付流水号.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.RemarkInfo">
            <summary>
            订单标记信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RemarkInfo.buyer_message">
            <summary>
            订单买家留言.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RemarkInfo.star">
            <summary>
            订单标星等级 0-5.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RemarkInfo.trade_memo">
            <summary>
            订单商家备注.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.SourceInfo">
            <summary>
            订单来源.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.SourceInfo.book_key">
            <summary>
            下单唯一标识.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.SourceInfo.is_offline_order">
            <summary>
            是否来自线下订单.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.SourceInfo.order_mark">
            <summary>
            订单标记.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.SourceInfo.source">
            <summary>
            订单来源平台.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.Source">
            <summary>
            订单来源平台.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Source.platform">
            <summary>
            平台 wx:微信.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Source.wx_entrance">
            <summary>
            微信平台细分.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.OrderPromotion">
            <summary>
            交易优惠信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderPromotion.adjust_fee">
            <summary>
            订单改价金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderPromotion.item">
            <summary>
            商品级优惠明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderPromotion.item_discount_fee">
            <summary>
            商品优惠总金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderPromotion.order">
            <summary>
            订单级优惠明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.OrderPromotion.order_discount_fee">
            <summary>
            订单优惠总金额.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.Item">
            <summary>
            商品级优惠明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Item.is_present">
            <summary>
            是否赠品.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Item.item_id">
            <summary>
            商品id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Item.oid">
            <summary>
            交易明细id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Item.promotions">
            <summary>
            优惠明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Item.sku_id">
            <summary>
            商品规格id.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.Promotions">
            <summary>
            优惠明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Promotions.decrease">
            <summary>
            优惠金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Promotions.promotion_title">
            <summary>
            活动标题.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Promotions.promotion_type">
            <summary>
            活动类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Promotions.promotion_type_id">
            <summary>
            活动类型id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Promotions.promotion_type_name">
            <summary>
            优惠类型描述.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.Order">
            <summary>
            订单级优惠明细结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.coupon_id">
            <summary>
            优惠券/码编号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.discount_fee">
            <summary>
            优惠金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.promotion_condition">
            <summary>
            优惠描述.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.promotion_content">
            <summary>
            优惠活动别名.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.promotion_id">
            <summary>
            优惠id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.promotion_type">
            <summary>
            活动类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.promotion_type_id">
            <summary>
            活动类型id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.promotion_type_name">
            <summary>
            优惠类型描述.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.Order.sub_promotion_type">
            <summary>
            优惠子类型.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.QrInfo">
            <summary>
            订单扫码收银信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.QrInfo.qr_id">
            <summary>
            收款码ID.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.QrInfo.qr_name">
            <summary>
            收款码名称.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.QrInfo.qr_pay_id">
            <summary>
            订单付款ID.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.RefundOrder">
            <summary>
            交易退款信息结构体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RefundOrder.oids">
            <summary>
            退款交易明细信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RefundOrder.refund_fee">
            <summary>
            退款金额.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RefundOrder.refund_id">
            <summary>
            退款id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RefundOrder.refund_state">
            <summary>
            退款状态.
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.RefundOrder.refund_type">
            <summary>
            退款类型.
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanOrderResponse">
            <summary>
            有赞订单响应
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanOrderInfo">
            <summary>
            有赞订单信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanFullOrderInfo">
            <summary>
            订单完整信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanChildInfo">
            <summary>
            子订单信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanRemarkInfo">
            <summary>
            备注信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanAddressInfo">
            <summary>
            地址信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanPayInfo">
            <summary>
            支付信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanBuyerInfo">
            <summary>
            买家信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanSourceInfo">
            <summary>
            来源信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanSource">
            <summary>
            来源详细信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanOrderBaseInfo">
            <summary>
            订单基础信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.consign_time">
            <summary>
            发货时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.order_extra">
            <summary>
            订单扩展信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.created">
            <summary>
            订单创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.expired_time">
            <summary>
            订单过期时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.status_str">
            <summary>
            订单状态描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.success_time">
            <summary>
            订单成功时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.type">
            <summary>
            订单类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.shop_name">
            <summary>
            店铺名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.tid">
            <summary>
            订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.confirm_time">
            <summary>
            确认时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.pay_time">
            <summary>
            支付时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.node_kdt_id">
            <summary>
            店铺ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.update_time">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.pay_type_str">
            <summary>
            支付方式描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.is_retail_order">
            <summary>
            是否为零售订单
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.backstage_order_type">
            <summary>
            后台订单类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.pay_type">
            <summary>
            支付类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.team_type">
            <summary>
            团队类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.refund_state">
            <summary>
            退款状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.root_kdt_id">
            <summary>
            根店铺ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.close_type">
            <summary>
            关闭类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.status">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.express_type">
            <summary>
            物流类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderBaseInfo.order_tags">
            <summary>
            订单标签
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanOrderExtra">
            <summary>
            订单扩展信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderExtra.is_from_cart">
            <summary>
            是否来自购物车
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderExtra.is_member">
            <summary>
            是否是会员
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderExtra.buyer_name">
            <summary>
            买家名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderExtra.is_points_order">
            <summary>
            是否积分订单
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderExtra.sales_guide">
            <summary>
            导购信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderExtra.service_guide">
            <summary>
            服务导购信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanServiceGuide">
            <summary>
            服务导购信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanServiceGuide.service_guide_staff">
            <summary>
            服务导购员工号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanServiceGuide.service_guide_shop_no">
            <summary>
            服务导购店铺编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanServiceGuide.service_guide_name">
            <summary>
            服务导购名称
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanOrderTags">
            <summary>
            订单标签
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderTags.is_member">
            <summary>
            是否会员订单
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderTags.is_secured_transactions">
            <summary>
            是否担保交易
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderTags.is_feedback">
            <summary>
            是否有反馈
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderTags.is_payed">
            <summary>
            是否已支付
            </summary>
        </member>
        <member name="T:BPM.Domain.Dto.trade.YouzanOrderItem">
            <summary>
            订单商品信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.is_cross_border">
            <summary>
            是否跨境
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.outer_item_id">
            <summary>
            外部商品ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.item_type">
            <summary>
            商品类型。0:普通类型商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.discount_price">
            <summary>
            优惠后价格
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.num">
            <summary>
            商品数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.oid">
            <summary>
            订单明细ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.title">
            <summary>
            商品标题
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.fenxiao_payment">
            <summary>
            分销支付金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.item_no">
            <summary>
            商品编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.buyer_messages">
            <summary>
            买家留言
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.root_sku_id">
            <summary>
            根SKU ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.is_present">
            <summary>
            是否赠品
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.cross_border_trade_mode">
            <summary>
            跨境贸易类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.price">
            <summary>
            商品原价
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.sub_order_no">
            <summary>
            子订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.total_fee">
            <summary>
            商品总价
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.fenxiao_price">
            <summary>
            分销价格
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.alias">
            <summary>
            商品别名
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.payment">
            <summary>
            实付金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.is_pre_sale">
            <summary>
            是否预售
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.item_barcode">
            <summary>
            商品条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.outer_sku_id">
            <summary>
            外部SKU ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.sku_unique_code">
            <summary>
            SKU唯一码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.goods_url">
            <summary>
            商品链接
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.customs_code">
            <summary>
            海关编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.item_id">
            <summary>
            商品ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.item_tags">
            <summary>
            商品标签
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.weight">
            <summary>
            商品重量
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.sku_id">
            <summary>
            SKU ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.sku_properties_name">
            <summary>
            SKU属性名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.pic_path">
            <summary>
            商品图片
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.is_combo">
            <summary>
            是否组合商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.pre_sale_type">
            <summary>
            预售类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.points_price">
            <summary>
            积分价格
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.sku_no">
            <summary>
            SKU编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.root_item_id">
            <summary>
            根商品ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Dto.trade.YouzanOrderItem.sku_barcode">
            <summary>
            SKU条码
            </summary>
        </member>
        <member name="T:BPM.Domain.Entitys.Dto.resultLogDto">
            <summary>
            测试列表.
            </summary>
        </member>
        <member name="P:BPM.Domain.Entitys.Dto.resultLogDto.succeed">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Entitys.Dto.resultLogDto.fail">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Entitys.Dto.resultLogDto.succeed_data">
            <summary>
             成功数据
            </summary>
        </member>
        <member name="P:BPM.Domain.Entitys.Dto.resultLogDto.fail_data">
            <summary>
             失败数据
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.baseEntity">
            <summary>
            实体基类
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.baseEntity.created_user_id">
            <summary> 
            创建用户 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.baseEntity.created_date">
            <summary> 
            创建日期 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.baseEntity.modify_user_id">
            <summary> 
            修改人 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.baseEntity.modify_date">
            <summary> 
            修改日期 
            </summary> 
            <returns></returns> 
        </member>
        <member name="T:BPM.Domain.Entity.customer.customerCardEntity">
            <summary>
            客户会员卡实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.customer_sn">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.grade_id">
            <summary>
            权益编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.card_no">
            <summary>
            卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.phone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.store_id">
            <summary>
            门店号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.state">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.point">
            <summary>
            积分
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.outer_card_no">
            <summary>
            来源卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.source">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.backage_code">
            <summary>
            权益规则编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.type">
            <summary>
            操作类型,1=开卡、2=续费
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.change_type">
            <summary>
            权益卡本次发生变更的类型 1.续费: 2.到期:3.延长有效期: 4.退款;5.作废权益卡
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.exp_date">
            <summary>
            有效期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.created_date">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.tag_mall_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardEntity.versionTimestamp">
            <summary>
            版本时间戳
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.customerCardLogsEntity">
            <summary>
            客户会员卡日志
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.changeAt">
            <summary>
            积分变更时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.expiredAt">
            <summary>
            积分过期时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.changePoint">
            <summary>
            变动积分，精确到小数点后两位，赠送积分为正数，扣减积分为负数
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.customerId">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.customerPhone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.pointChannel">
            <summary>
            积分渠道("ON_LINE_MALL"=在线商城)
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.pointMode">
            <summary>
            积分方式(1=POS积分;2=平台积分;3=积分调整;4=系统积分;5=积分消耗;99=其他)
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.pointType">
            <summary>
             积分类型(1=开卡礼包;2=升级礼包;3=购物积分;4=退货;7=积分调整;8=积分清零;12=积分抽奖;13=活动积分;99=其他)
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.relatedNo">
            <summary>
            关联单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.serialNo">
            <summary>
            流水号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.updateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerCardLogsEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.customerEntity">
            <summary>
            客户实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.store_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.customer_sn">
            <summary>
             客户编号
            </summary>
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.card_no">
            <summary>
            卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.phone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.nick_name">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.real_name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.birth_day">
            <summary>
            生日
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.gender">
            <summary>
             性别
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.state">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.source">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.avatar">
            <summary>
            图像
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.open_id">
            <summary>
            openId
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.union_id">
            <summary>
            unionId
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.created_date">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.modify_date">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.tag_body">
            <summary>
            同步内容
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.org_phone">
            <summary>
            原手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.request_id">
            <summary>
            外部来源id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.tag_mall_status">
            <summary>
            mall同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEntity.yzVersion">
            <summary>
            有赞版本号
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.customerEquityEntity">
            <summary>
            客户权益实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.customer_id">
            <summary>
             客户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.customer_request_id">
            <summary>
             客户来源编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.yz_open_id">
            <summary>
             有赞id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.phone">
            <summary>
             手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.status">
            <summary>
             状态 -1 删除,0-禁用,1-启用
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.equity_card_alias_id">
            <summary>
             权益卡id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.equity_card_no">
            <summary>
             权益卡号(有赞分配权益卡返回)
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.member_card_no">
            <summary>
             ERP卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.created_date">
            <summary>
            创建日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.start_time">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.end_time">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.modify_date">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.tag_body">
            <summary>
            同步内容
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.tag_mall_status">
            <summary>
            mall同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityEntity.yzVersion">
            <summary>
            有赞版本号
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.customerEquityRenewalEntity">
            <summary>
            客户权益续期记录表
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.operator_yz_open_id">
            <summary>
            操作人记录（非校验必须是店铺下管理员身份员工 ）
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.yz_open_id">
            <summary>
            有赞用户id，用户在有赞的唯一id。推荐使用
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.equity_card_no">
            <summary>
            权益卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.extension_end_time">
            <summary>
            结束时间延期天数 单位：天
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.extension_begin_time">
            <summary>
            开始时间延期天数 单位：天 （该字段需要加白使用，并且卡要未生效状态。使用前请联系平台）
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.extension_date_num">
            <summary>
            延期天数
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.modify_date">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.tag_body">
            <summary>
            同步日志
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerEquityRenewalEntity.create_time">
            <summary>
            创建日期
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.customerLogsEntity">
            <summary>
            客户实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.appid">
            <summary>
            
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.birthday">
            <summary>
             生日
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.customerId">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.customerPhone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.gender">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.nickname">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.openId">
            <summary>
            opendId
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.realName">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.unionId">
            <summary>
            unionId
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.updateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.customerLogsEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.EquityEntity">
            <summary>
            权益实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.create_time">
            <summary>
            创建日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.grant_type">
            <summary>
             权益卡类型;3:无门槛卡,2:付费卡,1:规则卡
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.name">
            <summary>
            权益卡名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.card_id">
            <summary>
            商家权益卡的id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.card_alias">
            <summary>
            商家权益卡的唯一标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.card_url">
            <summary>
            发卡链接
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.is_available">
            <summary>
             权益卡状态：使用中:true 已禁用:false
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.EquityEntity.grade_id">
            <summary>
            等级id
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.memberGradeEntity">
            <summary>
            会员实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.customer_id">
            <summary>
            客户id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.phone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.grade_id">
            <summary>
            等级编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.yz_grade_id">
            <summary>
            有赞等级编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.created_date">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.modify_date">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeEntity.tag_body">
            <summary>
            同步内容
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.memberGradeTemplateEntity">
            <summary>
            会员等级模板
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.grade_code">
            <summary>
            等级编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.source_grade_code">
            <summary>
            来源等级编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.grade_name">
            <summary>
            等级名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.birthday_disc">
            <summary>
            生日折扣
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.disc">
            <summary>
            折扣
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.type">
            <summary>
            等级类型：1.免费类型2.付费类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.created_date">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.created_user_id">
            <summary>
            创建用户id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.modify_user_id">
            <summary>
            修改用户
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.memberGradeTemplateEntity.modify_date">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.customer.pointsEntity">
            <summary>
            积分历史记录实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.unique_id">
            <summary>
            唯一标识位（主键）
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.mobile">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.amount">
            <summary>
             变动的积分值
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.total">
            <summary>
             用户当前积分值
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.description">
            <summary>
            积分变动描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.create_time">
            <summary>
            积分变动时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.event_type">
            <summary>
            事件类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.biz_token">
            <summary>
            外部业务唯一标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.biz_value">
            <summary>
            积分变动业务标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.yz_open_id">
            <summary>
            客户在有赞的唯一id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.node_kdt_id">
            <summary>
            分店kdt_id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.is_protected">
            <summary>
            是否是保护期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.client_hash">
            <summary>
            client_id做md5操作后的值
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.open_user_id">
            <summary>
            开放平台用户Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.operate_source_type">
            <summary>
            这笔积分操作的来源类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.customer.pointsEntity.third_biz_value">
            <summary>
            第三方业务标识
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.order.orderEntity">
            <summary>
            订单主表
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.order_no">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.kdt_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.kdt_name">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.yz_open_id">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.buyer_phone">
            <summary>
            会员手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.total_fee">
            <summary>
            订单总金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.status">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.created">
            <summary>
            下单时间，格式:yyyy-MM-dd HH:mm:ss
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.pay_time">
            <summary>
            付款时间，格式:yyyy-MM-dd HH:mm:ss
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.pay_type">
            <summary>
            付款方式
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.item_type">
            <summary>
            商品类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderEntity.is_fenxiao_order">
            <summary>
            是否分销
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.order.orderItemEntity">
            <summary>
            订单明细表
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.order_seq">
            <summary>
            明细序号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.order_no">
            <summary>
            订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.oid">
            <summary>
            订单明细id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.item_id">
            <summary>
            商品id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.item_no">
            <summary>
            商品编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.item_type">
            <summary>
            商品类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.alias">
            <summary>
            商品别称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.item_barcode">
            <summary>
            商品条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.is_present">
            <summary>
            是否赠品
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.num">
            <summary>
            订单数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.outer_item_id">
            <summary>
            外部商品id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.outer_sku_id">
            <summary>
            外部SKUid
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.price">
            <summary>
            商品原价
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.points_price">
            <summary>
            促销价格
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.discount_price">
            <summary>
            销售价格
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.payment">
            <summary>
            均摊价格
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.sku_barcode">
            <summary>
            SKU条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.sku_id">
            <summary>
            SKUID
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.sku_no">
            <summary>
            SKU编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.sku_properties_name">
            <summary>
            SKU描述
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.title">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderItemEntity.total_fee">
            <summary>
            总金额
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.order.orderPaymentEntity">
            <summary>
            订单支付记录
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderPaymentEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderPaymentEntity.order_no">
            <summary>
            订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderPaymentEntity.payment">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderPaymentEntity.post_fee">
            <summary>
            运费
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderPaymentEntity.outer_transactions">
            <summary>
            外部交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.order.orderPaymentEntity.transaction">
            <summary>
            交易单号
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.product.productEntity">
            <summary>
            版 本 BPM敏捷开发框架
            Copyright (c) 2018-2022 深圳市中畅源科技开发有限公司
            创建人：Aarons
            日 期：2022.03.21
            描 述：商品实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.product_id">
            <summary> 
            商品编号 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.store_id">
            <summary> 
            门店编号（sto_store表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.root_product_id">
            <summary> 
            总部商品id
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.product_code">
            <summary> 
            商品编码
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.bar_code">
            <summary> 
            商品自编码 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.type_id">
            <summary> 
            商品类型（pdt_type表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.goods_type">
            <summary> 
            商品类型（book.图书、product.常规商品、pre_sale.预售商品、gift.赠品） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.product_name">
            <summary> 
            商品名称 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.state">
            <summary> 
            商品状态（固定值：0.上架、1.下架、2.回收站） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.dpt_code">
            <summary> 
            部类（book_department表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.brand_code">
            <summary> 
            品牌编号（pdt_brand表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.make_price">
            <summary> 
            定价 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.has_sku">
            <summary> 
            是否启用多SKU（1.启用、2.不启用） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.inctax">
            <summary> 
            税率 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.weight">
            <summary>
            重量
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.product_des">
            <summary>
            商品简介
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.is_sync">
            <summary>
            是否同步
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.tag_status">
            <summary>
            标记状态(select,add,edit)
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productEntity.channel">
            <summary>
            店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.product.productExtEntity">
            <summary>
            商品扩展字段
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productExtEntity.isbn">
            <summary> 
            商品ISBN条码 
            </summary> 
            <returns></returns> 
        </member>
        <member name="T:BPM.Domain.Entity.product.productSkuEntity">
            <summary>
            版 本 BPM敏捷开发框架
            Copyright (c) 2018-2022 深圳市中畅源科技开发有限公司
            创建人：Aarons
            日 期：2022-05-27
            描 述：商品规格实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.id">
            <summary> 
            商品SKUID 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.product_id">
            <summary> 
            商品编号（pdt_product表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.sku_sn">
            <summary> 
            商品SKU编号 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.sku_name">
            <summary> 
            商品标题 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.make_price">
            <summary> 
            商品市场价 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.sale_price">
            <summary> 
            商品销售价,需要在Sku价格所决定的的区间内 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.cost_price">
            <summary> 
            商品成本价 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.des">
            <summary> 
            商品SKU规格描述, 格式：pText:vText;pText:vText，多个sku之间用逗号分隔，如：颜色:黄色;尺寸:M。pText和vText文本中不可以存在冒号和分号以及逗号 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.group">
            <summary> 
            商品SKU键值,由商品SKU明细的规格值ID按小到大组成的数字,便于检索 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.sku_state">
            <summary> 
            商品SKU状态（固定值：0.正常、1.删除，2-禁用） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuEntity.channel">
            <summary>
            店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.id">
            <summary> 
            主键
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.product_id">
            <summary> 
            商品编号
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.has_sku">
            <summary> 
            是否多规格
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.sku_id">
            <summary> 
            规格编号
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.store_id">
            <summary> 
            门店编号
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.sale_price">
            <summary> 
            门店销售价
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.create_date">
            <summary> 
            创建时间 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.modify_date">
            <summary> 
            更新日期 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.tag_status">
            <summary>
            标记状态(select,add,edit)
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.tag_body">
            <summary>
            同步主体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuShopPriceEntity.PLUCODE">
            <summary>
            ERP条码
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.product.productSkuDetailEntity">
            <summary>
            商品规格详情
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuDetailEntity.id">
            <summary> 
            id 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuDetailEntity.product_id">
            <summary> 
            商品SPUID 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuDetailEntity.sku_id">
            <summary> 
              
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuDetailEntity.spec_name_id">
            <summary> 
            商品规格编号（pdt_spec_name表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productSkuDetailEntity.spec_value_id">
            <summary> 
            商品规格值编号（tb_pdt_spec_value表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="T:BPM.Domain.Entity.product.productStockEntity">
            <summary>
            版 本 BPM敏捷开发框架
            Copyright (c) 2018-2022 深圳市中畅源科技开发有限公司
            创建人：Aarons
            日 期：2022.03.21
            描 述：商品实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.id">
            <summary> 
            主键编号 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.product_id">
            <summary> 
            商品编号（pdt_product表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.sku_id">
            <summary> 
            规格编号（pdt_sku表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.store_id">
            <summary> 
            门店编号（sto_store表id） 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.stock">
            <summary> 
            总收货库存 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.has_sku">
            <summary> 
             是否多规格 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.modify_date">
            <summary> 
            最后更新库存日期 
            </summary> 
            <returns></returns> 
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.tag_status">
            <summary>
            同步标记
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.product.productStockEntity.tag_body">
            <summary>
            同步主体
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.refund.refundEntity">
            <summary>
            退款单主表
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.order_no">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.tid">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.kdt_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.kdt_name">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.yz_open_id">
            <summary>
            客户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.buyer_phone">
            <summary>
            会员手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.refunded_fee">
            <summary>
            退款总金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.refund_postage">
            <summary>
            退款邮费
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.refund_type">
            <summary>
            退款类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.refund_reason">
            <summary>
            退款理由
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.status">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.created">
            <summary>
            创建日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.modified">
            <summary>
            修改日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundEntity.refund_account_time">
            <summary>
            退款时间
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.refund.refundItemEntity">
            <summary>
            退款明细细表
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundItemEntity.order_seq">
            <summary>
            明细序号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundItemEntity.order_no">
            <summary>
            订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundItemEntity.oid">
            <summary>
            订单明细id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundItemEntity.item_num">
            <summary>
            订单数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundItemEntity.refund_fee">
            <summary>
            总金额
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.refund.refundPaymentEntity">
            <summary>
            订单支付记录
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundPaymentEntity.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundPaymentEntity.order_no">
            <summary>
            订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundPaymentEntity.refund_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundPaymentEntity.refund_no">
            <summary>
            交易明细id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.refund.refundPaymentEntity.pay_way">
            <summary>
            交易渠道
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.shop.shopAlipayBillEntity">
            <summary>
            门店支付宝对账单
            版 本：V3.2
            版 权：中畅源科技开发有限公司（https://www.szclouds.com）
            作 者：Aarons
            日 期：2023-01-05.
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.id">
            <summary>
            编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.transaction_id">
            <summary>
            支付宝交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.out_trade_no">
            <summary>
            商户订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.terminal_id">
            <summary>
            终端号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.operator_id">
            <summary>
            业务员编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.trade_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.charge">
            <summary>
            手续费
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.trade_type">
            <summary>
            交易类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.trade_time">
            <summary>
            交易时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.finish_time">
            <summary>
            完成时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.body">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.bill_date">
            <summary>
            对账日期
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayBillEntity.out_refund_no">
            <summary>
            商户退款交易号
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.shop.shopAlipayConfigEntity">
            <summary>
            门店支付宝配置信息
            版 本：V3.2
            版 权：中畅源科技开发有限公司（https://www.szclouds.com）
            作 者：Aarons
            日 期：2023-01-05.
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayConfigEntity.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayConfigEntity.app_id">
            <summary>
            应用编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayConfigEntity.app_private_key">
            <summary>
            商户私钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayConfigEntity.app_public_key">
            <summary>
            商户公钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayConfigEntity.alipay_public_key">
            <summary>
            阿里公钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopAlipayConfigEntity.sign_type">
            <summary>
            签名类型
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.shop.shopEntity">
            <summary>
            门店信息
            版 本：V3.2
            版 权：中畅源科技开发有限公司（https://www.szclouds.com）
            作 者：Aarons
            日 期：2023-01-05.
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.store_no">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.store_name">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.parent_id">
            <summary>
            上级id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.status">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.province">
            <summary>
            省份
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.city">
            <summary>
            城市
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.area">
            <summary>
            区县
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.address">
            <summary>
            详细地址
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.longitude">
            <summary>
            精度
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.latitude">
            <summary>
            纬度
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.tel">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.intro">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.source_no">
            <summary>
            外部门店id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.net_source_no">
            <summary>
            网店id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.created_user_id">
            <summary>
            创建人id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.created_date">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.modify_user_id">
            <summary>
            更新人id
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopEntity.modify_date">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.shop.shopWechatBillEntity">
            <summary>
            门店支微信对账单
            版 本：V3.2
            版 权：中畅源科技开发有限公司（https://www.szclouds.com）
            作 者：Aarons
            日 期：2023-01-05.
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.id">
            <summary>
            编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.transaction_id">
            <summary>
            微信交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.out_trade_no">
            <summary>
            商户订单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.terminal_id">
            <summary>
            终端号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.trade_time">
            <summary>
            交易时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.trade_fee">
            <summary>
            交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.trade_type">
            <summary>
            交易类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.trade_state">
            <summary>
            交易状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.app_id">
            <summary>
            应用编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.mch_id">
            <summary>
            商户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.sub_mch_id">
            <summary>
            子商户编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.device_info">
            <summary>
            设备号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.refund_id">
            <summary>
            退款交易单号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.out_refund_no">
            <summary>
            退款商户号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.refund_fee">
            <summary>
            退款交易金额
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.charge">
            <summary>
            手续费
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.refund_state">
            <summary>
            退款商户号
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:BPM.Domain.Entity.shop.shopWechatBillEntity.body" -->
        <member name="P:BPM.Domain.Entity.shop.shopWechatBillEntity.bill_date">
            <summary>
            对账日期
            </summary>
        </member>
        <member name="T:BPM.Domain.Entity.shop.shopWechatConfigEntity">
            <summary>
            门店微信配置信息
            版 本：V3.2
            版 权：中畅源科技开发有限公司（https://www.szclouds.com）
            作 者：Aarons
            日 期：2023-01-05.
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.app_id">
            <summary>
            应用编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.mch_id">
            <summary>
            商户号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.sub_app_id">
            <summary>
            子商户应用编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.sub_mch_id">
            <summary>
            子商户号
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.pay_key">
            <summary>
            支付密钥
            </summary>
        </member>
        <member name="P:BPM.Domain.Entity.shop.shopWechatConfigEntity.cert_path">
            <summary>
            证书路径
            </summary>
        </member>
        <member name="T:BPM.Domain.Queries.customerQuery">
            <summary>
            客户查询对象
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.customerQuery.tag_status">
            <summary>
            标记状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.customerQuery.card_no">
            <summary>
            卡号
            </summary>
        </member>
        <member name="T:BPM.Domain.Queries.product.productPriceQuery">
            <summary>
            
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.product.productPriceQuery.tag_status">
            <summary>
            标记状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.product.productPriceQuery.card_no">
            <summary>
            卡号
            </summary>
        </member>
        <member name="T:BPM.Domain.Queries.product.productStoreQuery">
            <summary>
            商品库存查询参数
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.product.productStoreQuery.tag_status">
            <summary>
            标记状态
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.product.productStoreQuery.card_no">
            <summary>
            卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.product.productStoreQuery.store_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Domain.Queries.product.productStoreQuery.product_id">
            <summary>
            商品编号
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.createCustomerLevelRequest">
            <summary>
            客户级别请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerLevelRequest.level_type">
            <summary>
            等级类型(默认为1:免费 , 2:付费)
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.createCustomerRequest">
            <summary>
            客户请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.mobile">
            <summary>
             手机号
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.customer_create">
            <summary>
            客户基础信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.scrm_channel_type">
            <summary>
             scrm渠道类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.label_info">
            <summary>
            客户标识信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.country_code">
            <summary>
            国家码（默认为+86）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.is_mobile_auth">
            <summary>
            是否授权手机号 true-授权手机号 false-不授权手机号 不传默认为false（目前创建客户时授权手机号后不会计算客户等级，需调等级设置接口设置等级）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.is_do_ext_point">
            <summary>
            是否需要走扩展点，不传参数默认为true，true-走扩展点 false-不走扩展点 （其中扩展点为第三方创建客户）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.createCustomerRequest.create_date">
            <summary>
             用户创建时间(日期格式:yyyy-MM-dd HH:mm:ss)
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_info.wei_xin">
            <summary>
             微信
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_info.gender">
            <summary>
             性别，0：未知；1：男；2：女
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_info.remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_info.birthday">
            <summary>
            生日
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_info.name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_info.ascription_kdt_id">
            <summary>
            所属门店
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_label_info.src_way">
            <summary>
            客户来源方式（不传或0：其他，2008：系统打通）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customer_label_info.src_channel">
            <summary>
            客户来源渠道（不传或0：其他，2000：三方门店）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.CustomerPhoneRequest.phone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.customerInfoRequest">
            <summary>
            客户信息请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerInfoRequest.Customers">
            <summary>
            客户实体列表.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerInfoRequest.Equitys">
            <summary>
            客户权益列表.
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.setCustomerLevelRequest">
            <summary>
            客户级别请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.setCustomerLevelRequest.is_do_ext_point">
            <summary>
            是否走(修改等级)扩展点，如果为true走扩展点，为false不走扩展点，默认为true
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.setCustomerLevelRequest.level_alias">
            <summary>
            会员等级别名，有赞系统生成，可以使用【youzan.scrm.level.list】接口获得
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.setCustomerLevelRequest.user">
            <summary>
            用户信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.levelExtInfo">
            <summary>
            等级扩展信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.invited_account_type">
            <summary>
            会员邀请导购账户帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.up_grade_type">
            <summary>
            等级变更类型（1：商家设置等级 2：资产合并 3：触发升级规则 4：系统设置 5：主动退出 6：重新入会 7：融合仓-规则回回流）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.invited_sl">
            <summary>
            会员邀请标识（invited_account_id 二选一即可 ）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.invited_account_id">
            <summary>
            会员邀请导购账户ID；配合invited_account_type字段
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.set_level_lock_type">
            <summary>
            目前仅连接器使用。设置等级锁类型，防止调用线下和调用线上使用同一把锁产生死锁。（1：连接器回流锁）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.member_src_channel">
            <summary>
            会员来源渠道 （900：有赞云开放平台）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.levelExtInfo.member_src_way">
            <summary>
            会员来源方式 （302：三方平台）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.userInfo.account_id">
            <summary>
            帐号ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.userInfo.account_type">
            <summary>
            帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.setCustomerLevelRequest_v3">
            <summary>
            客户级别请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.setCustomerLevelRequest_v3.level_alias">
            <summary>
            会员等级别名，有赞系统生成，可以使用【youzan.scrm.level.list】接口获得
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.setCustomerLevelRequest_v3.account_id">
            <summary>
            帐号ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.setCustomerLevelRequest_v3.account_type">
            <summary>
            账户类型
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.updateCustomerRequest">
            <summary>
            更新客户请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.create_date">
            <summary>
             用户创建时间(日期格式:yyyy-MM-dd HH:mm:ss)
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.member_created_at">
            <summary>
            会员创建时间（时间格式:yyyy-MM-dd HH:mm:ss）当且仅当该用户是会员的情况下，更新会员创建时间有效
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.scrm_channel_type">
            <summary>
            scrm渠道类型（2:伯俊）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.external_request_id">
            <summary>
             外部requestId，在客户信息变更事件中会返回该字段
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.is_do_ext_point">
            <summary>
            是否走扩展点（不传默认为true）， true-走扩展点 false-不走扩展点(其中扩展点为：第三方修改客户信息扩展点)
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.account">
            <summary>
            用户帐号信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.updateCustomerRequest.customer_update">
            <summary>
            更新客户信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.accountInfo.account_id">
            <summary>
            帐号ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.accountInfo.account_type">
            <summary>
            帐号类型。目前支持以下选项（只支持传一种）： FansID：自有粉丝ID， Mobile：手机号， YouZanAccount：有赞账号，YzOpenId：有赞OpenId
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.customer.customerUpdateInfo">
            <summary>
            客户更新信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerUpdateInfo.wei_xin">
            <summary>
             微信
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerUpdateInfo.gender">
            <summary>
             性别，0：未知；1：男；2：女
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerUpdateInfo.remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerUpdateInfo.birthday">
            <summary>
            生日
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerUpdateInfo.name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.customer.customerUpdateInfo.ascription_kdt_id">
            <summary>
            所属门店
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.equity.createCustomerEquityRequest">
            <summary>
            创建权益卡列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.createCustomerEquityRequest.card_alias">
             <summary>
            卡别名，不能自定义，需要从接口youzan.scrm.card.list 中获取。
             </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.createCustomerEquityRequest.user">
            <summary>
            用户信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.userEquityInfo.account_id">
            <summary>
            帐号ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.userEquityInfo.account_type">
            <summary>
            帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.equity.customerEquityRenewalRequest">
            <summary>
            权益卡续期
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.operator_yz_open_id">
            <summary>
            操作人记录（非校验必须是店铺下管理员身份员工 ）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.yz_open_id">
            <summary>
            有赞用户id，用户在有赞的唯一id。推荐使用
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.card_no">
            <summary>
            权益卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.extension_end_time">
            <summary>
            结束时间延期天数 单位：天
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.extension_begin_time">
            <summary>
            开始时间延期天数 单位：天 （该字段需要加白使用，并且卡要未生效状态。使用前请联系平台）
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.extension_date_num">
            <summary>
            延期天数
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.customerEquityRenewalRequest.check_customer">
            <summary>
            是否需要校验客户存在,不传默认为false,会根据客户是否存在进行补偿创建客户
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.equity.deleteCustomerEquityRequest">
            <summary>
            删除客户权益卡
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.deleteCustomerEquityRequest.client_id">
            <summary>
            三方应用id
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.deleteCustomerEquityRequest.card_alias">
             <summary>
            卡别名，不能自定义，需要从接口youzan.scrm.card.list 中获取。
             </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.deleteCustomerEquityRequest.card_no">
            <summary>
            权益卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.deleteCustomerEquityRequest.user">
            <summary>
            用户信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.equity.getCustomerEquityRequest">
            <summary>
            获取权益卡列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.equity.getCustomerEquityRequest.page">
            <summary>
            页码，从1 ~100开始，分页数不能超过100页。
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.getBatchProductRequest">
            <summary>
            获取批量商家编码商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getBatchProductRequest.channel">
            <summary>
            店铺渠道类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getBatchProductRequest.item_no_list">
            <summary>
            商品编码
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.getDetailProductRequest">
            <summary>
            获取商品明细
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getDetailProductRequest.item_id">
            <summary>
            商品Id，有赞生成的店铺下商品唯一id，平台唯一。可以通过列表接口如youzan.items.onsale.get （查询出售中商品）和 youzan.items.inventory.get （查询仓库中商品）获取
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.getOnSaleProductRequest">
            <summary>
            获取在售商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.channel">
            <summary>
            店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店;默认网店渠道
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.page_no">
            <summary>
            页码，从1 ~100开始，分页数不能超过100页。
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.page_size">
            <summary>
            行数
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.order_by">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.q">
            <summary>
            搜索字段，支持查询搜索商品名称和商品编码，支持商品名称模糊搜索
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.update_time_start">
            <summary>
             更新开始时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.getOnSaleProductRequest.update_time_end">
            <summary>
            更新结束时间
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.updateProducPriceRequest">
            <summary>
            更新单价请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProducPriceRequest.item_id">
            <summary>
            商品Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProducPriceRequest.sku_list">
            <summary>
            商品sku集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProducPriceRequest.item_price_param">
            <summary>
            无规格
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProducPriceRequest.item_channel_params">
            <summary>
            多渠道价格参数
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.ItemChannelParam">
            <summary>
            渠道价格参数
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.ItemChannelParam.channel">
            <summary>
            渠道类型：0-网店，1-门店
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.ItemChannelParam.item_price_param">
            <summary>
            价格参数
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.SkuPriceParam">
            <summary>
            sku 请求参数
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.SkuPriceParam.price">
            <summary>
            商品的这个Sku的价格；单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.SkuPriceParam.sku_id">
            <summary>
            商品规格Id
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.NoSukPriceParam">
            <summary>
            无规格价格实体
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.NoSukPriceParam.price">
            <summary>
            价格(分)
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.NoSukPriceParam.origin">
            <summary>
            划线价格(元)
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.updateProductStoreRequest">
            <summary>
            更新库存请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.root_item_id">
            <summary>
            总店商品ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.multi_sku_update_open_param_list">
            <summary>
            场景：多sku商品的分店更新参数列表，不同skuId下的multi_sku_update_open_param_list数量总和不允许超过20
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.no_sku_update_open_param_list">
            <summary>
             更新无sku库存集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.multiSkuList.root_sku_id">
            <summary>
            skuId
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.multiSkuList.update_price_stock_open_param_list">
            <summary>
             更新sku库存集合
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.updateProductStoreRequest.multiSkuList.updateStockList">
            <summary>
             更新库存集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.multiSkuList.updateStockList.node_kdt_id">
            <summary>
            门店id
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.multiSkuList.updateStockList.stock_num">
            <summary>
            库存数量
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.product.updateProductStoreRequest.noSkuList">
            <summary>
             无sku集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.noSkuList.node_kdt_id">
            <summary>
            门店id
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.updateProductStoreRequest.noSkuList.stock_num">
            <summary>
            库存数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.MParamList.root_sku_id">
            <summary>
            总店商品的skuid
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.MParamList.update_price_stock_open_param_list">
            <summary>
            更新参数列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.NParamList.node_kdt_id">
            <summary>
            分店店铺id
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.NParamList.stock_num">
            <summary>
            库存数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.product.NParamList.price">
            <summary>
            价格,单位:分
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.shop.shopRequest">
            <summary>
            门店请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.shop.shopRequest.page_no">
            <summary>
            页码，从1 ~100开始，分页数不能超过100页。
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.shop.shopRequest.page_size">
            <summary>
            行数
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.subscriber.authMobileRequest">
            <summary>
             推送请求.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.is_auth_root">
            <summary>
            是否同步授权到总部.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.node_kdt_id">
            <summary>
            是否同步授权到总部.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.member_extra_info">
            <summary>
            会员的额外信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.mobile">
            <summary>
            手机号.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.sign">
            <summary>
            校验字段.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.root_kdt_id">
            <summary>
            总部店铺id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.authMobileRequest.yz_open_id">
            <summary>
            有赞用户id.
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.subscriber.MemberExtraInfo">
            <summary>
            会员的额外信息.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.MemberExtraInfo.invited_yz_open_id">
            <summary>
            成为会员时 对应的邀请导购yzOpenId.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.MemberExtraInfo.invited_sl">
            <summary>
            成为会员时 对应的邀请导购标识.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.MemberExtraInfo.member_src_channel">
            <summary>
            成为会员的渠道.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.MemberExtraInfo.member_src_way">
            <summary>
            成为会员的方式.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.MemberExtraInfo.sub_kdt_id">
            <summary>
            成为会员的三方门店id.
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.subscriber.pushRequest">
            <summary>
             推送请求.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.app_id">
            <summary>
            对应开发者后台的app_id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.client_id">
            <summary>
            对应开发者后台的client_id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.id">
            <summary>
            消息标识.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.yz_open_id">
            <summary>
            有赞用户id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.kdt_id">
            <summary>
            店铺id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.kdt_name">
            <summary>
            店铺名称.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.msg">
            <summary>
            信息体.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.msg_id">
            <summary>
            信息体id.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.status">
            <summary>
            消息状态，对应消息业务类型。CUSTOMER_CARD_TAKEN-用户领取获得会员卡；CUSTOMER_CARD_BOUGHT-用户购买获得会员卡；CUSTOMER_CARD_UPGRADED-用户升级获得会员卡；CUSTOMER_CARD_GIVEN-用户通过商家发放获得会员卡；CUSTOMER_CARD_DELETED-用户删除会员卡；CUSTOMER_CARD_ACTIVATED-用户激活会员卡；CUSTOMER_CARD_SYNC_WECHAT-用户领卡到微信卡包；CUSTOMER_CARD_GRANTED_FROM_OPEN-三方通过开放平台发卡.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.type">
            <summary>
            消息类型.
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.subscriber.pushRequest.version">
            <summary>
            消息版本号.
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.user.userQueryRequest">
            <summary>
            用户查询列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.user.userQueryRequest.open_id_type">
            <summary>
              weixin_open_id类型，1-微信公众号，2-微信小程序，9-微信大账号，weixin_open_id不为空时该参数必填
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.user.userQueryRequest.weixin_open_id">
            <summary>
            微信体系的openId；和yz_open_id、mobile、weixin_union_id至少传一个
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.user.userQueryRequest.mobile">
            <summary>
            手机号，默认使用+86区号,如果需要使用其他地区的手机号需要加上区，例如"+54-123123"；和yz_open_id、weixin_union_id、weixin_open_id至少传一个
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.user.userQueryRequest.yz_open_id">
            <summary>
            有赞用户id，用户在有赞的唯一id。推荐使用；和mobile、weixin_union_id、weixin_open_id至少传一个
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.user.userQueryRequest.weixin_union_id">
            <summary>
            微信体系的unionId；和yz_open_id、mobile、weixin_open_id至少传一个
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.user.userQueryRequest.result_type_list">
            <summary>
            返回结果类型列表，0-手机帐号；1-微信公众号；2-微信小程序；9-微信大账号；默认0。开发者可以根据自己需要的选择传入。补充说明：；在微信公众号场景下，正常会进行两次授权，一次通过商家公众号授权，一次通过有赞公众号授权，分别产生不同的用户数据： 1）1-微信公众号：通过商家的公众号授权产生的用户 2）9-微信大账号：通过”有赞“公众号授权产生的用户
            </summary>
        </member>
        <member name="T:BPM.Domain.Requests.voucher.deleteVoucherRequest">
            <summary>
            删除优惠券请求
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.voucher.deleteVoucherRequest.yz_open_id">
            <summary>
            客户在有赞的唯一标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Requests.voucher.deleteVoucherRequest.operator_yz_open_id">
            <summary>
            操作员在有赞的唯一标识（通常为管理员）
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.customer.createCustomerEquityResponse">
            <summary>
            创建客户权益卡响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.createCustomerEquityResponse.card_no">
            <summary>
            权益卡号
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.createCustomerEquityResponse.validate_url">
            <summary>
            激活地址
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.createCustomerEquityResponse.is_success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.customer.CustomerInfoResponse">
            <summary>
            客户信息响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.CustomerInfoResponse.yz_open_id">
            <summary>
            openid
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.CustomerInfoResponse.social_info">
            <summary>
            客户信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.customer.CustomerInfoResponse.socialInfo">
            <summary>
            客户信息
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.customer.customerEquityResponse">
            <summary>
            客户级别响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.customerEquityResponse.items">
            <summary>
            权益等级集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.customerEquityResponse.page_size">
            <summary>
            每页条数。默认30条
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.customerEquityResponse.page">
            <summary>
            分页
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.customerEquityResponse.total">
            <summary>
            总数量
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.customer.customerLevelResponse">
            <summary>
            客户级别响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.customerLevelResponse.level_details">
            <summary>
            会员等级集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.is_enabled">
            <summary>
            是否启用；true-启用,false-禁用
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.min_growth">
            <summary>
            成长值会员的成长值门槛
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.term_days">
            <summary>
            生效持续天数,termType=2时，改值有效
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.level_value">
            <summary>
            等级值
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.level_alias">
            <summary>
            等级别名
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.name">
            <summary>
            注册会员
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.level_type">
            <summary>
             等级类型：1.免费类型2.付费类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.customer.levelItem.term_type">
            <summary>
            生效方式模式:1-永久有效，2-从领取开始，持续一段时长
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.product.batchProcuctResponse">
            <summary>
            在售商品响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.count">
            <summary>
            
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.items">
            <summary>
            商品信息集合
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.product.batchProcuctResponse.SkuExtensionAttributesItem">
            <summary>
             sku扩展属性
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.SkuExtensionAttributesItem.kdt_id">
            <summary>
            店铺ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.SkuExtensionAttributesItem.item_id">
            <summary>
            商品id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.SkuExtensionAttributesItem.sku_id">
            <summary>
             skuId
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.detail_url">
            <summary>
            商品详情链接
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.share_detail">
            <summary>
            商品价格和price值一致，单位:分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.post_fee">
            <summary>
            运费 单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.item_type">
            <summary>
             商品类型： 0—普通商品 3—UMP降价拍 5—外卖商品 10—分销商品 20—会员卡商品 21—礼品卡商品 22—团购券 25—批发商品 30—收银台商品 31—知识付费商品 35—酒店商品 40—美业商品 60—虚拟商品 61—电子卡券
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.num">
            <summary>
            
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.origin">
            <summary>
            划线价 单位：元
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.channel">
            <summary>
            店铺渠道类型0 - 网店 1 - 门店
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.title">
            <summary>
            派克钢笔
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.item_no">
            <summary>
            商品编码，商家可以自定义参数，支持英文和数据组合。商家为商品设置的外部编号，可与商家外部系统对接
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.update_time">
            <summary>
            最后更新时间 格式"yyyy-MM-dd HH:mm:ss"
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.price">
            <summary>
             商品价格 单位:分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.alias">
            <summary>
            商品别名 微商城店铺下商品唯一标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.post_type">
            <summary>
            运费类型，1是统一运费，2是运费模板
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.barcode">
            <summary>
            商品SPU维度的条形码，用于扫码快速搜索商品，使用场景如：扫码购、扫码搜索商品（仅支持实物商品）
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.created_time">
            <summary>
             创建时间，格式"yyyy-MM-dd HH:mm:ss"
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.quantity">
            <summary>
            
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.skuExtensionAttributes">
            <summary>
            总库存
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.item_id">
            <summary>
             商品id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.actual_quantity">
            <summary>
            实际总库存，当为门店渠道的时候，quantity会扩大1000倍，而网店不会，该字段会自动处理单位转换成合理单位
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.batchProcuctResponse.ItemsItem.root_kdt_id">
            <summary>
            有赞连锁总部店铺id
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.product.detailProdctResponse">
            <summary>
            商品明细响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.is_multi_unit_item">
            <summary>
            是否多单位商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.category_properties">
            <summary>
            商品类目属性相关
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.has_online_channel">
            <summary>
            是否有网店渠道
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.biz_code">
            <summary>
            业务标
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.auto_listing_time">
            <summary>
            商品定时上架（定时开售）的时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.sku_list">
            <summary>
            规格详情
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.hide_stock">
            <summary>
            隐藏库存,0-不隐藏,1-隐藏
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_props">
            <summary>
            商品属性数据
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.kdt_id">
            <summary>
            店铺在有赞的id标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_price_param">
            <summary>
            无规格价格属性集
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_id">
            <summary>
            商品Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_barcodes">
            <summary>
            更多条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.sold_num">
            <summary>
            商品总销量
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_weight">
            <summary>
            商品重量,商品无规格的时候使用，单位：克
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_type">
            <summary>
            商品类型
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.join_level_discount">
            <summary>
            是否参加会员折扣
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.has_multi_barcode">
            <summary>
            是否有一品多码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.spu">
            <summary>
            商品规格汇总信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.alias">
            <summary>
            商品别名
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.quantity">
            <summary>
            商品总库存
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.cid">
            <summary>
            商品类目Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.unit">
            <summary>
            库存单位,商品资料-单位库-单位名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.is_virtual">
            <summary>
            是否虚拟商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.goods_platform">
            <summary>
            商品参与的平台。0-普通、10-分销商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.root_kdt_id">
            <summary>
            有赞连锁总部店铺id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.stock_deduct_mode">
            <summary>
            库存扣减模式0：下单扣库存->拍减1：付款扣库存->拍占2：非预占付款扣库存->付减
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.tag_ids">
            <summary>
            商品分组（仅一级）列表即商品标签Id列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.has_offline_channel">
            <summary>
            是否有门店渠道
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.start_sale_num">
            <summary>
            商品起售数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.specifications">
            <summary>
            规格型号
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.root_item_id">
            <summary>
            商品关联总部商品id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.prepare_time">
            <summary>
            备货时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.channel">
            <summary>
            当前商品所属渠道 0 网店 1 门店 -1 多渠道店铺商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.inventory_type">
            <summary>
            存货类别：0产成品；1半成品；2原材料
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.actual_tag_ids">
            <summary>
            商品分组（含二级）列表即商品标签Id列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.created_time">
            <summary>
            创建时间，Unix时间戳，单位：毫秒
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.title">
            <summary>
            商品标题
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.is_display">
            <summary>
            是否上架商品。1—上架商品，0—不上架商品新增若不传该字段默认传1
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_barcode">
            <summary>
            商品条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.detailProdctResponse.item_no">
            <summary>
            商品编码（商家为商品设置的外部编号，可与商家外部系统对接）
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemPriceParam.cost_price">
            <summary>
            无规格商品的成本价
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemPriceParam.min_guide_price">
            <summary>
            最小建议售价（分）
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemPriceParam.max_guide_price">
            <summary>
            最大建议售价（分）
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemPriceParam.origin">
            <summary>
            显示在原价那里的信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemPriceParam.price">
            <summary>
            商品价格，单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.CategoryProperties.publics">
            <summary>
            公有属性
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.CategoryProperties.privates">
            <summary>
            私有属性
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.CategoryProperties.leaf_category_id">
            <summary>
            新类目ID叶子类目
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.CategoryProperties.status">
            <summary>
            类目状态 1：正常 2：禁用
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.CategoryProperties.category_names">
            <summary>
            类目全路径 按照 层级顺序 1、2、3 或者 叶子类目名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.PublicProperty.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.PublicProperty.pro_name">
            <summary>
            属性项名称,如：颜色
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.PublicProperty.val_names">
            <summary>
            属性值有多选情况，如：红色
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.PrivateProperty.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.PrivateProperty.pro_name">
            <summary>
            属性项名称,如：颜色
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.PrivateProperty.val_names">
            <summary>
            属性值有多选情况，如：红色
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.min_guide_price">
            <summary>
            有规格时最低建议售价，单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.delivery_price">
            <summary>
            配送价（配销价）单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_no">
            <summary>
            sku编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_props">
            <summary>
            规格属性
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.weight">
            <summary>
            规格的重量，单位g
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.max_guide_price">
            <summary>
            有规格时最高建议售价，单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_modified_time">
            <summary>
            Sku最后修改日期，Unix时间戳，单位：毫秒
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.quantity">
            <summary>
            Sku的商品的数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_created_time">
            <summary>
            Sku创建日期，Unix时间戳，单位：毫秒
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.root_sku_id">
            <summary>
            总部商品规格Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.cost_price">
            <summary>
            Sku的成本价，单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sold_num">
            <summary>
            SKU销量
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_barcode">
            <summary>
            规格条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.price">
            <summary>
            商品的这个Sku的价格；单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.standard_price">
            <summary>
            标准价单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.prepare_time">
            <summary>
            备货时间
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.stock_num">
            <summary>
            库存数量
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_barcodes">
            <summary>
            更多条码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.is_fenxiao_sell">
            <summary>
            是否可售0.不可售1.可售仅在分销商品时生效非分销商品为null
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Sku.sku_id">
            <summary>
            商品规格Id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.SkuProp.prop_value_id">
            <summary>
            规格值名称id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.SkuProp.prop_name">
            <summary>
            规格项名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.SkuProp.prop_name_id">
            <summary>
            规格项名称id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.SkuProp.prop_value_name">
            <summary>
            规格值名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemProps.prop_id">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemProps.prop_name">
            <summary>
            属性名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.ItemProps.text_models">
            <summary>
            属性项关联属性值列表
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.TextModel.price">
            <summary>
            属性值价格，单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.TextModel.prop_text_name">
            <summary>
            属性值名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.TextModel.prop_text_id">
            <summary>
            属性值id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.Spu.spu_id">
            <summary>
            无规格商品规格id
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.product.onSaleProcuctResponse">
            <summary>
            在售商品响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.items">
            <summary>
            商品集合
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.page_size">
            <summary>
            每页条数。默认30条
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.page">
            <summary>
            分页
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.count">
            <summary>
            总数量
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.product.onSaleProcuctResponse.Items">
            <summary>
             商品明细
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.item_no">
            <summary>
            商品编码，商家可以自定义参数，支持英文和数据组合
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.barcode">
            <summary>
            商品SPU维度的条形码，用于扫码快速搜索商品，使用场景如：扫码购、扫码搜索商品（仅支持实物商品）
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.title">
            <summary>
            商品名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.item_id">
            <summary>
            商品Id，微商城店铺下商品唯一标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.price">
            <summary>
            商品价格，单位：分
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.origin">
            <summary>
            划线价，单位：元
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.item_type">
            <summary>
            商品类型：0—普通商品 3—UMP降价拍 5—外卖商品 10—分销商品 20—会员卡商品 21—礼品卡商品 22—团购券 25—批发商品 30—收银台商品 31—知识付费商品 35—酒店商品 40—美业商品 60—虚拟商品 61—电子卡券
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.update_time">
            <summary>
            最后更新时间 格式"yyyy-MM-dd HH:mm:ss"
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.created_time">
            <summary>
             创建时间，格式"yyyy-MM-dd HH:mm:ss"
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.root_kdt_id">
            <summary>
            有赞连锁总部店铺id，仅供有赞连锁场景下使用。有赞平台生成，在有赞平台唯一，用于判断信息属于哪一个总部。
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.root_item_id">
            <summary>
            总部商品id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.onSaleProcuctResponse.Items.channel">
            <summary>
            店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.product.shopProcuctStockResponse">
            <summary>
            门店商品库存跟新响应
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.shopProcuctStockResponse.code">
            <summary>
             错误码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.shopProcuctStockResponse.message">
            <summary>
             错误信息
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.shopProcuctStockResponse.sku_id">
            <summary>
            总店商品的skuId，如果操作的是无规格商品则为null
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.shopProcuctStockResponse.node_kdt_id">
            <summary>
            分店店铺id
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.product.shopProcuctStockResponse.is_success">
             <summary>
            是否更新成功
             </summary>
        </member>
        <member name="T:BPM.Domain.Responses.shop.shopResponse">
            <summary>
            门店商品
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.items">
            <summary>
            门店集合
            </summary>
        </member>
        <member name="T:BPM.Domain.Responses.shop.shopResponse.Items">
            <summary>
             明细
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.kdt_id">
            <summary>
            店铺在有赞的id标识
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.county_id">
            <summary>
            区编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.join_type">
            <summary>
            经营类型1:直营，2:加盟
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.address">
            <summary>
            详细地址
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.shop_code">
            <summary>
            三方门店编码
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.county">
            <summary>
            区名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.shop_display_no">
            <summary>
             区名称
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.business_relation_kdt_id">
            <summary>
            经营关联店铺
            </summary>
        </member>
        <member name="P:BPM.Domain.Responses.shop.shopResponse.Items.shop_name">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.shop_code">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.shop_name">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.province">
            <summary>
            省份
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.city">
            <summary>
            城市
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.area">
            <summary>
            区县
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.longitude">
            <summary>
            精度
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.address">
            <summary>
            详细地址
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.latitude">
            <summary>
            纬度
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.tel">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopBaseDto.intro">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:BPM.Business.Entitys.Dto.shop.shopCrInput">
            <summary>
            创建门店输入.
            </summary>
        </member>
        <member name="T:BPM.Business.Entitys.Dto.shop.shopListOutput">
            <summary>
            门店列表输出.
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListOutput.id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListOutput.creatorTime">
            <summary>
            创建时间.
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListOutput.creatorUser">
            <summary>
            创建人.
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListOutput.lastModifyTime">
            <summary>
            修改时间.
            </summary>
        </member>
        <member name="T:BPM.Business.Entitys.Dto.shop.shopListQuery">
            <summary>
            门店列表查询.
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListQuery.shop_id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListQuery.transaction_id">
            <summary>
            交易单号
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListQuery.out_trade_no">
            <summary>
            商户订单号
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListQuery.startTime">
            <summary>
            开始时间.
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.Dto.shop.shopListQuery.endTime">
            <summary>
            结束时间.
            </summary>
        </member>
        <member name="T:BPM.Business.Entitys.shop.shopEntity">
            <summary>
            门店信息
            版 本：V3.2
            版 权：中畅源科技开发有限公司（https://www.szclouds.com）
            作 者：Aarons
            日 期：2023-01-05.
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.id">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.store_no">
            <summary>
            门店编号
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.store_name">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.parent_id">
            <summary>
            上级id
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.status">
            <summary>
            门店名称
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.province">
            <summary>
            省份
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.city">
            <summary>
            城市
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.area">
            <summary>
            区县
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.address">
            <summary>
            详细地址
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.longitude">
            <summary>
            精度
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.latitude">
            <summary>
            纬度
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.tel">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.intro">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.source_no">
            <summary>
            外部门店id
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.net_source_no">
            <summary>
            网店id
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.created_user_id">
            <summary>
            创建人id
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.created_date">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.modify_user_id">
            <summary>
            更新人id
            </summary>
        </member>
        <member name="P:BPM.Business.Entitys.shop.shopEntity.modify_date">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:BPM.SubDev.Entitys.Entity.TestEntity">
            <summary>
            测试用例
            版 本：V3.2
            版 权：BPM信息技术有限公司
            作 者：BPM开发平台组
            日 期：2021-06-01.
            </summary>
        </member>
        <member name="P:BPM.SubDev.Entitys.Entity.TestEntity.EnCode">
            <summary>
            编码.
            </summary>
        </member>
        <member name="P:BPM.SubDev.Entitys.Entity.TestEntity.FullName">
            <summary>
            名称.
            </summary>
        </member>
    </members>
</doc>
