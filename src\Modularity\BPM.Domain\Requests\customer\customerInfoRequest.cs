﻿using BPM.DependencyInjection;
using BPM.Domain.Entity.customer;

namespace BPM.Domain.Requests.customer;

/// <summary>
/// 客户信息请求
/// </summary>
[SuppressSniffer]
public class customerInfoRequest
{
    /// <summary>
    /// 客户实体列表.
    /// </summary>
    public List<customerEntity> Customers { get; set; }

    /// <summary>
    /// 客户权益列表.
    /// </summary>
    public List<customerEquityEntity> Equitys { get; set; }

}