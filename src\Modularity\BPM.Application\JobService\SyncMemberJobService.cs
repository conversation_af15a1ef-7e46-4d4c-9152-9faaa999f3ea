using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;

namespace BPM.Application;

/// <summary>
/// 本地任务-同步会员信息.
/// </summary>
[JobDetail("job_sync_member", Description = "同步会员信息", GroupName = "BuiltIn", Concurrent = true)]
public class SyncMemberJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 会员服务.
    /// </summary>
    private readonly MemberService _memberService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public SyncMemberJobService(IServiceScopeFactory serviceScopeFactory, MemberService memberService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _memberService = memberService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _memberService.syncMember();
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 