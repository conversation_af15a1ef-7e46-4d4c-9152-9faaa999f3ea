info: 2025-08-12 09:23:17.2973074 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      开始处理时间段: 2025-08-11 17:40:00 至 2025-08-11 17:41:00，总跨度: 0.0 小时
info: 2025-08-12 09:23:17.3602319 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      开始处理时间段商品数据 - 请求参数: channel=-1, q=, order_by=update_time:desc
info: 2025-08-12 09:23:17.4379820 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      时间范围: 2025-08-11 17:40:00 至 2025-08-11 17:41:00
info: 2025-08-12 09:23:17.5033983 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment初始化完成 - 页大小: 200
info: 2025-08-12 09:23:17.5504683 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment请求参数序列化成功，准备调用有赞API
info: 2025-08-12 09:23:17.6113355 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      开始调用有赞API获取商品数据，时间段: 2025-08-11 17:40:00 至 2025-08-11 17:41:00
info: 2025-08-12 09:23:18.2844947 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求URL: https://open.youzanyun.com/api/youzan.items.onsale.get/3.0.0
info: 2025-08-12 09:23:18.3213601 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求体: {
        "channel": -1,
        "page_no": 1,
        "page_size": 200,
        "order_by": "update_time:desc",
        "q": "",
        "update_time_start": 1754905200000,
        "update_time_end": 1754905260000,
        "update_time_start_str": "2025-8-11 17:40",
        "update_time_end_str": "2025-8-11 17:41"
      }
info: 2025-08-12 09:23:18.4074602 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[100] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Start processing HTTP request POST https://open.youzanyun.com/api/youzan.items.onsale.get/3.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:18.4759835 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[100] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Sending HTTP request POST https://open.youzanyun.com/api/youzan.items.onsale.get/3.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:18.7305324 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[101] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler+Log.RequestEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      Received HTTP response headers after 222.5381ms - 200
info: 2025-08-12 09:23:18.7406085 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[101] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler+Log.RequestPipelineEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      End processing HTTP request after 364.5561ms - 200
info: 2025-08-12 09:23:18.7654098 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口响应: {"trace_id":"yz7-0a350a45-1754961787825-926857","code":200,"data":{"count":3,"items":[{"page_url":"packages/goods/detail/index","is_virtual":0,"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=1y8cp05jds002nq","share_detail":4000,"post_fee":0,"item_type":0,"num":0,"origin":"","channel":1,"title":"青青世界小男130cm夏礼服上衣 (顺)","item_no":"6926352901311","update_time":"2025-08-11 17:40:58","price":4000,"alias":"1y8cp05jds002nq","post_type":1,"barcode":"6926352901311","created_time":"2025-08-11 16:33:13","image":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","tag_ids":[310837814],"biz_code":"000000000000","sub_title":"","quantity":0,"item_id":4272647033,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg","url":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png","combine":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","item_source":0,"goods_platform":0,"actual_quantity":"0","share_title":"青青世界小男130cm夏礼服上衣 (顺)","root_item_id":0,"delivery_template":{},"root_kdt_id":0,"video_id":0,"ability_mark_codes":[40002,40005,40025,10005,40008,30005,40001,40006,40013]},{"page_url":"packages/goods/detail/index","is_virtual":0,"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=3f5xr76vdfz3mfg","share_detail":4000,"post_fee":1000,"item_type":0,"num":0,"origin":"","channel":0,"title":"青青世界小男130cm夏礼服上衣 (顺)","item_no":"6926352901311","update_time":"2025-08-11 17:40:58","price":4000,"alias":"3f5xr76vdfz3mfg","post_type":1,"barcode":"6926352901311","created_time":"2025-08-11 16:33:13","image":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","tag_ids":[310835314,297525093,362184192],"biz_code":"000000000000","sub_title":"","quantity":0,"item_id":4272646387,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg","url":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png","combine":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","item_source":0,"goods_platform":0,"actual_quantity":"0","share_title":"青青世界小男130cm夏礼服上衣 (顺)","root_item_id":0,"delivery_template":{},"root_kdt_id":0,"video_id":0,"ability_mark_codes":[30002,40005,10022,10020,10005,10021,40006,20001,40013,40002,10038,20002,40025,10043,10012,40008,50001,30007,40016]},{"page_url":"packages/goods/detail/index","is_virtual":0,"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=26zns6ex7hdcya8","share_detail":2980,"post_fee":0,"item_type":10,"num":0,"origin":"","channel":0,"title":"趣味新乘除思维训练一本通","item_no":"","update_time":"2025-08-11 17:40:29","price":2980,"alias":"26zns6ex7hdcya8","post_type":1,"barcode":"","created_time":"2025-08-11 17:40:01","image":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/0/w/240/h/240/q/75","tag_ids":[278319838,360977794],"biz_code":"000000000000","sub_title":"","quantity":4996,"item_id":4272845791,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191237,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191247,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191417,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191273,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:07","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191288,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","item_source":0,"goods_platform":0,"actual_quantity":"4996","share_title":"趣味新乘除思维训练一本通","root_item_id":0,"delivery_template":{},"root_kdt_id":0,"video_id":0,"ability_mark_codes":[30002,10020,10038,70003,20002,10043,10029,40008,50001,30007,40016]}]},"success":true,"message":"successful"}
info: 2025-08-12 09:23:18.8271386 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      时间段 2025-08-11 17:40:00 至 2025-08-11 17:41:00 获取商品数量: 3
info: 2025-08-12 09:23:18.8701731 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求URL: https://open.youzanyun.com/api/youzan.items.onsale.get/3.0.0
info: 2025-08-12 09:23:18.9057312 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求体: {
        "channel": -1,
        "page_no": 1,
        "page_size": 200,
        "order_by": "update_time:desc",
        "q": "",
        "update_time_start": 1754905200000,
        "update_time_end": 1754905260000,
        "update_time_start_str": "2025-8-11 17:40",
        "update_time_end_str": "2025-8-11 17:41"
      }
info: 2025-08-12 09:23:18.9638315 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[100] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Start processing HTTP request POST https://open.youzanyun.com/api/youzan.items.onsale.get/3.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:19.0280483 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[100] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Sending HTTP request POST https://open.youzanyun.com/api/youzan.items.onsale.get/3.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:19.4188146 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[101] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler+Log.RequestEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      Received HTTP response headers after 349.8327ms - 200
info: 2025-08-12 09:23:19.4299407 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[101] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler+Log.RequestPipelineEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      End processing HTTP request after 494.547ms - 200
info: 2025-08-12 09:23:19.4469408 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口响应: {"trace_id":"yz7-0a350a45-1754961788415-531416","code":200,"data":{"count":3,"items":[{"page_url":"packages/goods/detail/index","is_virtual":0,"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=1y8cp05jds002nq","share_detail":4000,"post_fee":0,"item_type":0,"num":0,"origin":"","channel":1,"title":"青青世界小男130cm夏礼服上衣 (顺)","item_no":"6926352901311","update_time":"2025-08-11 17:40:58","price":4000,"alias":"1y8cp05jds002nq","post_type":1,"barcode":"6926352901311","created_time":"2025-08-11 16:33:13","image":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","tag_ids":[310837814],"biz_code":"000000000000","sub_title":"","quantity":0,"item_id":4272647033,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg","url":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png","combine":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","item_source":0,"goods_platform":0,"actual_quantity":"0","share_title":"青青世界小男130cm夏礼服上衣 (顺)","root_item_id":0,"delivery_template":{},"root_kdt_id":0,"video_id":0,"ability_mark_codes":[40002,40008,40005,40013,30005,40001,40025,10005,40006]},{"page_url":"packages/goods/detail/index","is_virtual":0,"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=3f5xr76vdfz3mfg","share_detail":4000,"post_fee":1000,"item_type":0,"num":0,"origin":"","channel":0,"title":"青青世界小男130cm夏礼服上衣 (顺)","item_no":"6926352901311","update_time":"2025-08-11 17:40:58","price":4000,"alias":"3f5xr76vdfz3mfg","post_type":1,"barcode":"6926352901311","created_time":"2025-08-11 16:33:13","image":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","tag_ids":[310835314,297525093,362184192],"biz_code":"000000000000","sub_title":"","quantity":0,"item_id":4272646387,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg","url":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png","combine":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","item_source":0,"goods_platform":0,"actual_quantity":"0","share_title":"青青世界小男130cm夏礼服上衣 (顺)","root_item_id":0,"delivery_template":{},"root_kdt_id":0,"video_id":0,"ability_mark_codes":[40002,40005,30002,40013,50001,40025,10005,10021,30007,40016,20002,40008,10012,10022,20001,10020,10043,40006,10038]},{"page_url":"packages/goods/detail/index","is_virtual":0,"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=26zns6ex7hdcya8","share_detail":2980,"post_fee":0,"item_type":10,"num":0,"origin":"","channel":0,"title":"趣味新乘除思维训练一本通","item_no":"","update_time":"2025-08-11 17:40:29","price":2980,"alias":"26zns6ex7hdcya8","post_type":1,"barcode":"","created_time":"2025-08-11 17:40:01","image":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/0/w/240/h/240/q/75","tag_ids":[278319838,360977794],"biz_code":"000000000000","sub_title":"","quantity":4996,"item_id":4272845791,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191237,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191247,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/FnNy8KBGRkpPBikWlntz8hYHMfza.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191417,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/FgkBZdckJnZq6CB0qigkQrmtW5Uy.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191273,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/FlTBj8gzpGzNp7BI2ioQVVNSRnRQ.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"},{"thumbnail":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:08","medium":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg?imageView2/2/w/600/h/0/q/75/format/jpg","id":9522191288,"url":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg","combine":"https://img.yzcdn.cn/upload_files/2025/08/06/Ft-pH0UmODhQ_d7YAa3Jfd2BPBJr.jpg?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2025/08/06/Fmg742cF6Sbw27gULgbN0zszNJIW.jpg?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","item_source":0,"goods_platform":0,"actual_quantity":"4996","share_title":"趣味新乘除思维训练一本通","root_item_id":0,"delivery_template":{},"root_kdt_id":0,"video_id":0,"ability_mark_codes":[70003,20002,40008,30002,50001,10020,30007,10029,10043,40016,10038]}]},"success":true,"message":"successful"}
info: 2025-08-12 09:23:19.4834450 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      处理页码: 1, 页大小: 200, 总数: 3
info: 2025-08-12 09:23:19.5111656 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment第 1 页筛选出符合条件的商品ID数量: 2 (item_type=0)
info: 2025-08-12 09:23:19.5758430 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment查询到已存在的商品数量: 2
info: 2025-08-12 09:23:19.5881992 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment已将 2 个商品写入缓存
info: 2025-08-12 09:23:19.5959269 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment开始处理第 1 页商品，总数: 3
info: 2025-08-12 09:23:19.6038054 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment商品已存在 - barcode: 6926352901311, item_id: 4272647033, 缓存修改时间: 2025-08-01 00:00:00, 有赞更新时间: 2025-08-11 17:40:58
info: 2025-08-12 09:23:19.6116713 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment商品需要更新 - barcode: 6926352901311, item_id: 4272647033, 开始删除旧数据
info: 2025-08-12 09:23:19.7715078 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      开始删除商品ID: 4272647033
info: 2025-08-12 09:23:19.9257874 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      成功删除商品 4272647033，删除了 1 条商品记录和 1 条SKU记录
info: 2025-08-12 09:23:19.9328010 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment成功删除商品和缓存 - barcode: 6926352901311, item_id: 4272647033, product_id: 4272647033
info: 2025-08-12 09:23:19.9383451 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment商品已加入待处理队列 - barcode: 6926352901311, item_id: 4272647033, 当前队列长度: 1
info: 2025-08-12 09:23:19.9487440 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment商品已存在 - barcode: 6926352901311, item_id: 4272646387, 缓存修改时间: 2025-08-11 17:40:58, 有赞更新时间: 2025-08-11 17:40:58
info: 2025-08-12 09:23:19.9544818 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment商品无需更新，跳过 - barcode: 6926352901311, item_id: 4272646387, title: 青青世界小男130cm夏礼服上衣 (顺)
info: 2025-08-12 09:23:19.9644955 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment跳过非普通商品 - barcode: , item_id: 4272845791, item_type: 10
info: 2025-08-12 09:23:19.9699436 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment已处理完所有页面 - 当前页: 1, 页大小: 200, 总数: 3
info: 2025-08-12 09:23:19.9798624 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment开始批量处理商品，总数: 1, 批大小: 20
info: 2025-08-12 09:23:19.9871440 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment批量处理第 1 批商品，数量: 1
info: 2025-08-12 09:23:19.9992717 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment当前批次按channel分组，共 1 个channel
info: 2025-08-12 09:23:20.0081805 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment处理channel=1的商品，数量: 1
info: 2025-08-12 09:23:20.0342463 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      ProcessTimeSegment收集到商品ID数量: 1 (channel=1)
info: 2025-08-12 09:23:20.1112127 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      开始批量获取商品SKU信息 - 商品数量: 1, channel: 1
info: 2025-08-12 09:23:20.1185214 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList开始批量获取商品的SKU信息，商品数量: 1, channel: 1
info: 2025-08-12 09:23:20.1289860 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList开始分析商品规格类型，总数: 1
info: 2025-08-12 09:23:20.1356650 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList获取商品详情 - item_id: 4272647033
info: 2025-08-12 09:23:20.1507144 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求URL: https://open.youzanyun.com/api/youzan.item.detail.get/1.0.0
info: 2025-08-12 09:23:20.1645277 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求体: {
        "item_id": "4272647033"
      }
info: 2025-08-12 09:23:20.1840229 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[100] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Start processing HTTP request POST https://open.youzanyun.com/api/youzan.item.detail.get/1.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:20.2209385 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[100] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Sending HTTP request POST https://open.youzanyun.com/api/youzan.item.detail.get/1.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:20.3675202 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[101] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler+Log.RequestEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      Received HTTP response headers after 126.8062ms - 200
info: 2025-08-12 09:23:20.3830275 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[101] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler+Log.RequestPipelineEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      End processing HTTP request after 207.2871ms - 200
info: 2025-08-12 09:23:20.3971359 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口响应: {"trace_id":"yz7-0a350a45-1754961789576-511374","code":200,"data":{"hide_stock":0,"post_sale_param":{"is_return_msg":false},"item_type":0,"channel":1,"category_properties":{"publics":[],"privates":[]},"item_barcode":"6926352901311","need_customs_check":false,"created_time":1754901193000,"tag_ids":[310837814],"images":[{"image_url":"https://img01.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png"}],"biz_code":"000000000000","sku_picture_display_config":false,"purchase_permission":{"quota_cycle":0,"purchase_right":false,"buy_quota":0},"stock_deduct_mode":0,"item_delivery_param":{"postage":0},"goods_platform":0,"channels":[],"spu":{"spu_id":14898031443},"messages":[],"root_item_id":0,"root_kdt_id":0,"extra_param":{"item_channel_extra":{"category_id":99289509}},"is_display":1,"cid":8000000,"desc":"","ability_mark_codes":[40002,40008,40005,40025,40006,30005,10005,40013,40001,30002,10020,10023],"item_props":[],"is_virtual":0,"template":{"template_title":"普通版","template_id":0},"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=1y8cp05jds002nq&from=wsc&kdtfrom=wsc","auto_listing_time":0,"item_price_param":{"price":4000,"origin":""},"title":"青青世界小男130cm夏礼服上衣 (顺)","join_level_discount":true,"item_no":"6926352901311","actual_tag_ids":[310837814],"kdt_id":165085058,"is_serial_item":0,"alias":"1y8cp05jds002nq","need_customs_info":false,"summary":"","need_customs_info_with_picture":false,"quantity":0,"is_multi_unit_item":0,"item_id":4272647033,"sku_value_props":[],"sold_num":0,"size_group":{"sizes":[]},"sku_list":[],"video_id":0},"success":true,"message":"successful"}
info: 2025-08-12 09:23:20.4442989 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList成功解析商品详情 - item_id: 4272647033, item_no: 6926352901311
info: 2025-08-12 09:23:20.4663720 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList检测到无规格商品 - item_id: 4272647033, item_no: 6926352901311
info: 2025-08-12 09:23:20.5015110 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList无规格商品已加入批量处理队列 - item_id: 4272647033, item_no: 6926352901311
info: 2025-08-12 09:23:20.5352936 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList开始批量处理无规格商品 - 数量: 1
info: 2025-08-12 09:23:20.5632599 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList开始批量获取1个无规格商品的SKU信息
info: 2025-08-12 09:23:20.5922593 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求URL: https://open.youzanyun.com/api/youzan.item.custom.batch.get/1.0.0
info: 2025-08-12 09:23:20.6195121 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口请求体: {
        "channel": 1,
        "item_no_list": [
          "6926352901311"
        ]
      }
info: 2025-08-12 09:23:20.6602487 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[100] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Start processing HTTP request POST https://open.youzanyun.com/api/youzan.item.custom.batch.get/1.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:20.7091651 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[100] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] async Task<HttpResponseMessage> Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.SendCoreAsync(HttpRequestMessage request, bool useAsync, CancellationToken cancellationToken)+Core(?)
      Sending HTTP request POST https://open.youzanyun.com/api/youzan.item.custom.batch.get/1.0.0?access_token=6e0e11292cfea7daaa4e87ff7ecd6bf
info: 2025-08-12 09:23:20.9108970 +08:00 Tuesday L System.Net.Http.HttpClient.Default.ClientHandler[101] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler+Log.RequestEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      Received HTTP response headers after 169.1448ms - 200
info: 2025-08-12 09:23:20.9184849 +08:00 Tuesday L System.Net.Http.HttpClient.Default.LogicalHandler[101] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [Microsoft.Extensions.Http.dll] void Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler+Log.RequestPipelineEnd(ILogger logger, HttpResponseMessage response, TimeSpan duration, Func<string, bool> shouldRedactHeaderValue)
      End processing HTTP request after 278.1776ms - 200
info: 2025-08-12 09:23:20.9359069 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      有赞接口响应: {"trace_id":"yz7-0a350a45-1754961790062-8832","code":200,"data":{"count":1,"items":[{"detail_url":"https://h5.youzan.com/v2/showcase/goods?alias=1y8cp05jds002nq","share_detail":4000,"post_fee":0,"item_type":0,"num":0,"origin":"","channel":1,"title":"青青世界小男130cm夏礼服上衣 (顺)","item_no":"6926352901311","update_time":"2025-08-11 17:40:58","price":4000,"alias":"1y8cp05jds002nq","post_type":1,"barcode":"6926352901311","created_time":"2025-08-11 16:33:13","image":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","sub_title":"","quantity":0,"skuExtensionAttributes":[],"item_id":4272647033,"item_imgs":[{"thumbnail":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/290/h/290/q/75/format/jpg","created":"2025-08-12 09:23:10","medium":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg","url":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png","combine":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/2/w/600/h/0/q/75/format/jpg"}],"share_icon":"https://img.yzcdn.cn/upload_files/2020/05/19/FhO5Y19m1QWzD_FXF3lxF8IllIgN.png?imageView2/0/w/240/h/240/q/75","classid":"youzan_goods_selling","actual_quantity":"0","share_title":"青青世界小男130cm夏礼服上衣 (顺)","delivery_template":{},"root_kdt_id":0,"video_id":0}]},"success":true,"message":"successful"}
info: 2025-08-12 09:23:21.0116421 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      批量获取无规格SKU成功，处理了 1 个商品
info: 2025-08-12 09:23:21.0343519 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList批量获取无规格SKU完成 - 返回SKU数量: 1
info: 2025-08-12 09:23:21.0607216 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList开始分配无规格SKU到对应商品
info: 2025-08-12 09:23:21.0910145 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList找到匹配的商品ID - sku_sn: 6926352901311, 匹配数量: 1
info: 2025-08-12 09:23:21.1177683 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList成功将无规格SKU匹配到商品 - item_id: 4272647033, sku_sn: 6926352901311, sku_id: 724761466263044165
info: 2025-08-12 09:23:21.1454032 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList批量获取所有商品SKU完成，成功处理 1 个商品的SKU信息
info: 2025-08-12 09:23:21.1698856 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      getBatchYzProductSkuList商品SKU统计 - item_id: 4272647033, SKU数量: 1
info: 2025-08-12 09:23:21.2442532 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      开始批量插入 1 个商品和 1 个SKU
info: 2025-08-12 09:23:21.2915907 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      已插入商品批次 1/1，本批次 1 条
info: 2025-08-12 09:23:21.3127204 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      已插入SKU批次 1/1，本批次 1 条
info: 2025-08-12 09:23:21.3265063 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      批量插入完成：1 个商品和 1 个SKU
info: 2025-08-12 09:23:21.3319811 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-8850a5e4994065ee9ac2da083f70c014-13d7a46c28c0bd12-00'
      [BPM.dll] void BPM.Logging.StringLoggingPart.LogInformation()
      批量插入 1 个商品和 1 个SKU
