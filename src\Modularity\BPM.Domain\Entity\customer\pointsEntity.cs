using SqlSugar;

namespace BPM.Domain.Entity.customer;

/// <summary>
/// 积分历史记录实体
/// </summary>
[SugarTable("Points")]
[Tenant("IPOS-CRM")]
public class pointsEntity
{
    /// <summary>
    /// 唯一标识位（主键）
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string unique_id { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string mobile { get; set; }
    
    /// <summary>
    ///  变动的积分值
    /// </summary>
    public long amount { get; set; }

    /// <summary>
    ///  用户当前积分值
    /// </summary>
    public long total { get; set; }
    
    /// <summary>
    /// 积分变动描述
    /// </summary>
    public string description { get; set; }
    
    /// <summary>
    /// 积分变动时间
    /// </summary>
    public DateTime create_time { get; set; }
    
    /// <summary>
    /// 事件类型
    /// </summary>
    public int event_type { get; set; }
    
    /// <summary>
    /// 外部业务唯一标识
    /// </summary>
    public string biz_token { get; set; }
    
    /// <summary>
    /// 积分变动业务标识
    /// </summary>
    public string biz_value { get; set; }
    
    /// <summary>
    /// 客户在有赞的唯一id
    /// </summary>
    public string yz_open_id { get; set; }
    
    /// <summary>
    /// 分店kdt_id
    /// </summary>
    public long node_kdt_id { get; set; }
    
    /// <summary>
    /// 是否是保护期
    /// </summary>
    public bool is_protected { get; set; }
    
    /// <summary>
    /// client_id做md5操作后的值
    /// </summary>
    public string client_hash { get; set; }
    
    /// <summary>
    /// 开放平台用户Id
    /// </summary>
    public string open_user_id { get; set; }
    
    /// <summary>
    /// 这笔积分操作的来源类型
    /// </summary>
    public int? operate_source_type { get; set; }
    
    /// <summary>
    /// 第三方业务标识
    /// </summary>
    public string third_biz_value { get; set; }
} 