﻿using SqlSugar;

namespace BPM.Domain.Entity.shop;

/// <summary>
/// 门店支付宝配置信息
/// 版 本：V3.2
/// 版 权：中畅源科技开发有限公司（https://www.szclouds.com）
/// 作 者：Aarons
/// 日 期：2023-01-05.
/// </summary>
[SugarTable("SHOP_ALIPAY_CONFIG")]
[Tenant("IPOS-PRODUCT")]
public class shopAlipayConfigEntity
{
    /// <summary>
    /// 门店编号
    /// </summary>
    [SugarColumn(ColumnName = "F_ShopId")]
    public string shop_id { get; set; }

    /// <summary>
    /// 应用编号
    /// </summary>
    [SugarColumn(ColumnName = "F_AppId")]
    public string app_id { get; set; }

    /// <summary>
    /// 商户私钥
    /// </summary>
    [SugarColumn(ColumnName = "F_AppPrivatekey")]
    public string app_private_key { get; set; }

    /// <summary>
    /// 商户公钥
    /// </summary>
    [SugarColumn(ColumnName = "F_AppPublickey")]
    public string app_public_key { get; set; }

    /// <summary>
    /// 阿里公钥
    /// </summary>
    [SugarColumn(ColumnName = "F_AlipayPublicKey")]
    public string alipay_public_key { get; set; }

    /// <summary>
    /// 签名类型
    /// </summary>
    [SugarColumn(ColumnName = "F_SignType")]
    public string sign_type { get; set; }
}

