﻿using BPM.Common.Models;
using BPM.JsonSerialization;
using Newtonsoft.Json;

namespace BPM.@(@Model.NameSpace).<EMAIL>;
 
/// <summary>
/// @(@Model.BusName)修改输入参数.
/// </summary>
public class @(@Model.ClassName)CrInput
{
@foreach (var column in Model.TableField)
{
@if(column.PrimaryKey)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(Model.PrimaryKeyPolicy == 1 ? "string" : "long") @column.LowerColumnName { get; set; }
@:
}else if(column.bpmKey != null){
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
    @:public List<string> @column.LowerColumnName { get; set; }
@:
}else{
    @:public string @column.LowerColumnName { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:public List<List<string>> @column.LowerColumnName { get; set; }
@:
}else{
    @:public List<string> @column.LowerColumnName { get; set; }
@:
}
break;
case "switch":
    @:[JsonConverter(typeof(BoolJsonConverter))]
    @:public bool @column.LowerColumnName { get; set; }
@:
break;
case "checkbox":
    @:public List<string> @column.LowerColumnName { get; set; }
@:
break;
case "radio":
    @:public string @column.LowerColumnName { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:public List<FileControlsModel> @column.LowerColumnName { get; set; }
@:
break;
case "createTime":
case "modifyTime":
    
@:
break;
default:
    @:public @(column.NetType) @column.LowerColumnName { get; set; }
@:
break;
}
}
}
}