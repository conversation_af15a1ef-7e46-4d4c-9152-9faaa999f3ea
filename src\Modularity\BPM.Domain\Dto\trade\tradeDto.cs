﻿using BPM.DependencyInjection;
using BPM.Domain.Dto.trade;

namespace BPM.Domain.Dto.points;

/// <summary>
/// 交易信息.
/// </summary>
[SuppressSniffer]
public class tradeDto
{
    /// <summary>
    ///  交易物流信息结构体.
    /// </summary>
    //public DeliveryOrder delivery_order { get; set; }

    /// <summary>
    ///  交易基础信息结构体.
    /// </summary>
    public FullOrderInfo full_order_info { get; set; }

    /// <summary>
    ///  交易优惠信息结构体.
    /// </summary>
    //public OrderPromotion order_promotion { get; set; }

    /// <summary>
    ///  订单扫码收银信息.
    /// </summary>
   // public QrInfo qr_info { get; set; }

    /// <summary>
    ///  交易退款信息结构体.
    /// </summary>
   // public RefundOrder refund_order { get; set; }
}
