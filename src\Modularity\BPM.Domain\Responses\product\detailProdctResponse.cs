﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.product;

/// <summary>
/// 商品明细响应
/// </summary>
[SuppressSniffer]
public class detailProdctResponse
{
    /// <summary>
    /// 是否多单位商品
    /// </summary>
    public int is_multi_unit_item { get; set; }

    /// <summary>
    /// 商品类目属性相关
    /// </summary>
    public CategoryProperties category_properties { get; set; }

    /// <summary>
    /// 是否有网店渠道
    /// </summary>
    public bool has_online_channel { get; set; }

    /// <summary>
    /// 业务标
    /// </summary>
    public string biz_code { get; set; }

    /// <summary>
    /// 商品定时上架（定时开售）的时间
    /// </summary>
    public long auto_listing_time { get; set; }

    /// <summary>
    /// 规格详情
    /// </summary>
    public List<Sku> sku_list { get; set; }

    /// <summary>
    /// 隐藏库存,0-不隐藏,1-隐藏
    /// </summary>
    public int hide_stock { get; set; }

    /// <summary>
    /// 商品属性数据
    /// </summary>
    public List<ItemProps> item_props { get; set; }

    /// <summary>
    /// 店铺在有赞的id标识
    /// </summary>
    public long kdt_id { get; set; }

    /// <summary>
    /// 无规格价格属性集
    /// </summary>
    public ItemPriceParam item_price_param { get; set; }

    /// <summary>
    /// 商品Id
    /// </summary>
    public string item_id { get; set; }

    /// <summary>
    /// 更多条码
    /// </summary>
    public List<string> item_barcodes { get; set; }

    /// <summary>
    /// 商品总销量
    /// </summary>
    public long sold_num { get; set; }

    /// <summary>
    /// 商品重量,商品无规格的时候使用，单位：克
    /// </summary>
    public long item_weight { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    public int item_type { get; set; }

    /// <summary>
    /// 是否参加会员折扣
    /// </summary>
    public bool join_level_discount { get; set; }

    /// <summary>
    /// 是否有一品多码
    /// </summary>
    public bool has_multi_barcode { get; set; }

    /// <summary>
    /// 商品规格汇总信息
    /// </summary>
    public Spu spu { get; set; }

    /// <summary>
    /// 商品别名
    /// </summary>
    public string alias { get; set; }

    /// <summary>
    /// 商品总库存
    /// </summary>
    public long quantity { get; set; }

    /// <summary>
    /// 商品类目Id
    /// </summary>
    public long cid { get; set; }

    /// <summary>
    /// 库存单位,商品资料-单位库-单位名称
    /// </summary>
    public string unit { get; set; }


    /// <summary>
    /// 是否虚拟商品
    /// </summary>
    public int is_virtual { get; set; }

    /// <summary>
    /// 商品参与的平台。0-普通、10-分销商品
    /// </summary>
    public int goods_platform { get; set; }

    /// <summary>
    /// 有赞连锁总部店铺id
    /// </summary>
    public long root_kdt_id { get; set; }


    /// <summary>
    /// 库存扣减模式0：下单扣库存->拍减1：付款扣库存->拍占2：非预占付款扣库存->付减
    /// </summary>
    public int stock_deduct_mode { get; set; }

    /// <summary>
    /// 商品分组（仅一级）列表即商品标签Id列表
    /// </summary>
    public List<long> tag_ids { get; set; }

    /// <summary>
    /// 是否有门店渠道
    /// </summary>
    public bool has_offline_channel { get; set; }

    /// <summary>
    /// 商品起售数量
    /// </summary>
    public int start_sale_num { get; set; }


    /// <summary>
    /// 规格型号
    /// </summary>
    public string specifications { get; set; }

    /// <summary>
    /// 商品关联总部商品id
    /// </summary>
    public long root_item_id { get; set; }

    /// <summary>
    /// 备货时间
    /// </summary>
    public long prepare_time { get; set; }

    /// <summary>
    /// 当前商品所属渠道 0 网店 1 门店 -1 多渠道店铺商品
    /// </summary>
    public int channel { get; set; }

    /// <summary>
    /// 存货类别：0产成品；1半成品；2原材料
    /// </summary>
    public int inventory_type { get; set; }

    /// <summary>
    /// 商品分组（含二级）列表即商品标签Id列表
    /// </summary>
    public List<long> actual_tag_ids { get; set; }

    /// <summary>
    /// 创建时间，Unix时间戳，单位：毫秒
    /// </summary>
    public long created_time { get; set; }

    /// <summary>
    /// 商品标题
    /// </summary>
    public string title { get; set; }

    /// <summary>
    /// 是否上架商品。1—上架商品，0—不上架商品新增若不传该字段默认传1
    /// </summary>
    public bool is_display { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string item_barcode { get; set; }

    /// <summary>
    /// 商品编码（商家为商品设置的外部编号，可与商家外部系统对接）
    /// </summary>
    public string item_no { get; set; }
}




public class ItemPriceParam
{
    /// <summary>
    /// 无规格商品的成本价
    /// </summary>
    public long cost_price { get; set; }

    /// <summary>
    /// 最小建议售价（分）
    /// </summary>
    public long min_guide_price { get; set; }

    /// <summary>
    /// 最大建议售价（分）
    /// </summary>
    public long max_guide_price { get; set; }

    /// <summary>
    /// 显示在原价那里的信息
    /// </summary>
    public string origin { get; set; }

    /// <summary>
    /// 商品价格，单位：分
    /// </summary>
    public long price { get; set; }
}

public class CategoryProperties
{
    /// <summary>
    /// 公有属性
    /// </summary>
    public List<PublicProperty> publics { get; set; }

    /// <summary>
    /// 私有属性
    /// </summary>
    public List<PrivateProperty> privates { get; set; }

    /// <summary>
    /// 新类目ID叶子类目
    /// </summary>
    public long leaf_category_id { get; set; }

    /// <summary>
    /// 类目状态 1：正常 2：禁用
    /// </summary>
    public int status { get; set; }

    /// <summary>
    /// 类目全路径 按照 层级顺序 1、2、3 或者 叶子类目名称
    /// </summary>
    public List<string> category_names { get; set; }
}

public class PublicProperty
{
    /// <summary>
    /// 主键
    /// </summary>
    public long id { get; set; }

    /// <summary>
    /// 属性项名称,如：颜色
    /// </summary>
    public string pro_name { get; set; }

    /// <summary>
    /// 属性值有多选情况，如：红色
    /// </summary>
    public List<string> val_names { get; set; }
}

public class PrivateProperty
{
    /// <summary>
    /// 主键
    /// </summary>
    public long id { get; set; }

    /// <summary>
    /// 属性项名称,如：颜色
    /// </summary>
    public string pro_name { get; set; }

    /// <summary>
    /// 属性值有多选情况，如：红色
    /// </summary>
    public List<string> val_names { get; set; }
}

public class Sku
{
    /// <summary>
    /// 有规格时最低建议售价，单位：分
    /// </summary>
    public long min_guide_price { get; set; }

    /// <summary>
    /// 配送价（配销价）单位：分
    /// </summary>
    public long delivery_price { get; set; }

    /// <summary>
    /// sku编码
    /// </summary>
    public string sku_no { get; set; }

    /// <summary>
    /// 规格属性
    /// </summary>
    public List<SkuProp> sku_props { get; set; }

    /// <summary>
    /// 规格的重量，单位g
    /// </summary>
    public long weight { get; set; }

    /// <summary>
    /// 有规格时最高建议售价，单位：分
    /// </summary>
    public long max_guide_price { get; set; }

    /// <summary>
    /// Sku最后修改日期，Unix时间戳，单位：毫秒
    /// </summary>
    public long sku_modified_time { get; set; }

    /// <summary>
    /// Sku的商品的数量
    /// </summary>
    public long quantity { get; set; }

    /// <summary>
    /// Sku创建日期，Unix时间戳，单位：毫秒
    /// </summary>
    public long sku_created_time { get; set; }

    /// <summary>
    /// 总部商品规格Id
    /// </summary>
    public long root_sku_id { get; set; }

    /// <summary>
    /// Sku的成本价，单位：分
    /// </summary>
    public long cost_price { get; set; }

    /// <summary>
    /// SKU销量
    /// </summary>
    public long sold_num { get; set; }

    /// <summary>
    /// 规格条码
    /// </summary>
    public string sku_barcode { get; set; }

    /// <summary>
    /// 商品的这个Sku的价格；单位：分
    /// </summary>
    public long price { get; set; }

    /// <summary>
    /// 标准价单位：分
    /// </summary>
    public long standard_price { get; set; }

    /// <summary>
    /// 备货时间
    /// </summary>
    public long prepare_time { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public long stock_num { get; set; }

    /// <summary>
    /// 更多条码
    /// </summary>
    public List<string> sku_barcodes { get; set; }

    /// <summary>
    /// 是否可售0.不可售1.可售仅在分销商品时生效非分销商品为null
    /// </summary>
    public int is_fenxiao_sell { get; set; }

    /// <summary>
    /// 商品规格Id
    /// </summary>
    public string sku_id { get; set; }
}

public class SkuProp
{
    /// <summary>
    /// 规格值名称id
    /// </summary>
    public long prop_value_id { get; set; }

    /// <summary>
    /// 规格项名称
    /// </summary>
    public string prop_name { get; set; }

    /// <summary>
    /// 规格项名称id
    /// </summary>
    public long prop_name_id { get; set; }

    /// <summary>
    /// 规格值名称
    /// </summary>
    public string prop_value_name { get; set; }
}

public class ItemProps
{
    /// <summary>
    /// 属性ID
    /// </summary>
    public long prop_id { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    public string prop_name { get; set; }

    /// <summary>
    /// 属性项关联属性值列表
    /// </summary>
    public List<TextModel> text_models { get; set; }
}

public class TextModel
{
    /// <summary>
    /// 属性值价格，单位：分
    /// </summary>
    public int price { get; set; }

    /// <summary>
    /// 属性值名称
    /// </summary>
    public string prop_text_name { get; set; }

    /// <summary>
    /// 属性值id
    /// </summary>
    public string prop_text_id { get; set; }
}


public class Spu
{
    /// <summary>
    /// 无规格商品规格id
    /// </summary>
    public long spu_id { get; set; }
}