﻿using BPM.DependencyInjection;
using Spire.Presentation;
using static BPM.Domain.Responses.product.onSaleProcuctResponse;

namespace BPM.Domain.Dto.trade;

/// <summary>
/// 交易优惠信息结构体.
/// </summary>
[SuppressSniffer]
public class OrderPromotion
{
    /// <summary>
    /// 订单改价金额.
    /// </summary>
    public string adjust_fee { get; set; }

    /// <summary>
    /// 商品级优惠明细结构体.
    /// </summary>
    public List<Item> item { get; set; }

    /// <summary>
    /// 商品优惠总金额.
    /// </summary>
    public string item_discount_fee { get; set; }

    /// <summary>
    /// 订单级优惠明细结构体.
    /// </summary>
    public List<Order> order { get; set; }

    /// <summary>
    /// 订单优惠总金额.
    /// </summary>
    public string order_discount_fee { get; set; }
}

/// <summary>
/// 商品级优惠明细结构体.
/// </summary>
public class Item
{
    /// <summary>
    /// 是否赠品.
    /// </summary>
    public bool is_present { get; set; }

    /// <summary>
    /// 商品id.
    /// </summary>
    public int item_id { get; set; }

    /// <summary>
    /// 交易明细id.
    /// </summary>
    public string oid { get; set; }

    /// <summary>
    /// 优惠明细结构体.
    /// </summary>
    public Promotions promotions { get; set; }

    /// <summary>
    /// 商品规格id.
    /// </summary>
    public long sku_id { get; set; }
}

/// <summary>
/// 优惠明细结构体.
/// </summary>
public class Promotions
{
    /// <summary>
    /// 优惠金额.
    /// </summary>
    public string decrease { get; set; }

    /// <summary>
    /// 活动标题.
    /// </summary>
    public string promotion_title { get; set; }

    /// <summary>
    /// 活动类型.
    /// </summary>
    public string promotion_type { get; set; }

    /// <summary>
    /// 活动类型id.
    /// </summary>
    public int promotion_type_id { get; set; }

    /// <summary>
    /// 优惠类型描述.
    /// </summary>
    public string promotion_type_name { get; set; }
}

/// <summary>
/// 订单级优惠明细结构体.
/// </summary>
public class Order
{
    /// <summary>
    /// 优惠券/码编号.
    /// </summary>
    public string coupon_id { get; set; }

    /// <summary>
    /// 优惠金额.
    /// </summary>
    public string discount_fee { get; set; }

    /// <summary>
    /// 优惠描述.
    /// </summary>
    public string promotion_condition { get; set; }

    /// <summary>
    /// 优惠活动别名.
    /// </summary>
    public string promotion_content { get; set; }

    /// <summary>
    /// 优惠id.
    /// </summary>
    public int promotion_id { get; set; }

    /// <summary>
    /// 活动类型.
    /// </summary>
    public string promotion_type { get; set; }

    /// <summary>
    /// 活动类型id.
    /// </summary>
    public int promotion_type_id { get; set; }

    /// <summary>
    /// 优惠类型描述.
    /// </summary>
    public string promotion_type_name { get; set; }

    /// <summary>
    /// 优惠子类型.
    /// </summary>
    public string sub_promotion_type { get; set; }
}
