﻿@{var n=0;}
@foreach (var table in Model.TableRelations)
{
if(table.IsShowField)
{
@:using BPM.@(Model.NameSpace).<EMAIL>;
@{n++;}
}
}
@if(n > 1)
{
@:
}
namespace BPM.@(Model.NameSpace).<EMAIL>;

/// <summary>
/// @(Model.BusName)输入参数.
/// </summary>
public class @(Model.ClassName)ListOutput
{
@foreach (var column in Model.TableField)
{
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@if (column.PrimaryKey){
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}else if (column.IsShow){
@switch(column.bpmKey)
{
case "datePicker":
case "createTime":
case "modifyTime":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @parameterName { get; set; }
@:
break;
case "uploadFile":
case "uploadImg":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @(parameterName) { get; set; }
@:
break;
case "switch":
case "radio":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @parameterName { get; set; }
@:
break;
case "relationForm":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @parameterName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(parameterName)_id { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "string" : column.NetType) @parameterName { get; set; }
@:
break;
}
}
}
@foreach (var column in Model.TableField)
{
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@switch(column.bpmKey)
{
case "treeSelect":
@if(column.IsTreeParentField && Model.TableType == 5)
{
@if(!column.IsShow)
{
    @:/// <summary>
    @:/// @(Model.BusName).
    @:/// </summary>
    @:public @column.NetType @parameterName { get; set; }
@:
}
    @:/// <summary>
    @:/// @(Model.BusName)-父级ID.
    @:/// </summary>
    @:public @column.NetType @(parameterName)_pid { get; set; }
@:
    @:/// <summary>
    @:/// @(Model.BusName)-子级.
    @:/// </summary>
    @:public List<@(Model.ClassName)ListOutput> children { get; set; }
@:
}
break;
}
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
    @:/// <summary>
    @:/// 流程任务ID.
    @:/// </summary>
    @:public string flowTaskId { get; set; }
@:
}
@foreach (var table in Model.TableRelations)
{
if(table.IsShowField)
{
    @:/// <summary>
    @:/// @(table.TableComment).
    @:/// </summary>
    @:public List<@(table.ClassName)ListOutput> @table.ControlModel { get; set; }
@:
}
}
@if(Model.EnableFlow)
{
    @:/// <summary>
    @:/// 流程状态.
    @:/// </summary>
    @:public int? flowState { get; set; } = 0;
@:
    @:/// <summary>
    @:/// 流程引擎ID.
    @:/// </summary>
    @:public string flowId { get; set; }
}
}