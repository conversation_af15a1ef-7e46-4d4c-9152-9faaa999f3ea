﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.subscriber;

/// <summary>
///  推送请求.
/// </summary>
[SuppressSniffer]
public class authMobileRequest
{
    /// <summary>
    /// 是否同步授权到总部.
    /// </summary>
    public bool is_auth_root { get; set; }

    /// <summary>
    /// 是否同步授权到总部.
    /// </summary>
    public string node_kdt_id { get; set; }

    /// <summary>
    /// 会员的额外信息.
    /// </summary>
    public MemberExtraInfo member_extra_info { get; set; }

    /// <summary>
    /// 手机号.
    /// </summary>
    public string mobile { get; set; }

    /// <summary>
    /// 校验字段.
    /// </summary>
    public string sign { get; set; }

    /// <summary>
    /// 总部店铺id.
    /// </summary>
    public string root_kdt_id { get; set; }

    /// <summary>
    /// 有赞用户id.
    /// </summary>
    public string yz_open_id { get; set; }
}
