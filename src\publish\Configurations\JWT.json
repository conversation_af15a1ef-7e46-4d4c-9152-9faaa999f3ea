{
  "JWTSettings": {
    "ValidateIssuerSigningKey": true, // 是否验证密钥，bool 类型，默认true
    "IssuerSigningKey": "RkayGi4ltkMWrSQKsQTWic1VnakqsQfaJOmJIBUWE1gxGaS0IrJHxa9anjVAwuew", // 密钥，string 类型，必须是复杂密钥，长度大于16，.NET8+ 长度需大于 32
    "ValidateIssuer": true, // 是否验证签发方，bool 类型，默认true
    "ValidIssuer": "bpm", // 签发方，string 类型
    "ValidateAudience": true, // 是否验证签收方，bool 类型，默认true
    "ValidAudience": "bpm", // 签收方，string 类型
    "ValidateLifetime": true, // 是否验证过期时间，bool 类型，默认true，建议true
    "ExpiredTime": 1440, // 过期时间，long 类型，单位分钟，默认20分钟
    "ClockSkew": 5 // 过期时间容错值，long 类型，单位秒，默认5秒
  }
}