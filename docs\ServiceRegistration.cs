using BPM.Application.Configuration;
using BPM.Application.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BPM.Application.Extensions;

/// <summary>
/// ProductService 依赖注入扩展
/// </summary>
public static class ProductServiceExtensions
{
    /// <summary>
    /// 注册 ProductService 相关服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddProductServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 1. 注册配置
        services.AddSingleton<ProductSyncConfiguration>(provider =>
        {
            return new ProductSyncConfiguration(configuration);
        });

        // 2. 注册核心服务
        services.AddScoped<ProductSyncResultHandler>();
        services.AddScoped<ProductBatchProcessor>();
        services.AddScoped<ProductCompensationService>();

        // 3. 注册策略工厂实现（延迟初始化避免循环依赖）
        services.AddScoped<ProductSyncStrategyFactory, ProductSyncStrategyFactoryImpl>();

        return services;
    }
}

/// <summary>
/// 在 Startup.cs 或 Program.cs 中的使用示例
/// </summary>
public class StartupExample
{
    public void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 其他服务注册...
        
        // 注册 ProductService 相关服务
        services.AddProductServices(configuration);
        
        // 注册 ProductService 本身（如果还没有注册的话）
        services.AddScoped<ProductService>();
        
        // 其他服务注册...
    }
}

/// <summary>
/// 在 Program.cs (NET 6+) 中的使用示例
/// </summary>
public class ProgramExample
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        
        // 其他服务注册...
        
        // 注册 ProductService 相关服务
        builder.Services.AddProductServices(builder.Configuration);
        
        // 注册 ProductService 本身
        builder.Services.AddScoped<ProductService>();
        
        var app = builder.Build();
        
        // 配置管道...
        
        app.Run();
    }
}

/// <summary>
/// 配置文件示例 (appsettings.json)
/// </summary>
/*
{
  "ProductSync": {
    "MaxPageSize": 200,
    "BatchSize": 50,
    "MaxParallelism": 5,
    "DelayBetweenBatches": 100,
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 5,
    "CacheExpirationMinutes": 5,
    "EnableStoreProductResync": true,
    "EnableProductCompletion": true,
    "EnableBatchProcessing": true,
    "EnableParallelProcessing": true,
    "EnableDetailedLogging": false
  }
}
*/

/// <summary>
/// 环境特定配置示例
/// </summary>
/*
// appsettings.Development.json
{
  "ProductSync": {
    "EnableDetailedLogging": true,
    "MaxParallelism": 2,
    "BatchSize": 20
  }
}

// appsettings.Production.json
{
  "ProductSync": {
    "EnableDetailedLogging": false,
    "MaxParallelism": 10,
    "BatchSize": 100,
    "CacheExpirationMinutes": 10
  }
}
*/
