﻿using BPM.Common.Const;
using BPM.Common.Contracts;
using SqlSugar;

namespace BPM.Domain.Entity.shop;

/// <summary>
/// 门店微信配置信息
/// 版 本：V3.2
/// 版 权：中畅源科技开发有限公司（https://www.szclouds.com）
/// 作 者：Aarons
/// 日 期：2023-01-05.
/// </summary>
[SugarTable("SHOP_WECHAT_CONFIG")]
[Tenant(ClaimConst.TENANTID)]
public class shopWechatConfigEntity
{
    /// <summary>
    /// 门店编号
    /// </summary>
    [SugarColumn(ColumnName = "F_ShopId")]
    public string shop_id { get; set; }

    /// <summary>
    /// 应用编号
    /// </summary>
    [SugarColumn(ColumnName = "F_AppId")]
    public string app_id { get; set; }

    /// <summary>
    /// 商户号
    /// </summary>
    [SugarColumn(ColumnName = "F_MchId")]
    public string mch_id{ get; set; }

    /// <summary>
    /// 子商户应用编号
    /// </summary>
    [SugarColumn(ColumnName = "F_SubAppId")]
    public string sub_app_id { get; set; }

    /// <summary>
    /// 子商户号
    /// </summary>
    [SugarColumn(ColumnName = "F_SubMchId")]
    public string sub_mch_id { get; set; }

    /// <summary>
    /// 支付密钥
    /// </summary>
    [SugarColumn(ColumnName = "F_PayKey")]
    public string pay_key { get; set; }

    /// <summary>
    /// 证书路径
    /// </summary>
    [SugarColumn(ColumnName = "F_CertPath")]
    public string cert_path { get; set; }
}

