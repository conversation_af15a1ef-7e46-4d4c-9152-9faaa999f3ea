﻿using BPM.Common.Filter;
using BPM.DependencyInjection;

namespace BPM.Business.Entitys.Dto.shop;

/// <summary>
/// 门店列表查询.
/// </summary>
[SuppressSniffer]
public class shopListQuery : PageInputBase
{
    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_id { get; set; }

    /// <summary>
    /// 交易单号
    /// </summary>
    public string transaction_id { get; set; }

    /// <summary>
    /// 商户订单号
    /// </summary>
    public string out_trade_no { get; set; }

    /// <summary>
    /// 开始时间.
    /// </summary>
    public long? startTime { get; set; }

    /// <summary>
    /// 结束时间.
    /// </summary>
    public long? endTime { get; set; }
}