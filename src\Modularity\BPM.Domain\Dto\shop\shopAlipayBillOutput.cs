﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.shop;

/// <summary>
/// 支付宝对账单输出
/// </summary>
[SuppressSniffer]
public class shopAlipayBillOutput
{
    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_name { get; set; }

    /// <summary>
    /// 支付宝交易单号
    /// </summary>
    public string transaction_id { get; set; }

    /// <summary>
    /// 商户订单号
    /// </summary>
    public string out_trade_no { get; set; }

    /// <summary>
    /// 终端号
    /// </summary>
    public string terminal_id { get; set; }

    /// <summary>
    /// 业务员编号
    /// </summary>
    public string operator_id { get; set; }

    /// <summary>
    /// 交易金额
    /// </summary>
    public decimal trade_fee { get; set; }

    /// <summary>
    /// 手续费
    /// </summary>
    public decimal charge { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    public string trade_type { get; set; }

    /// <summary>
    /// 交易时间
    /// </summary>
    public DateTime? trade_time { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? finish_time { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string body { get; set; }

    /// <summary>
    /// 对账日期
    /// </summary>

    public string bill_date { get; set; }

    /// <summary>
    /// 商户退款交易号
    /// </summary>
    public string out_refund_no { get; set; }

}


/// <summary>
/// 支付宝对账单汇总输出
/// </summary>
[SuppressSniffer]
public class shopAlipayBillDaySummaryOutput
{
    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_name { get; set; }

    /// <summary>
    /// 交易金额
    /// </summary>
    public decimal trade_fee { get; set; }

    /// <summary>
    /// 对账日期
    /// </summary>
    public string trade_time { get; set; }

}
