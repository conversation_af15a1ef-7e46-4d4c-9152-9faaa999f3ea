﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;

/// <summary>
/// 客户实体
/// </summary>
[SugarTable("CUSTOMER_LOGS")]
public class customerLogsEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string appid { get; set; }

    /// <summary>
    ///  生日
    /// </summary>
    public string birthday { get; set; }

    /// <summary>
    /// 客户编号
    /// </summary>
    public string customerId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string customerPhone { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int gender { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string nickname { get; set; }

    /// <summary>
    /// opendId
    /// </summary>
    public string openId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string realName { get; set; }

    /// <summary>
    /// unionId
    /// </summary>
    public string unionId { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime updateTime { get; set; }

    /// <summary>
    /// 同步标记
    /// </summary>
    public string tag_status { get; set; }

}

