﻿namespace BPM.Domain.Entity;

/// <summary>
/// 实体基类
/// </summary>
public class baseEntity
{
    /// <summary> 
    /// 创建用户 
    /// </summary> 
    /// <returns></returns> 
    public string created_user_id { get; set; }
    /// <summary> 
    /// 创建日期 
    /// </summary> 
    /// <returns></returns> 
    public DateTime? created_date { get; set; }
    /// <summary> 
    /// 修改人 
    /// </summary> 
    /// <returns></returns> 
    public string modify_user_id { get; set; }
    /// <summary> 
    /// 修改日期 
    /// </summary> 
    /// <returns></returns> 
    public DateTime? modify_date { get; set; }
}

