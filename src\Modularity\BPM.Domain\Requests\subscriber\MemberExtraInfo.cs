﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.subscriber;

/// <summary>
/// 会员的额外信息.
/// </summary>
[SuppressSniffer]
public class MemberExtraInfo
{
    /// <summary>
    /// 成为会员时 对应的邀请导购yzOpenId.
    /// </summary>
    public string invited_yz_open_id { get; set; }

    /// <summary>
    /// 成为会员时 对应的邀请导购标识.
    /// </summary>
    public string invited_sl { get; set; }

    /// <summary>
    /// 成为会员的渠道.
    /// </summary>
    public int member_src_channel { get; set; }

    /// <summary>
    /// 成为会员的方式.
    /// </summary>
    public int member_src_way { get; set; }

    /// <summary>
    /// 成为会员的三方门店id.
    /// </summary>
    public long sub_kdt_id { get; set; }
}
