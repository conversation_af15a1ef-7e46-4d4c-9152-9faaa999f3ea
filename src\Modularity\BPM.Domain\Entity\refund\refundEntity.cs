﻿using SqlSugar;

namespace BPM.Domain.Entity.refund;

/// <summary>
/// 退款单主表
/// </summary>
[SugarTable("REFUND")]
[Tenant("IPOS-ORDER")]
public class refundEntity
{
    /// <summary>
    /// 订单编号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string order_no { get; set; }

    /// <summary>
    /// 订单编号
    /// </summary>
    public string tid { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string kdt_id { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public string kdt_name { get; set; }

    /// <summary>
    /// 客户编号
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 会员手机号
    /// </summary>
    public string buyer_phone { get; set; }

    /// <summary>
    /// 退款总金额
    /// </summary>
    public decimal refunded_fee { get; set; }

    /// <summary>
    /// 退款邮费
    /// </summary>
    public decimal refund_postage { get; set; }

    /// <summary>
    /// 退款类型
    /// </summary>
    public string refund_type { get; set; }

    /// <summary>
    /// 退款理由
    /// </summary>
    public string refund_reason { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string status { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime created { get; set; }

    /// <summary>
    /// 修改日期
    /// </summary>
    public DateTime modified { get; set; }

    /// <summary>
    /// 退款时间
    /// </summary>
    public DateTime refund_account_time { get; set; }

}
