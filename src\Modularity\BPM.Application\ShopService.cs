﻿using BPM.Common.Cache;
using BPM.Common.Manager;
using BPM.Domain.Entity.customer;
using BPM.Domain.Entity.shop;
using BPM.Domain.Requests.shop;
using BPM.Domain.Requests.user;
using BPM.Domain.Responses.product;
using BPM.Domain.Responses.shop;
namespace BPM.Application;

/// <summary>
/// 门店管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11
/// </summary>
[ApiDescriptionSettings(Tag = "门店管理", Name = "Shop", Order = 600)]
[Route("api/[controller]")]
public class ShopService : IDynamicApiController, ITransient
{

    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly SqlSugarProvider _repository;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;


    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    /// <summary>
    /// 缓存管理.
    /// </summary>
    private readonly ICacheManager _cache;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context"></param>
    public ShopService(ISqlSugarClient context, IYouzanService youzanService, ICacheManager cache)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<shopEntity>();
        _youzanService = youzanService;
        _db = _repository.AsTenant();
        _cache = cache;
    }

    /// <summary>
    /// 获取门店列表
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpGet("search")]
    public async Task<dynamic> getShopList()
    {
        var shop_list = await _repository.Queryable<shopEntity>().ToListAsync();

        // 使用可变变量存储页码和页大小
        int page_no = 1;
        int page_size = 50;
        
        var token = await _youzanService.GetTokenAsync();
        var add_shop_list = new List<shopEntity>();
        var update_shop_list = new List<shopEntity>();
        
        while (true)
        {
            var request = new 
            {
                shop_query_dto = new 
                {
                    page_no = page_no,
                    page_size = page_size
                }
            };
            
            var param = new YouzanParameter();
            param.url = "youzan.scrm.shop.search/1.0.0";
            param.method = "POST";
            param.body = request.ToJsonString();
            
            var res = await _youzanService.GetData(param);
            
            // 检查接口返回是否成功
            if (!res.success)
            {
                // 返回错误信息
                return new { success = false, message = res.message };
            }
            
            // 检查返回的数据是否为空
            if (res.data == null)
            {
                return new { success = false, message = "返回数据为空" };
            }
            
            shopResponse result;
            try 
            {
                result = res.data.ToObject<shopResponse>();
                if (result == null || result.items == null)
                {
                    return new { success = false, message = "数据格式不正确" };
                }
            }
            catch (Exception ex)
            {
                return new { success = false, message = $"数据转换失败: {ex.Message}" };
            }
            
            // 从返回的JSON数据中获取总数
            var jsonData = res.data.ToString();
            var totalCount = 0;
            
            // 尝试从JSON数据中解析总数
            try
            {
                var jsonObj = Newtonsoft.Json.Linq.JObject.Parse(jsonData);
                totalCount = jsonObj["paginator"]?["total_count"]?.ToObject<int>() ?? 0;
            }
            catch
            {
                // 如果解析失败，假设没有更多数据
                totalCount = 0;
            }
            
            if (result.items.Count > 0)
            {
                foreach (var item in result.items)
                {
                    var shop_info = shop_list.Where(x => x.id == item.shop_code).FirstOrDefault();
                    
                    if (shop_info == null)
                    {
                        var shop = new shopEntity()
                        {
                            id = item.shop_code,
                            store_no = item.shop_code,
                            status = 1,
                            created_date = DateTime.Now,
                            address = item.address,
                            source_no = item.kdt_id,
                            // 如果business_relation_kdt_id为0（包括API未返回此字段的情况），则使用kdt_id
                            net_source_no = item.business_relation_kdt_id != 0 ? item.business_relation_kdt_id : item.kdt_id,
                            store_name = item.shop_name,
                            created_user_id = "admin"
                        };
                        add_shop_list.Add(shop);
                    }
                    else
                    {
                        shop_info.store_no = item.shop_code;
                        shop_info.address = item.address;
                        shop_info.source_no = item.kdt_id;
                        // 如果business_relation_kdt_id为0（包括API未返回此字段的情况），则使用kdt_id
                        shop_info.net_source_no = item.business_relation_kdt_id != 0 ? item.business_relation_kdt_id : item.kdt_id;
                        shop_info.store_name = item.shop_name;
                        shop_info.modify_date = DateTime.Now;
                        shop_info.modify_user_id = "admin";
                        update_shop_list.Add(shop_info);
                    }
                }
            }
            
            // 如果没有更多数据或者已经获取了所有数据，则跳出循环
            if (result.items.Count == 0 || page_no * page_size >= totalCount)
                break;
                
            // 页码加一
            page_no++;
        }
        
        try
        {
            // 开启事务
            await _db.BeginTranAsync();
            if (add_shop_list.Count > 0)
                await _repository.Insertable(add_shop_list).ExecuteCommandAsync();
            if (update_shop_list.Count > 0)
                await _repository.Updateable(update_shop_list).ExecuteCommandAsync();

            await _cache.DelByPatternAsync("shop*");

            await _db.CommitTranAsync();
            return new { success = true };
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            return new { success = false, message = ex.Message };
        }
    }

}
