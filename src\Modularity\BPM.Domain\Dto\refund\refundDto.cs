﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.refund;

/// <summary>
/// 交易退款信息结构体.
/// </summary>
[SuppressSniffer]
public class refundDto
{
    /// <summary>
    /// 退款交易明细信息.
    /// </summary>
    public string oids { get; set; }

    /// <summary>
    /// 退款金额.
    /// </summary>
    public string refunded_fee { get; set; }

    /// <summary>
    /// 退款id.
    /// </summary>
    public string refund_id { get; set; }

    /// <summary>
    /// 退款原因.
    /// </summary>
    public string refund_reason { get; set; }

    /// <summary>
    /// 退款类型.
    /// </summary>
    public string refund_type { get; set; }

    /// <summary>
    /// 订单号.
    /// </summary>
    public string tid { get; set; }

    /// <summary>
    /// 更新时间.
    /// </summary>
    public string update_time { get; set; }
}
