﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31912.275
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-应用模块", "01-应用模块", "{1230723A-B35C-45C2-9160-7D1DCD1310B0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-应用服务", "02-应用服务", "{318DD697-D142-45CA-A1A4-ACC88131501B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{1B3B434C-4A39-40B1-892C-303706847853}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		Directory.Build.props = Directory.Build.props
		dotnet.ruleset = dotnet.ruleset
		global.json = global.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BPM.API.Entry", "application\BPM.API.Entry\BPM.API.Entry.csproj", "{53604AFB-0CCA-41EB-9A71-50D650106BFD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BPM.Domain", "modularity\BPM.Domain\BPM.Domain.csproj", "{8AB3A852-0E96-4D8B-BB4A-051ECD277EBF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BPM.Application", "modularity\BPM.Application\BPM.Application.csproj", "{05155CCA-260B-4AB8-A1A5-A2DDA72721B5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BPM.Extras.Youzan", "BPM.Extras.Youzan\BPM.Extras.Youzan.csproj", "{EE178492-A4BC-4E1B-9F35-BB3D620D416E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{53604AFB-0CCA-41EB-9A71-50D650106BFD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53604AFB-0CCA-41EB-9A71-50D650106BFD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53604AFB-0CCA-41EB-9A71-50D650106BFD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53604AFB-0CCA-41EB-9A71-50D650106BFD}.Release|Any CPU.Build.0 = Release|Any CPU
		{8AB3A852-0E96-4D8B-BB4A-051ECD277EBF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8AB3A852-0E96-4D8B-BB4A-051ECD277EBF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8AB3A852-0E96-4D8B-BB4A-051ECD277EBF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8AB3A852-0E96-4D8B-BB4A-051ECD277EBF}.Release|Any CPU.Build.0 = Release|Any CPU
		{05155CCA-260B-4AB8-A1A5-A2DDA72721B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05155CCA-260B-4AB8-A1A5-A2DDA72721B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05155CCA-260B-4AB8-A1A5-A2DDA72721B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05155CCA-260B-4AB8-A1A5-A2DDA72721B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE178492-A4BC-4E1B-9F35-BB3D620D416E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE178492-A4BC-4E1B-9F35-BB3D620D416E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE178492-A4BC-4E1B-9F35-BB3D620D416E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE178492-A4BC-4E1B-9F35-BB3D620D416E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{53604AFB-0CCA-41EB-9A71-50D650106BFD} = {318DD697-D142-45CA-A1A4-ACC88131501B}
		{8AB3A852-0E96-4D8B-BB4A-051ECD277EBF} = {1230723A-B35C-45C2-9160-7D1DCD1310B0}
		{05155CCA-260B-4AB8-A1A5-A2DDA72721B5} = {1230723A-B35C-45C2-9160-7D1DCD1310B0}
		{EE178492-A4BC-4E1B-9F35-BB3D620D416E} = {1230723A-B35C-45C2-9160-7D1DCD1310B0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {646DDD1C-F143-42C2-894F-F5C7B3A0CE74}
	EndGlobalSection
EndGlobal
