﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.trade;

/// <summary>
/// 交易退款信息结构体.
/// </summary>
[SuppressSniffer]
public class RefundOrder
{
    /// <summary>
    /// 退款交易明细信息.
    /// </summary>
    public string oids { get; set; }

    /// <summary>
    /// 退款金额.
    /// </summary>
    public string refund_fee { get; set; }

    /// <summary>
    /// 退款id.
    /// </summary>
    public string refund_id { get; set; }

    /// <summary>
    /// 退款状态.
    /// </summary>
    public int refund_state { get; set; }

    /// <summary>
    /// 退款类型.
    /// </summary>
    public int refund_type { get; set; }
}
