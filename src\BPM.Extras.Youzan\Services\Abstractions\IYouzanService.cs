﻿using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Result;
using Microsoft.AspNetCore.Mvc;

namespace BPM.Extras.Youzan.Services.Abstractions;

/// <summary>
/// 有赞服务
/// </summary>
public interface IYouzanService
{
    /// <summary>
    /// 有赞token.
    /// </summary>
    /// <param name="grant_id">授权店铺ID</param>
    /// <param name="forceRefresh">是否强制刷新token</param>
    /// <returns>访问令牌</returns>
    [NonAction]
    Task<string> GetTokenAsync(string grant_id = "", bool forceRefresh = false);

    /// <summary>
    /// 获取数据.
    /// </summary>
    /// <returns></returns>
    [NonAction]
    Task<YouzanResult<object>> GetData(YouzanParameter param);
}