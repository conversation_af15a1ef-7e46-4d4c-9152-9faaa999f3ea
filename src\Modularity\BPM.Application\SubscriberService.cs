﻿using BPM.Common.Manager;
using BPM.Domain.Dto.customerCard;
using BPM.Domain.Dto.points;
using BPM.Domain.Dto.refund;
using BPM.Domain.Entity.customer;
using BPM.Domain.Entity.order;
using BPM.Domain.Entity.refund;
using BPM.Domain.Requests.subscriber;
using BPM.EventBus;
using BPM.EventHandler;
using BPM.Systems.Entitys.System;
using Mapster;
using Microsoft.Data.SqlClient;
using System.Diagnostics;
using System.Text.Json;

namespace BPM.Application;

/// <summary>
/// 订阅管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11.
/// </summary>
[ApiDescriptionSettings(Tag = "订阅管理", Name = "subscriber", Order = 600)]
[Route("api/[controller]")]
public class SubscriberService : IDynamicApiController, ITransient
{
    /// <summary>
    ///  服务提供.
    /// </summary>
    private readonly SqlSugarProvider _repository;

    /// <summary>
    ///  服务提供.
    /// </summary>
    private readonly SqlSugarProvider _repository1;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    /// <summary>
    /// 事件总线.
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    /// 缓存管理器
    /// </summary>
    private readonly ICacheManager _cache;

    /// <summary>
    /// 订单服务
    /// </summary>
    private readonly OrderService _orderService;

    private const string CUSTOMER_UPDATE_PREFIX = "customer:update:";
    private const string CACHE_HIT_COUNT_PREFIX = "cache:hit:count:";
    private const int CUSTOMER_UPDATE_EXPIRE_SECONDS = 20; // 20秒过期
    private const int PROCESS_DELAY_MS = 1500; // 1.5秒延迟


    public class CustomerUpdateInfo
    {
        public pushRequest Request { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public bool Processed { get; set; }
    }

    /// <summary>
    /// 构造函数.
    /// </summary>
    /// <param name="context"></param>
    public SubscriberService(ISqlSugarClient context, IYouzanService youzanService, IEventPublisher eventPublisher, ICacheManager cache, OrderService orderService)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<orderEntity>();
        _repository1 = context.AsTenant().GetConnectionWithAttr<customerEntity>();
        _db = _repository.AsTenant();
        _youzanService = youzanService;
        _eventPublisher = eventPublisher;
        _cache = cache;
        _orderService = orderService;
    }

    /// <summary>
    /// 订阅消息推送.
    /// </summary>
    /// <returns></returns>
    [HttpPost("push")]
    [AllowAnonymous]
    [IgnoreLog]
    [NonUnify]
    public async Task<dynamic> Push([FromBody] dynamic body)
    {
        string msg = "";
        try
        {
            pushRequest pushRequest = body.ToObject<pushRequest>();
            Log.Information("推送信息原始记录:" + body);

            //// 临时直接返回成功
            //var timestamptemp = Helpers.GenerateTimeStamp();
            //return new { code = "200", msg = "success", timestamptemp };

            var decodedMsg = HttpUtility.UrlDecode(pushRequest.msg);
            Log.Information("推送信息内容:" + decodedMsg);
            var ipAddress = NetHelper.Ip;
            var ipAddressName = await NetHelper.GetLocation(ipAddress);
            var type = pushRequest.type;
            var bodystring = pushRequest.ToJsonString();

            // 如果是交易支付事件,提前判断是否为线下订单
            if (type == "trade_TradePaid")
            {
                try
                {
                    // 解析订单信息
                    var tradeInfo = JsonSerializer.Deserialize<JsonElement>(decodedMsg);
                    var fullOrderInfo = tradeInfo.GetProperty("full_order_info");
                    var isOfflineOrder = fullOrderInfo.GetProperty("source_info")
                                                    .GetProperty("is_offline_order")
                                                    .GetBoolean();
                    // 如果是线下订单则获取订单号并返回
                    if (isOfflineOrder)
                    {
                        var tid = fullOrderInfo.GetProperty("order_info")
                                             .GetProperty("tid")
                                             .GetString();
                        Log.Information($"线下订单，跳过处理: 订单号={tid}");
                        return new { code = "200", msg = "success", timestamp = Helpers.GenerateTimeStamp() };
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning($"解析订单线下标记失败，可能缺少必要字段: {ex.Message}");
                }
            }
            // 如果是积分事件,判断是否为第三方应用
            else if (type == "POINTS")
            {
                try
                {
                    var pointInfo = JsonSerializer.Deserialize<JsonElement>(decodedMsg);
                    var description = pointInfo.GetProperty("description").GetString();
                    if (description.Contains("第三方应用-yoyi-api") || description.Contains("批量导入积分"))
                    {
                        var mobile = pointInfo.GetProperty("mobile").GetString();
                        var thirdBizValue = pointInfo.GetProperty("third_biz_value").GetString();
                        Log.Information($"第三方应用或批量导入积分，跳过处理: 手机号={mobile}, third_biz_value={thirdBizValue}");
                        return new { code = "200", msg = "success", timestamp = Helpers.GenerateTimeStamp() };
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning($"解析积分事件描述失败，可能缺少必要字段: {ex.Message}");
                }
            }

            Stopwatch sw = new Stopwatch();
            sw.Start();

            switch (type)
            {
                case "SCRM_CUSTOMER_EVENT": // 客户消息事件.
                    await ProcessPendingCustomerUpdates(pushRequest);
                    msg = "";
                    break;
                case "POINTS": // 积分变动消息事件.
                    msg = await PonitsEvent(pushRequest);
                    break;
                case "trade_TradePaid": // 交易支付事件.
                    msg = await TradeEventNew(pushRequest);
                    break;
                case "trade_refund_RefundSuccess": // 退货成功事件.
                case "trade_refund_RefundSellerAgree": // 卖家同意退款
                case "trade_refund_RefundSellerCreated": // 卖家主动退款
                    msg = await RefundEvent(pushRequest);
                    break;
                case "SCRM_CUSTOMER_CARD": // 会员卡事件.
                    // 为会员卡事件添加延迟处理，以确保短时间内多个事件能够顺序处理
                    if (pushRequest.status == "CUSTOMER_CARD_ACTIVATED")
                    {
                        // 如果是激活事件，等待5秒，确保购买事件先被处理
                        Log.Information($"延迟处理会员卡激活事件，等待5000ms: 事件状态={pushRequest.status}, id={pushRequest.id}");
                        await Task.Delay(5000);
                    }
                    // 移除重试机制，直接处理
                    msg = await CustomerCardEvent(pushRequest);
                    break;
                default:
                    msg = "";
                    break;
            }

            sw.Stop();

            var timestamp = Helpers.GenerateTimeStamp();

            if (msg.Length > 0)
            {
                await _eventPublisher.PublishAsync(new LogEventSource("Log:CreateExLog", null, new SysLogEntity
                {
                    Id = SnowflakeIdHelper.NextId(),
                    UserId = "admin",
                    UserName = string.Format("{0}/{1}", "管理员", "admin"),
                    Type = 4,
                    IPAddress = ipAddress,
                    IPAddressName = ipAddressName,
                    RequestURL = "/api/subscriber",
                    RequestMethod = "POST",
                    Json = msg,
                    PlatForm = "Windows 10",
                    Browser = "Edge 129.0.0",
                    RequestParam = bodystring,
                    ModuleName = "subcriber",
                    CreatorTime = DateTime.Now
                }));
                return new { code = "500", msg = msg, timestamp };
            }
            else
            {
                await _eventPublisher.PublishAsync(new LogEventSource("Log:CreateOpLog", null, new SysLogEntity
                {
                    Id = SnowflakeIdHelper.NextId(),
                    UserId = "admin",
                    UserName = string.Format("{0}/{1}", "管理员", "admin"),
                    Type = 3,
                    IPAddress = ipAddress,
                    IPAddressName = ipAddressName,
                    RequestURL = "/api/subscriber",
                    RequestDuration = (int)sw.ElapsedMilliseconds,
                    RequestMethod = "POST",
                    CreatorTime = DateTime.Now,
                    ModuleName = "subcriber",
                    RequestParam = bodystring,
                    RequestTarget = type
                }));
                return new { code = "200", msg = "success", timestamp };
            }
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            var timestamp = Helpers.GenerateTimeStamp();
            Log.Error($"系统错误:{ex.Message}");
            return new {
                code = "500",
                message = ex.Message,
                timestamp
            };
        }
    }

    #region 客户事件
    /// <summary>
    /// 处理待处理的客户更新
    /// </summary>
    public async Task ProcessPendingCustomerUpdates(pushRequest pushRequest)
    {
        try
        {
            string customerKey = $"{CUSTOMER_UPDATE_PREFIX}{pushRequest.yz_open_id}";
            string hitCountKey = $"{CACHE_HIT_COUNT_PREFIX}{pushRequest.yz_open_id}";

            // 从Redis获取已存在的更新信息
            var existingUpdateInfo = await _cache.GetAsync<CustomerUpdateInfo>(customerKey);

            if (existingUpdateInfo == null)
            {
                // 创建新的更新信息
                var updateInfo = new CustomerUpdateInfo
                {
                    Request = pushRequest,
                    LastUpdateTime = DateTime.Now,
                    Processed = false
                };

                // 设置Redis缓存
                var cacheKey = await _cache.SetAsync(
                    customerKey,
                    updateInfo,
                    TimeSpan.FromSeconds(CUSTOMER_UPDATE_EXPIRE_SECONDS)
                );

                // 初始化命中次数
                await _cache.SetAsync(hitCountKey, 0, TimeSpan.FromSeconds(CUSTOMER_UPDATE_EXPIRE_SECONDS));
                Log.Information($"消息合并: 创建新任务 yz_open_id={pushRequest.yz_open_id}, version={pushRequest.version}, status={pushRequest.status}");

            }
            else
            {
                // 记录命中次数
                var hitCount = await _cache.GetAsync<int>(hitCountKey);
                hitCount++;
                await _cache.SetAsync(hitCountKey, hitCount, TimeSpan.FromSeconds(CUSTOMER_UPDATE_EXPIRE_SECONDS));
                Log.Information($"消息合并: Redis缓存命中 yz_open_id={pushRequest.yz_open_id}, 命中次数={hitCount}");

                if (pushRequest.version > existingUpdateInfo.Request.version)
                {
                    Log.Information($"消息合并: 发现新版本消息 yz_open_id={pushRequest.yz_open_id}, 旧版本={existingUpdateInfo.Request.version}, 新版本={pushRequest.version}");
                    existingUpdateInfo.Request = pushRequest;
                    existingUpdateInfo.LastUpdateTime = DateTime.Now;
                    await _cache.SetAsync(customerKey, existingUpdateInfo, TimeSpan.FromSeconds(CUSTOMER_UPDATE_EXPIRE_SECONDS));
                }
                else
                {
                    Log.Information($"消息合并: 忽略旧版本消息 yz_open_id={pushRequest.yz_open_id}, 当前版本={existingUpdateInfo.Request.version}, 收到版本={pushRequest.version}");
                }
            }

            // 等待1.5秒，让其他消息有机会到达
            await Task.Delay(PROCESS_DELAY_MS);
            var updateInfo1 = await _cache.GetAsync<CustomerUpdateInfo>(customerKey);

            if (updateInfo1 != null && !updateInfo1.Processed)
            {
                // 标记为已处理
                updateInfo1.Processed = true;
                await _cache.SetAsync(customerKey, updateInfo1, TimeSpan.FromSeconds(CUSTOMER_UPDATE_EXPIRE_SECONDS));

                // 记录处理开始时间
                var startTime = DateTime.Now;

                Log.Information($"消息合并: 开始处理 yz_open_id={updateInfo1.Request.yz_open_id}, version={updateInfo1.Request.version}, status={updateInfo1.Request.status}");

                // 处理客户更新
                await _eventPublisher.PublishAsync("Customer:PushUpdate", updateInfo1.Request.ToJsonString());

                // 记录处理耗时
                var processTime = (DateTime.Now - startTime).TotalMilliseconds;
                Log.Information($"消息合并: 处理完成 yz_open_id={updateInfo1.Request.yz_open_id}, 耗时={processTime}ms");

                // 处理完成后删除缓存和命中计数
                await _cache.DelAsync(customerKey);
                hitCountKey = $"{CACHE_HIT_COUNT_PREFIX}{updateInfo1.Request.yz_open_id}";
                var finalHitCount = await _cache.GetAsync<int>(hitCountKey);
                Log.Information($"消息合并: 处理完成 yz_open_id={updateInfo1.Request.yz_open_id}, 最终命中次数={finalHitCount}");
                await _cache.DelAsync(hitCountKey);
            }
        }
        catch (SqlException ex)
        {
            Log.Error($"消息合并: 处理失败 key={pushRequest.yz_open_id}, 错误={ex.Message}");
        }
    }
    #endregion

    #region 积分事件

    /// <summary>
    /// 积分事件.
    /// </summary>
    /// <param name="pushRequest">请求参数.</param>
    /// <returns></returns>
    private async Task<string> PonitsEvent(pushRequest pushRequest)
    {
        try
        {
            Log.Information($"========== 开始处理积分变动事件: 店铺ID={pushRequest.kdt_id} ==========");

            // 解析积分事件推送的消息
            var point = HttpUtility.UrlDecode(pushRequest.msg).ToObject<pointsDto>();
            Log.Information($"积分事件详情: 手机号={point.mobile}, 业务标识={point.biz_value}, 积分变动={point.amount}, 总积分={point.total}, 事件类型={point.event_type}");

            // 检查并处理 create_time 格式
            DateTime createTime;
            if (!DateTime.TryParse(point.create_time.ToString(), out createTime))
            {
                Log.Warning($"解析 create_time 失败: {point.create_time}，使用当前时间代替, 手机号={point.mobile}");
                createTime = DateTime.Now;
            }

            Log.Information($"积分变动时间: 手机号={point.mobile}, 时间={createTime.ToString("yyyy-MM-dd HH:mm:ss")}");

            // 确保 unique_id 不为空
            string uniqueId = !string.IsNullOrEmpty(point.unique_id) ? point.unique_id : Guid.NewGuid().ToString("N");
            if (string.IsNullOrEmpty(point.unique_id))
            {
                Log.Warning($"原始 unique_id 为空，已生成新的唯一标识: 手机号={point.mobile}, 唯一标识={uniqueId}");
            }

            // 查询是否已存在相同积分记录
            Log.Information($"检查是否存在相同积分记录: 手机号={point.mobile}, 唯一标识={uniqueId}");
            var pointRecord = await _repository1.Queryable<pointsEntity>().FirstAsync(x => x.unique_id == uniqueId);

            // 如果积分记录不存在，则进行处理
            if (pointRecord == null)
            {
                Log.Information($"未找到相同积分记录，准备创建新记录: 手机号={point.mobile}, 唯一标识={uniqueId}");

                try
                {
                    // 创建积分历史记录
                    var pointsRecord = new pointsEntity
                    {
                        unique_id = uniqueId,
                        mobile = point.mobile ?? string.Empty,
                        amount = point.amount,
                        total = point.total,
                        description = point.description ?? string.Empty,
                        create_time = createTime,
                        event_type = point.event_type,
                        biz_token = point.biz_token ?? string.Empty,
                        biz_value = point.biz_value ?? string.Empty,
                        yz_open_id = point.yz_open_id ?? string.Empty,
                        node_kdt_id = point.node_kdt_id,
                        is_protected = point.is_protected,
                        client_hash = point.client_hash ?? string.Empty,
                        open_user_id = point.open_user_id ?? string.Empty,
                        operate_source_type = point.operate_source_type,
                        third_biz_value = point.third_biz_value ?? string.Empty
                    };

                    Log.Information($"准备插入积分记录: 手机号={point.mobile}, 唯一标识={uniqueId}, 积分变动={point.amount}, 创建时间={createTime.ToString("yyyy-MM-dd HH:mm:ss")}");

                    // 直接插入积分记录
                    var insertResult = await _repository1.Insertable<pointsEntity>(pointsRecord).ExecuteCommandAsync();
                    Log.Information($"积分记录插入结果: 手机号={point.mobile}, 影响行数={insertResult}");
                    Log.Information($"积分事件处理成功: 手机号={point.mobile}, 积分变动={point.amount}, 总积分={point.total}, 唯一标识={uniqueId}");
                }
                catch (Exception ex)
                {
                    Log.Error($"积分事件处理失败: 手机号={point.mobile}, 错误信息={ex.Message}, 堆栈={ex.StackTrace}");
                    return $"积分事件处理失败: {ex.Message}";
                }
            }
            else
            {
                Log.Information($"积分记录已存在: 手机号={point.mobile}, 唯一标识={uniqueId}, 创建时间={pointRecord.create_time}");
            }

            Log.Information($"========== 积分事件处理完成: 手机号={point.mobile}, 唯一标识={uniqueId} ==========");
            return "";
        }
        catch (Exception ex)
        {
            Log.Error($"积分事件处理异常: 错误信息={ex.Message}, 堆栈={ex.StackTrace}");
            return $"积分事件处理异常: {ex.Message}";
        }
    }

    #endregion

    #region 手机号更新事件 

    /// <summary>
    /// 手机号更新事件处理.
    /// </summary>
    /// <param name="pushRequest">请求参数.</param>
    /// <returns></returns>
    private async Task<string> AuthMobile(authMobileRequest pushRequest)
    {
        Log.Information($"========== 开始处理手机号更新事件: 手机号={pushRequest.mobile}, yz_open_id={pushRequest.yz_open_id} ==========");

        var cust = pushRequest;
        Log.Information($"手机号更新事件详情: 手机号={cust.mobile}, 有赞OpenID={cust.yz_open_id}");

        // 查找会员卡记录
        var customer_card = await _repository1.Queryable<customerCardEntity>().FirstAsync(x => x.phone.Equals(cust.mobile));
        if (customer_card != null)
        {
            Log.Information($"找到会员卡记录: 手机号={customer_card.phone}");
        }
        else
        {
            Log.Warning($"未找到会员卡记录: 手机号={cust.mobile}");
            return "会员卡记录不存在";
        }

        // 查找客户记录
        var customer = await _repository1.Queryable<customerEntity>().FirstAsync(x => x.open_id.Equals(cust.yz_open_id));
        if (customer != null)
        {
            Log.Information($"找到客户记录: 手机号={customer.phone}, ID={customer.id}, 原手机号={customer.phone}");
        }
        else
        {
            Log.Warning($"未找到客户记录: 手机号={cust.mobile}, yz_open_id={cust.yz_open_id}");
            return "客户记录不存在";
        }

        // 查找客户日志记录
        var log = await _repository1.Queryable<customerLogsEntity>().FirstAsync(x => x.customerId.Equals(customer.id));
        if (log != null)
        {
            Log.Information($"找到客户日志记录: 手机号={customer.phone}, ID={log.id}, 客户ID={log.customerId}");
        }
        else
        {
            Log.Information($"未找到客户日志记录: 手机号={customer.phone}, 客户ID={customer.id}");
        }

        try
        {
            // 更新客户日志记录
            if (log != null)
            {
                log.customerPhone = cust.mobile;
                log.tag_status = "edit";
                await _repository1.Insertable<customerLogsEntity>(log).ExecuteCommandAsync();
                Log.Information($"成功更新客户日志记录: 手机号={log.customerPhone}, ID={log.id}, 状态={log.tag_status}");
            }

            // 更新客户记录
            customer.phone = cust.mobile;
            await _repository1.Updateable<customerEntity>(customer).ExecuteCommandAsync();
            Log.Information($"成功更新客户记录: 手机号={customer.phone}, ID={customer.id}");

            // 更新会员卡记录
            customer_card.phone = cust.mobile;
            await _repository1.Updateable<customerCardEntity>(customer_card).ExecuteCommandAsync();
            Log.Information($"成功更新会员卡记录: 手机号={customer_card.phone}");

            Log.Information($"手机号更新成功: 手机号={cust.mobile}");
        }
        catch (Exception ex)
        {
            Log.Error($"手机号更新事件处理异常: 手机号={cust.mobile}, 错误信息={ex.Message}, 堆栈={ex.StackTrace}");
            return $"手机号更新事件处理异常: {ex.Message}";
        }

        Log.Information($"========== 手机号更新事件处理完成: 手机号={cust.mobile} ==========");
        return "";
    }

    #endregion

    #region 交易支付事件

    /// <summary>
    /// 交易支付事件(新版本).
    /// </summary>
    /// <param name="pushRequest">请求参数.</param>
    /// <returns></returns>
    private async Task<string> TradeEventNew(pushRequest pushRequest)
    {
        try
        {
            Log.Information($"========== 开始处理交易支付事件(新版本): 店铺ID={pushRequest.kdt_id} ==========");

            // 解析推送消息
            var trade = HttpUtility.UrlDecode(pushRequest.msg).ToObject<tradeDto>();
            var order_no = trade.full_order_info.order_info.tid;
            Log.Information($"交易事件详情: 订单号={order_no}, 买家手机号={trade.full_order_info.buyer_info.buyer_phone}, 买家OpenID={trade.full_order_info.buyer_info.yz_open_id}");

            // 检查是否为线下订单
            if (trade.full_order_info.source_info.is_offline_order)
            {
                Log.Information($"线下订单，跳过处理: 订单号={order_no}");
                return "";
            }

            // 检查订单是否已支付
            if (!trade.full_order_info.order_info.order_tags.is_payed)
            {
                Log.Warning($"订单未支付，跳过处理: 订单号={order_no}, 支付状态=未支付");
                return "订单暂未支付！";
            }

            // 检查店铺ID是否有效
            if (pushRequest.kdt_id.IsNullOrEmpty())
            {
                Log.Error($"店铺ID为空: 订单号={order_no}");
                return "店铺id获取失败！";
            }

            // 检查订单是否已存在
            var order = await _repository.Queryable<orderEntity>().FirstAsync(x => x.order_no.Equals(order_no));
            if (order != null)
            {
                Log.Information($"订单已存在: 订单号={order_no}, 买家手机号={order.buyer_phone}");
                return "";
            }

            // 调用GetTradesSold方法获取并保存订单
            var request = new GetTradesSoldRequest
            {
                tid = order_no
            };

            Log.Information($"调用GetTradesSold方法处理订单: 订单号={order_no}");
            var result = await _orderService.GetTradesSold(request);

            if (result != null)
            {
                Log.Information($"订单处理完成: 订单号={order_no}");
                return "";
            }
            else
            {
                Log.Error($"订单处理失败: 订单号={order_no}");
                return "订单处理失败";
            }
        }
        catch (Exception ex)
        {
            Log.Error($"交易支付事件处理异常: 错误信息={ex.Message}, 堆栈={ex.StackTrace}");
            return $"交易支付事件处理异常: {ex.Message}";
        }
    }

    /// <summary>
    /// 交易支付事件.
    /// </summary>
    /// <param name="pushRequest">请求参数.</param>
    /// <returns></returns>
    private async Task<string> TradeEvent(pushRequest pushRequest)
    {
        Log.Information($"========== 开始处理交易支付事件: kdt_id={pushRequest.kdt_id} ==========");

        var trade = HttpUtility.UrlDecode(pushRequest.msg).ToObject<tradeDto>();
        var order_no = trade.full_order_info.order_info.tid;
        Log.Information($"交易事件详情: 订单号={order_no}, 买家OpenID={trade.full_order_info.buyer_info.yz_open_id}");

        var order = await _repository.Queryable<orderEntity>().FirstAsync(x => x.order_no.Equals(order_no));
        if (order != null)
        {
            Log.Information($"订单已存在: 订单号={order_no}");
            return "";
        }

        string phone = "";
        if (trade.full_order_info.order_info.order_tags.is_offline_order)
        {
            Log.Information($"线下订单，跳过处理: 订单号={order_no}");
            return "";
        }

        if (!trade.full_order_info.order_info.order_tags.is_payed)
        {
            Log.Warning($"订单未支付，跳过处理: 订单号={order_no}");
            return "订单暂未支付！";
        }

        if (pushRequest.kdt_id.IsNullOrEmpty())
        {
            Log.Error($"店铺ID为空: 订单号={order_no}");
            return "店铺id获取失败！";
        }

        Log.Information($"获取店铺Token: kdt_id={pushRequest.kdt_id}");
        var token = await _youzanService.GetTokenAsync(pushRequest.kdt_id.ToString());

        var param = new YouzanParameter();
        param.url = "youzan.cloud.secret.decrypt.single/1.0.0";
        param.method = "POST";
        param.body = new { source = trade.full_order_info.buyer_info.buyer_phone }.ToJsonString();
        Log.Information($"调用有赞API解密买家手机号");
        var res = await _youzanService.GetData(param);
        if (res.success)
        {
            phone = res.data.IsNullOrEmpty() ? "匿名用户" : res.data.ToString();
            Log.Information($"成功获取买家手机号: {phone}");
        }
        else
        {
            Log.Error($"获取买家手机号失败: {res.message}");
            return "有赞返回信息:信息解析失败!" + res.message;
        }

        // 订单主体
        Log.Information($"开始构建订单主体信息");
        var order_info = new orderEntity()
        {
            order_no = trade.full_order_info.order_info.tid,
            yz_open_id = trade.full_order_info.buyer_info.yz_open_id,
            buyer_phone = phone,
            status = trade.full_order_info.order_info.status,
            created = trade.full_order_info.order_info.created,
            kdt_id = pushRequest.kdt_id.ToString(),
            kdt_name = pushRequest.kdt_name,
            total_fee = trade.full_order_info.pay_info.total_fee.ParseToDecimal(),
            pay_time = trade.full_order_info.order_info.pay_time,
            pay_type = trade.full_order_info.order_info.pay_type,
            item_type = 0,
            is_fenxiao_order = trade.full_order_info.order_info.order_tags.is_fenxiao_order
        };
        Log.Information($"订单主体信息: 订单号={order_info.order_no}, 买家手机号={order_info.buyer_phone}, 订单状态={order_info.status}, 支付方式={order_info.pay_type}, 总金额={order_info.total_fee}");

        // 订单明细
        Log.Information($"开始构建订单明细信息: 订单号={order_no}, 商品数量={trade.full_order_info.orders.Count}");
        var order_items = new List<orderItemEntity>();
        var i = 1;
        foreach (var item in trade.full_order_info.orders)
        {
            order_items.Add(new orderItemEntity()
            {
                order_seq = i.ToString(),
                order_no = trade.full_order_info.order_info.tid,
                oid = item.oid,
                price = item.price.ParseToDecimal(),
                points_price = item.points_price.ParseToDecimal(),
                payment = item.payment.ParseToDecimal(),
                outer_item_id = item.outer_item_id,
                outer_sku_id = item.outer_sku_id,
                item_barcode = item.item_barcode,
                item_id = item.item_id.ToString(),
                item_no = item.item_no,
                item_type = item.item_type,
                alias = item.alias,
                title = item.title,
                num = item.num,
                discount_price = item.discount_price.ParseToDecimal(),
                is_present = item.is_present.ParseToInt(),
                sku_barcode = item.sku_barcode,
                sku_id = item.sku_id,
                sku_no = item.sku_no,
                sku_properties_name = item.sku_properties_name,
                total_fee = item.total_fee.ParseToDecimal()
            });
            Log.Information($"订单明细项: 序号={i}, 商品ID={item.item_id}, 商品名称={item.title}, 数量={item.num}, 单价={item.price}, 总价={item.total_fee}");
            i++;
        }

        // 订单支付方式
        Log.Information($"开始构建订单支付信息: 订单号={order_no}");
        var order_pay = new orderPaymentEntity()
        {
            id = SnowflakeIdHelper.NextId(),
            order_no = trade.full_order_info.order_info.tid,
            payment = trade.full_order_info.pay_info.payment.ParseToDecimal(),
            post_fee = trade.full_order_info.pay_info.post_fee.ParseToDecimal(),
            outer_transactions = string.Join(",", trade.full_order_info.pay_info.outer_transactions),
            transaction = string.Join(",", trade.full_order_info.pay_info.transaction)
        };
        Log.Information($"订单支付信息: 实付金额={order_pay.payment}, 邮费={order_pay.post_fee}");

        // 开启事务
        Log.Information($"开启事务，准备插入订单数据: 订单号={order_no}");
        _db.BeginTran();
        try
        {
            if (order_info.IsNotEmptyOrNull())
            {
                await _repository.Insertable<orderEntity>(order_info).ExecuteCommandAsync();
                Log.Information($"成功插入订单主体信息: 订单号={order_no}");
            }

            if (order_items.IsNotEmptyOrNull() && order_items.Count > 0)
            {
                await _repository.Insertable<orderItemEntity>(order_items).ExecuteCommandAsync();
                Log.Information($"成功插入{order_items.Count}条订单明细: 订单号={order_no}");
            }

            if (order_pay.IsNotEmptyOrNull())
            {
                await _repository.Insertable<orderPaymentEntity>(order_pay).ExecuteCommandAsync();
                Log.Information($"成功插入订单支付信息: 订单号={order_no}");
            }

            _db.CommitTran();
            Log.Information($"事务提交成功: 订单号={order_no}");
        }
        catch (Exception ex)
        {
            try
            {
                _db.RollbackTran();
                Log.Error($"事务回滚: {ex.Message}");
            }
            catch
            {
                // 忽略回滚失败的异常
            }
            Log.Error($"交易支付事件处理异常: {ex.Message}, 堆栈: {ex.StackTrace}");
            return $"交易支付事件处理异常: {ex.Message}";
        }

        Log.Information($"========== 交易支付事件处理完成: 订单号={order_no} ==========");
        return "";
    }

    #endregion

    #region 退货/退款事件
    /// <summary>
    /// 退货事件.
    /// </summary>
    /// <param name="pushRequest">请求参数.</param>
    /// <returns></returns>
    private async Task<string> RefundEvent(pushRequest pushRequest)
    {
        Log.Information($"========== 开始处理退货/退款事件: 店铺ID={pushRequest.kdt_id} ==========");

        var refund = HttpUtility.UrlDecode(pushRequest.msg).ToObject<refundDto>();
        var order_no = refund.refund_id;
        Log.Information($"退款事件详情: 退款单号={order_no}, 订单号={refund.tid}, 退款金额={refund.refunded_fee}, 退款类型={refund.refund_type}, 退款原因={refund.refund_reason}");

        var refund_order = await _repository.Queryable<refundEntity>().FirstAsync(x => x.order_no.Equals(order_no));
        if (refund_order != null)
        {
            Log.Information($"退款订单已存在: 退款单号={order_no}, 订单号={refund.tid}, 跳过处理");
            return "";
        }

        var refundDto = new refundItemDto();

        Log.Information($"开始构建退款信息: 退款单号={order_no}, 订单号={refund.tid}");
        var order_info = await _repository.Queryable<orderEntity>().Where(w => w.order_no.Equals(refund.tid)).FirstAsync();
        if (order_info != null)
        {
            Log.Information($"找到原始订单信息: 订单号={refund.tid}, 买家手机号={order_info.buyer_phone}");
        }
        else
        {
            Log.Warning($"未找到原始订单信息: 订单号={refund.tid}");
        }

        var refund_info = new refundEntity();
        var refund_items = new List<refundItemEntity>();
        var refund_pay = new List<refundPaymentEntity>();

        Log.Information($"获取店铺Token: 退款单号={order_no}, 店铺ID={pushRequest.kdt_id}");
        var token = await _youzanService.GetTokenAsync(pushRequest.kdt_id.ToString());
        var param = new YouzanParameter();
        param.url = "youzan.trade.refund.get/3.0.1";
        param.method = "POST";
        param.body = new { refund_id = order_no }.ToJsonString();
        Log.Information($"调用有赞API获取退款详情: 退款单号={order_no}");
        var res = await _youzanService.GetData(param);
        if (res.success)
        {
            refundDto = res.data.ToObject<refundItemDto>();
            Log.Information($"成功获取退款详情: 退款单号={order_no}, 退款状态={refundDto.status}");
        }
        else
        {
            Log.Error($"获取退款详情失败: 退款单号={order_no}, 错误信息={res.message}");
            return "有赞返回信息:" + res.message;
        }

        // 退款主体
        Log.Information($"开始构建退款单主体信息: 退款单号={order_no}, 订单号={refund.tid}");
        if (order_info != null)
        {
            refund_info = order_info.Adapt<refundEntity>();
            Log.Information($"从原订单复制信息到退款单: 退款单号={order_no}, 订单号={refund.tid}, 买家手机号={order_info.buyer_phone}");
        }
        refund_info.kdt_name = pushRequest.kdt_name;
        refund_info.kdt_id = pushRequest.kdt_id.ToString();
        refund_info.order_no = order_no;
        refund_info.tid = refund.tid;
        refund_info.refund_reason = refund.refund_reason;
        refund_info.refund_type = refund.refund_type;
        refund_info.refunded_fee = refund.refunded_fee.ParseToDecimal();
        Log.Information($"退款单主体信息: 退款单号={refund_info.order_no}, 订单号={refund_info.tid}, 买家手机号={refund_info.buyer_phone}, 退款金额={refund_info.refunded_fee}, 退款原因={refund_info.refund_reason}");

        if (refundDto.IsNotEmptyOrNull())
        {
            refund_info.status = refundDto.status;
            refund_info.refund_postage = refundDto.refund_postage.ParseToDecimal();

            // 检查并解析日期时间
            try
            {
                //refund_info.created = refundDto.created.ToUniversalTime();
                refund_info.created = refundDto.created;
                Log.Information($"退款单创建时间: 退款单号={order_no}, 创建时间={refund_info.created}");
            }
            catch (Exception ex)
            {
                Log.Warning($"解析退款单创建时间失败: 退款单号={order_no}, 时间值={refundDto.created}, 错误信息={ex.Message}");
            }

            try
            {
                //refund_info.modified = refundDto.modified.ToUniversalTime();
                refund_info.modified = refundDto.modified;
                Log.Information($"退款单修改时间: 退款单号={order_no}, 修改时间={refund_info.modified}");
            }
            catch (Exception ex)
            {
                Log.Warning($"解析退款单修改时间失败: 退款单号={order_no}, 时间值={refundDto.modified}, 错误信息={ex.Message}");
            }

            try
            {
                //refund_info.refund_account_time = refundDto.refund_account_time.ToUniversalTime();
                refund_info.refund_account_time = refundDto.refund_account_time;
                Log.Information($"退款到账时间: 退款单号={order_no}, 到账时间={refund_info.refund_account_time}");
            }
            catch (Exception ex)
            {
                Log.Warning($"解析退款到账时间失败: 退款单号={order_no}, 时间值={refundDto.refund_account_time}, 错误信息={ex.Message}");
            }
        }

        if (refundDto.refund_order_item.IsNotEmptyOrNull())
        {
            // 退货明细
            Log.Information($"开始构建退款明细信息: 退款单号={order_no}, 订单号={refund.tid}, 商品数量={refundDto.refund_order_item.Count}");
            var i = 1;
            foreach (var item in refundDto.refund_order_item)
            {
                var refundItemEntity = new refundItemEntity()
                {
                    order_no = order_no,
                    order_seq = i.ToString(),
                    refund_fee = item.refund_fee.ParseToDecimal() / 100,
                    oid = item.oid,
                    item_num = item.item_num.ParseToInt()
                };
                refund_items.Add(refundItemEntity);
                Log.Information($"退款明细项: 退款单号={order_no}, 序号={i}, OID={item.oid}, 退款金额={refundItemEntity.refund_fee}, 数量={refundItemEntity.item_num}");
                i++;
            }

            // 订单退款方式
            Log.Information($"开始构建退款支付方式信息: 退款单号={order_no}, 退款方式数量={refundDto.refund_fund_list.Count}");
            foreach (var item in refundDto.refund_fund_list)
            {
                var refundPaymentEntity = new refundPaymentEntity()
                {
                    id = SnowflakeIdHelper.NextId(),
                    order_no = order_no,
                    refund_fee = item.refund_fee.ParseToDecimal() / 100,
                    refund_no = item.refund_no,
                    pay_way = item.pay_way
                };
                refund_pay.Add(refundPaymentEntity);
                Log.Information($"退款支付方式: 退款单号={order_no}, 退款流水号={item.refund_no}, 退款方式={item.pay_way}, 退款金额={refundPaymentEntity.refund_fee}");
            }
        }
        else
        {
            Log.Warning($"未获取到退款商品明细信息: 退款单号={order_no}, 订单号={refund.tid}");
        }

        Log.Information($"开启事务，准备插入退款数据: 退款单号={order_no}, 订单号={refund.tid}");
        await _db.BeginTranAsync();
        try
        {
            if (refund_info.IsNotEmptyOrNull())
            {
                await _repository.Insertable<refundEntity>(refund_info).ExecuteCommandAsync();
                Log.Information($"成功插入退款单主体信息: 退款单号={order_no}, 订单号={refund.tid}");
            }

            if (refund_items.IsNotEmptyOrNull() && refund_items.Count > 0)
            {
                await _repository.Insertable<refundItemEntity>(refund_items).ExecuteCommandAsync();
                Log.Information($"成功插入退款明细: 退款单号={order_no}, 订单号={refund.tid}, 明细数量={refund_items.Count}");
            }

            if (refund_pay.IsNotEmptyOrNull() && refund_pay.Count > 0)
            {
                await _repository.Insertable<refundPaymentEntity>(refund_pay).ExecuteCommandAsync();
                Log.Information($"成功插入退款支付方式: 退款单号={order_no}, 订单号={refund.tid}, 支付方式数量={refund_pay.Count}");
            }

            await _db.CommitTranAsync();
            Log.Information($"事务提交成功: 退款单号={order_no}, 订单号={refund.tid}");
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            Log.Error($"事务回滚: 退款单号={order_no}, 订单号={refund.tid}, 错误信息={ex.Message}");
            Log.Error($"退款事件处理异常: 退款单号={order_no}, 订单号={refund.tid}, 错误信息={ex.Message}, 堆栈={ex.StackTrace}");
            return $"退款事件处理异常: {ex.Message}";
        }

        Log.Information($"========== 退款事件处理完成: 退款单号={order_no}, 订单号={refund.tid} ==========");
        return "";
    }

    #endregion

    #region 客户权益卡订阅事件

    /// <summary>
    /// 客户权益卡订阅事件处理方法.
    /// </summary>
    /// <remarks>
    /// 本方法处理有赞平台推送的会员卡、权益卡相关事件，包括：
    /// 1. 卡片发放/购买 (CUSTOMER_CARD_GIVEN/CUSTOMER_CARD_BOUGHT)
    /// 2. 卡片激活 (CUSTOMER_CARD_ACTIVATED)
    /// 3. 卡片延期 (CUSTOMER_CARD_EXTENSION)
    /// 4. 卡片禁用/删除 (CUSTOMER_CARD_DELETED)
    /// 5. 第三方发卡 (CUSTOMER_CARD_GRANTED_FROM_OPEN)
    /// 
    /// 优化说明：
    /// - 所有数据库操作都采用直接执行方式，不使用事务
    /// - 每个操作都是独立的，不需要保证原子性
    /// - 提高了性能并减少了数据库资源占用
    /// </remarks>
    /// <param name="pushRequest">有赞推送的请求数据</param>
    /// <returns>处理结果，空字符串表示成功，否则返回错误信息</returns>
    private async Task<string> CustomerCardEvent(pushRequest pushRequest)
    {
        try
        {
            // 记录事件开始处理的日志
            Log.Information($"========== 开始处理权益卡订阅事件: 类型={pushRequest.status}, kdt_id={pushRequest.kdt_id} ==========");

            // 解析推送消息中的卡片信息
            var card = HttpUtility.UrlDecode(pushRequest.msg).ToObject<customerCardDto>();
            Log.Information($"权益卡订阅事件详情: 卡号={card.card_no}, 手机号={card.mobile}, 卡别名={card.card_alias}, 用户ID={card.user_id}");

            // 处理事件时间，确保有有效的时间值
            DateTime eventTime;
            if (!DateTime.TryParse(card.event_time.ToString(), out eventTime))
            {
                Log.Warning($"解析 event_time 失败: {card.event_time}，使用当前时间代替, 卡号={card.card_no}, 手机号={card.mobile}");
                eventTime = DateTime.Now;
            }
            Log.Information($"权益卡订阅事件时间: {eventTime.ToString("yyyy-MM-dd HH:mm:ss")}, 卡号={card.card_no}, 手机号={card.mobile}");

            // 查询是否存在对应的权益卡记录
            // 修改查询逻辑，使用卡号作为主要查询条件
            var equity_card = await _repository1.Queryable<customerEquityEntity>().With(SqlWith.NoLock)
                .Where(x => x.equity_card_no == card.card_no)
                .Select(p => new customerEquityEntity
                {
                    id = p.id,
                    status = p.status,
                    modify_date = p.modify_date,
                    yzVersion = p.yzVersion,
                    start_time = p.start_time,
                    end_time = p.end_time,
                    tag_mall_status = p.tag_mall_status
                }).FirstAsync();

            // 记录权益卡查询结果
            if (equity_card != null)
            {
                Log.Information($"找到对应权益卡记录: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}, 状态={equity_card.status}, 最后修改时间={equity_card.modify_date}");
            }
            else
            {
                Log.Information($"未找到对应权益卡记录, 卡号={card.card_no}, 手机号={card.mobile}");
            }

            // 查询是否存在对应的客户记录
            // 使用有赞OpenID匹配客户记录
            var customer = await _repository1.Queryable<customerEntity>().With(SqlWith.NoLock)
                .Where(x => x.open_id.Equals(card.yz_open_id))
                .Select(p => new customerEntity { id = p.id, phone = p.phone }).FirstAsync();

            // 记录客户查询结果
            if (customer != null)
            {
                Log.Information($"找到对应客户记录: 卡号={card.card_no}, 手机号={customer.phone}, ID={customer.id}");
            }
            else
            {
                // 如果客户记录不存在，则创建新的客户记录
                Log.Information($"未找到对应客户记录, 卡号={card.card_no}, 手机号={card.mobile}, open_id={card.yz_open_id}");

                // 创建新客户实体，使用雪花算法生成ID
                customer = new customerEntity
                {
                    id = SnowflakeIdHelper.NextId().ToString(),
                    customer_sn = card.card_no, // 使用卡号作为客户编号
                    phone = card.mobile,
                    open_id = card.yz_open_id,
                    // 设置基本信息
                    store_id = pushRequest.kdt_id.ToString(),
                    nick_name = "有赞新会员",
                    // 设置时间信息
                    created_date = eventTime,
                    modify_date = eventTime,
                    // 设置状态信息
                    state = 1, // 正常状态
                    source = "youzan", // 来源为有赞
                    org_phone = card.mobile,
                    card_no = card.card_no,
                    request_id = card.user_id.ToString(),
                    tag_mall_status = "add", // 标记为新增
                    tag_status = "sync", // 标记需要同步客户信息
                    yzVersion = pushRequest.version
                };

                // 插入新客户记录
                await _repository1.Insertable(customer).ExecuteCommandAsync();
                Log.Information($"成功创建新客户记录: 卡号={card.card_no}, 手机号={customer.phone}, ID={customer.id}, OpenID={customer.open_id}");

                // 发布客户信息同步事件，用于后续异步处理
                await _eventPublisher.PublishAsync("Customer:Sync", customer.id);
                Log.Information($"已发布客户信息Customer:Sync同步事件: 卡号={card.card_no}, 手机号={customer.phone}, ID={customer.id}");
            }

            // 准备存储API返回的卡片信息
            var customerCardItemDto = new customerCardItemDto();

            // 根据不同的事件类型处理逻辑
            switch (pushRequest.status)
            {
                // 处理用户发放权益卡和用户购买获得会员卡事件
                case "CUSTOMER_CARD_GIVEN":
                case "CUSTOMER_CARD_BOUGHT":
                    Log.Information($"处理权益卡发放订阅事件: 卡号={card.card_no}, 手机号={card.mobile}");

                    // 如果权益卡记录不存在，则创建新记录
                    if (equity_card.IsNullOrEmpty())
                    {
                        // 创建新的权益卡实体
                        equity_card = new customerEquityEntity()
                        {
                            id = card.card_no,
                            customer_id = card.card_no,
                            customer_request_id = card.user_id.ToString(),
                            yz_open_id = card.yz_open_id,
                            phone = card.mobile,
                            status = 0, // 初始状态为未激活
                            equity_card_alias_id = card.card_alias,
                            equity_card_no = card.card_no,
                            member_card_no = card.card_no,
                            created_date = eventTime,
                            modify_date = eventTime,
                            tag_mall_status = "add",
                            yzVersion = pushRequest.version
                        };
                        Log.Information($"创建权益卡实体: 卡号={card.card_no}, 手机号={equity_card.phone}, ID={equity_card.id}, 状态={equity_card.status}, 版本号={equity_card.yzVersion}");

                        // 调用有赞API获取卡片详细信息，包括有效期
                        var token = await _youzanService.GetTokenAsync(pushRequest.kdt_id.ToString());
                        var param = new YouzanParameter();
                        param.url = "youzan.scrm.customer.card.list/4.0.0";
                        param.method = "POST";
                        var dict = new Dictionary<string, object>();
                        dict.Add("params", new { user = new { account_type = 5, account_id = card.yz_open_id }, page_no = 1 });
                        param.body = dict.ToJsonString();
                        Log.Information($"请求有赞API获取权益卡详情: 卡号={card.card_no}, 手机号={card.mobile}, account_id={card.mobile}");

                        // 发送API请求
                        var res = await _youzanService.GetData(param);
                        if (res.success)
                        {
                            // 解析API返回结果
                            customerCardItemDto = res.data.ToObject<customerCardItemDto>();
                            Log.Information($"成功获取权益卡信息: 卡号={card.card_no}, 手机号={card.mobile}, 共{customerCardItemDto.total}张卡");
                        }
                        else
                        {
                            // API调用失败
                            Log.Error($"获取权益卡信息失败: 卡号={card.card_no}, 手机号={card.mobile}, 错误信息={res.message}");
                            return "有赞返回信息:" + res.message;
                        }

                        // 从API返回的卡片列表中查找匹配的卡片信息
                        if (customerCardItemDto.total > 0 && customerCardItemDto.items.Count > 0)
                        {
                            var item = customerCardItemDto.items.Where(w => w.card_no.Equals(card.card_no) && w.card_alias.Equals(card.card_alias)).FirstOrDefault();
                            if (item != null)
                            {
                                // 设置卡片有效期
                                //equity_card.start_time = item.card_start_time.ToUniversalTime();
                                //equity_card.end_time = item.card_end_time.ToUniversalTime();
                                equity_card.start_time = item.card_start_time;
                                equity_card.end_time = item.card_end_time;
                                Log.Information($"设置权益卡有效期: 卡号={card.card_no}, 手机号={card.mobile}, 开始时间={equity_card.start_time}, 结束时间={equity_card.end_time}");
                            }
                            else
                            {
                                Log.Warning($"在API返回中未找到匹配的卡信息: 卡号={card.card_no}, 手机号={card.mobile}, 卡别名={card.card_alias}");
                            }
                        }

                        try
                        {
                            // 插入权益卡记录 - 不使用事务，直接执行
                            await _repository1.Insertable<customerEquityEntity>(equity_card).ExecuteCommandAsync();
                            Log.Information($"权益卡记录插入成功: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}, 版本号={equity_card.yzVersion}");
                        }
                        catch (SqlException ex) when (ex.Message.Contains("PRIMARY KEY"))
                        {
                            // 捕获主键冲突异常，尝试更新记录
                            Log.Warning($"权益卡插入时发生主键冲突，改为更新操作: 卡号={card.card_no}, 手机号={card.mobile}");

                            await _repository1.Updateable<customerEquityEntity>()
                                .SetColumns(it => new customerEquityEntity
                                {
                                    modify_date = eventTime,
                                    tag_mall_status = "edit",
                                    start_time = equity_card.start_time,
                                    end_time = equity_card.end_time,
                                    yzVersion = pushRequest.version
                                })
                                .Where(w => w.id == card.card_no)
                                .ExecuteCommandAsync();
                            Log.Information($"权益卡信息更新成功: 卡号={card.card_no}, 手机号={card.mobile}, ID={card.card_no}");
                        }

                        // 处理客户信息同步 - 不使用事务，直接执行
                        if (customer != null)
                        {
                            // 更新客户状态标记为需要同步
                            customer.tag_status = "sync";
                            customer.modify_date = eventTime;

                            // 执行更新，仅更新指定字段，避免其他字段被置为NULL
                            await _repository1.Updateable<customerEntity>()
                                .SetColumns(it => new customerEntity
                                {
                                    tag_status = "sync",
                                    modify_date = eventTime
                                })
                                .Where(w => w.id == customer.id)
                                .ExecuteCommandAsync();
                            Log.Information($"已标记客户需要同步信息: 卡号={card.card_no}, 手机号={customer.phone}, ID={customer.id}");

                            // 发布客户信息同步事件
                            await _eventPublisher.PublishAsync("Customer:Sync", customer.id);
                            Log.Information($"已发布客户信息Customer:Sync同步事件: 卡号={card.card_no}, ID={customer.id}");
                        }
                    }
                    else
                    {
                        // 权益卡已存在，不做处理
                        Log.Information($"权益卡记录已存在: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}");
                    }
                    break;

                // 处理权益卡激活事件
                case "CUSTOMER_CARD_ACTIVATED":
                    Log.Information($"处理权益卡激活订阅事件: 卡号={card.card_no}, 手机号={card.mobile}");

                    // 如果权益卡记录存在，则更新状态为激活
                    if (!equity_card.IsNullOrEmpty())
                    {
                        Log.Information($"开始更新权益卡状态为激活: 卡号={card.card_no}, 手机号={card.mobile}, 原状态={equity_card.status}");
                        equity_card.status = 1;

                        // 更新权益卡状态 - 只更新status字段，避免其他字段被置为NULL
                        await _repository1.Updateable<customerEquityEntity>()
                            .SetColumns(it => new customerEquityEntity { status = 1 })
                            .Where(w => w.id == equity_card.id)
                            .ExecuteCommandAsync();
                        Log.Information($"权益卡激活成功: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}, 新状态=1");
                    }
                    else
                    {
                        // 权益卡记录不存在，直接返回错误，依靠消息重推机制重试
                        Log.Warning($"权益卡记录不存在，无法激活: 卡号={card.card_no}, 手机号={card.mobile}");
                        return $"权益卡记录不存在，卡号={card.card_no}，依靠消息重推机制重试";
                    }
                    break;

                // 处理权益卡延期事件
                case "CUSTOMER_CARD_EXTENSION":
                    Log.Information($"处理权益卡延期订阅事件: 卡号={card.card_no}, 手机号={card.mobile}");

                    // 标记是否为新记录
                    bool isNewRecord = equity_card.IsNullOrEmpty();

                    // 版本控制 - 如果现有记录版本号更高，则跳过处理
                    if (!isNewRecord && pushRequest.version <= equity_card.yzVersion && equity_card.yzVersion != null)
                    {
                        Log.Information($"跳过处理: 卡号={card.card_no}, 手机号={card.mobile}, 推送版本({pushRequest.version})小于等于现有版本({equity_card.yzVersion})");
                        return "";
                    }

                    // 处理权益卡记录
                    if (!isNewRecord)
                    {
                        // 如果记录存在，准备更新有效期
                        Log.Information($"开始延长权益卡有效期: 卡号={card.card_no}, 手机号={card.mobile}, 原结束时间={equity_card.end_time}");
                    }
                    else
                    {
                        // 如果记录不存在，创建新记录
                        Log.Information($"未找到权益卡记录,准备新建记录: 卡号={card.card_no}, 手机号={card.mobile}");
                        equity_card = new customerEquityEntity()
                        {
                            // 使用卡号作为记录ID
                            id = card.card_no,
                            // 客户相关信息
                            customer_id = card.card_no,
                            customer_request_id = card.user_id.ToString(),
                            yz_open_id = card.yz_open_id,
                            phone = card.mobile,
                            // 初始状态为激活
                            status = 1,
                            // 卡片相关信息
                            equity_card_alias_id = card.card_alias,
                            equity_card_no = card.card_no,
                            member_card_no = card.card_no,
                            // 时间信息
                            created_date = eventTime,
                            modify_date = eventTime,
                            // 标记为新增记录
                            tag_mall_status = "add",
                            // 设置版本号
                            yzVersion = pushRequest.version
                        };
                        Log.Information($"创建权益卡实体: 卡号={card.card_no}, 手机号={equity_card.phone}, ID={equity_card.id}, 状态={equity_card.status}, 版本号={equity_card.yzVersion}");
                    }

                    // 获取有赞平台上的权益卡信息，用于更新有效期
                    var cardToken = await _youzanService.GetTokenAsync(pushRequest.kdt_id.ToString());
                    var cardParam = new YouzanParameter();
                    cardParam.url = "youzan.scrm.customer.card.list/4.0.0";
                    cardParam.method = "POST";
                    var cardDict = new Dictionary<string, object>();
                    cardDict.Add("params", new { user = new { account_type = 5, account_id = card.yz_open_id }, page_no = 1 });
                    cardParam.body = cardDict.ToJsonString();
                    Log.Information($"请求有赞API获取权益卡详情: 卡号={card.card_no}, 手机号={card.mobile}, account_id={card.mobile}");

                    // 发送API请求
                    var cardRes = await _youzanService.GetData(cardParam);
                    if (cardRes.success)
                    {
                        // 解析API返回结果
                        customerCardItemDto = cardRes.data.ToObject<customerCardItemDto>();
                        Log.Information($"成功获取权益卡信息: 卡号={card.card_no}, 手机号={card.mobile}, 共{customerCardItemDto.total}张卡");
                    }
                    else
                    {
                        // API调用失败
                        Log.Error($"获取权益卡信息失败: 卡号={card.card_no}, 手机号={card.mobile}, 错误信息={cardRes.message}");
                        return "有赞返回信息:" + cardRes.message;
                    }

                    // 从API返回的卡片列表中查找匹配的卡片，获取延期后的有效期
                    if (customerCardItemDto.total > 0 && customerCardItemDto.items.Count > 0)
                    {
                        var item = customerCardItemDto.items.Where(w => w.card_no.Equals(card.card_no) && w.card_alias.Equals(card.card_alias)).FirstOrDefault();
                        if (item != null)
                        {
                            // 记录旧的有效期信息，用于日志记录
                            var oldStartTime = equity_card.start_time;
                            var oldEndTime = equity_card.end_time;

                            // 更新有效期
                            //equity_card.start_time = item.card_start_time.ToUniversalTime();
                            //equity_card.end_time = item.card_end_time.ToUniversalTime();
                            equity_card.start_time = item.card_start_time;
                            equity_card.end_time = item.card_end_time;
                            Log.Information($"更新权益卡有效期: 卡号={card.card_no}, 手机号={card.mobile}, 开始时间从{oldStartTime}变更为{equity_card.start_time}, 结束时间从{oldEndTime}变更为{equity_card.end_time}");
                        }
                        else
                        {
                            Log.Warning($"在API返回中未找到匹配的卡信息: 卡号={card.card_no}, 手机号={card.mobile}, 卡别名={card.card_alias}");
                        }
                    }

                    // 保存权益卡记录
                    if (!equity_card.IsNullOrEmpty())
                    {
                        // 更新修改时间和版本号
                        equity_card.modify_date = eventTime;
                        equity_card.yzVersion = pushRequest.version;

                        if (isNewRecord) // 新记录，直接插入
                        {
                            await _repository1.Insertable<customerEquityEntity>(equity_card).ExecuteCommandAsync();
                            Log.Information($"新增权益卡记录成功: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}, 版本号={equity_card.yzVersion}");
                        }
                        else // 已有记录，更新有效期
                        {
                            // 标记为编辑状态
                            equity_card.tag_mall_status = "edit";

                            // 更新记录，只更新指定字段
                            await _repository1.Updateable<customerEquityEntity>()
                                .SetColumns(it => new customerEquityEntity
                                {
                                    modify_date = eventTime,
                                    yzVersion = pushRequest.version,
                                    tag_mall_status = "edit",
                                    start_time = equity_card.start_time,
                                    end_time = equity_card.end_time
                                })
                                .Where(w => w.id == equity_card.id)
                                .ExecuteCommandAsync();
                            Log.Information($"权益卡延期成功: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}, 新结束时间={equity_card.end_time}, 版本号={equity_card.yzVersion}");
                        }
                    }
                    break;

                // 处理权益卡禁用事件
                case "CUSTOMER_CARD_DELETED":
                    Log.Information($"处理权益卡禁用订阅事件: 卡号={card.card_no}, 手机号={card.mobile}");

                    // 只有当权益卡存在时才能禁用
                    if (!equity_card.IsNullOrEmpty())
                    {
                        Log.Information($"开始禁用权益卡: 卡号={card.card_no}, 手机号={card.mobile}, 原状态={equity_card.status}, tag_status={equity_card.tag_mall_status}");

                        // 设置需要更新的字段
                        equity_card.modify_date = eventTime;
                        equity_card.tag_mall_status = "del";
                        equity_card.status = -1;

                        // 更新权益卡状态 - 只更新需要的字段
                        await _repository1.Updateable<customerEquityEntity>()
                            .SetColumns(it => new customerEquityEntity
                            {
                                status = -1,
                                modify_date = eventTime,
                                tag_mall_status = "del"
                            })
                            .Where(w => w.id == equity_card.id)
                            .ExecuteCommandAsync();
                        Log.Information($"权益卡禁用成功: 卡号={card.card_no}, 手机号={card.mobile}, ID={equity_card.id}, 新状态=-1, tag_status=del");
                    }
                    else
                    {
                        // 如果权益卡不存在，无法禁用
                        Log.Error($"权益卡禁用失败: 卡号={card.card_no}, 手机号={card.mobile}, 卡别名={card.card_alias}");
                        return "权益卡记录不存在";
                    }
                    break;

                // 处理第三方发卡事件 - 直接跳过
                case "CUSTOMER_CARD_GRANTED_FROM_OPEN":
                    Log.Information($"三方通过开放平台发卡订阅事件（AOS发卡）: 卡号={card.card_no}, 手机号={card.mobile}，直接跳过");
                    break;

                // 处理其他未知事件类型
                default:
                    // 记录未知事件类型的日志
                    Log.Warning($"未知的权益卡订阅事件类型: 卡号={card.card_no}, 手机号={card.mobile}, 状态={pushRequest.status}");
                    break;
            }

            // 记录事件处理完成的日志
            Log.Information($"========== 权益卡订阅事件处理完成: 卡号={card.card_no}, 手机号={card.mobile}, 类型={pushRequest.status} ==========");
            return "";
        }
        catch (Exception ex)
        {
            // 记录异常信息
            Log.Error($"权益卡订阅事件处理异常: {ex.Message}, 堆栈: {ex.StackTrace}");
            return $"权益卡订阅事件处理异常: {ex.Message}";
        }
    }

    #endregion

}
