@if(Model.IsUploading)
{
@:using BPM.Common.Models;
@:using Newtonsoft.Json;
}
@foreach (var table in Model.TableRelations)
{
@:using BPM.@(Model.NameSpace).<EMAIL>;
}

namespace BPM.@(Model.NameSpace).<EMAIL>;

/// <summary>
/// @(Model.BusName)详情输出参数.
/// </summary>
public class @(Model.ClassName)DetailOutput
{
@foreach (var column in Model.TableField)
{
@if (column.PrimaryKey){
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}
else if (column.bpmKey != null)
{
switch(column.bpmKey)
{
case "slider":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public int @column.LowerColumnName { get; set; }
@:
break;
case "datePicker":
case "createTime":
case "modifyTime":
case "switch":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @column.LowerColumnName { get; set; }
@:
break;
case "uploadFile":
case "uploadImg":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @column.LowerColumnName { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "string" : column.NetType) @column.LowerColumnName { get; set; }
@:
break;
}
}
}
@foreach (var table in Model.TableRelations)
{
    @:/// <summary>
    @:/// @(table.TableComment).
    @:/// </summary>
    @:public List<@(table.ClassName)DetailOutput> @table.ControlModel { get; set; }
@:
}
}