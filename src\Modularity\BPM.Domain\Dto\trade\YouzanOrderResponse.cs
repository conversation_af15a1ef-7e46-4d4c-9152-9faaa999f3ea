using System.Collections.Generic;
using Newtonsoft.Json;

namespace BPM.Domain.Dto.trade;

/// <summary>
/// 有赞订单响应
/// </summary>
public class YouzanOrderResponse
{
    public string trace_id { get; set; }
    public int code { get; set; }
    public List<YouzanOrderInfo> full_order_info_list { get; set; }
    public int total_results { get; set; }
}

/// <summary>
/// 有赞订单信息
/// </summary>
public class YouzanOrderInfo
{
    public YouzanFullOrderInfo full_order_info { get; set; }
}

/// <summary>
/// 订单完整信息
/// </summary>
public class YouzanFullOrderInfo
{
    public YouzanChildInfo child_info { get; set; }
    public YouzanRemarkInfo remark_info { get; set; }
    public YouzanAddressInfo address_info { get; set; }
    public YouzanPayInfo pay_info { get; set; }
    public YouzanBuyerInfo buyer_info { get; set; }
    public List<YouzanOrderItem> orders { get; set; }
    public YouzanSourceInfo source_info { get; set; }
    public YouzanOrderBaseInfo order_info { get; set; }
}

/// <summary>
/// 子订单信息
/// </summary>
public class YouzanChildInfo
{
    public List<object> child_orders { get; set; }
}

/// <summary>
/// 备注信息
/// </summary>
public class YouzanRemarkInfo
{
    public string buyer_message { get; set; }
}

/// <summary>
/// 地址信息
/// </summary>
public class YouzanAddressInfo
{
    public string self_fetch_info { get; set; }
    public string delivery_address { get; set; }
    public string delivery_postal_code { get; set; }
    public string receiver_name { get; set; }
    public string delivery_province { get; set; }
    public string delivery_city { get; set; }
    public string delivery_district { get; set; }
    public string address_extra { get; set; }
    public string receiver_tel { get; set; }
}

/// <summary>
/// 支付信息
/// </summary>
public class YouzanPayInfo
{
    public List<object> outer_transactions { get; set; }
    public string post_fee { get; set; }
    public List<object> phase_payments { get; set; }
    public string total_fee { get; set; }
    public string payment { get; set; }
    public List<object> transaction { get; set; }
}

/// <summary>
/// 买家信息
/// </summary>
public class YouzanBuyerInfo
{
    public string outer_user_id { get; set; }
    public string buyer_phone { get; set; }
    public string yz_open_id { get; set; }
    public int fans_type { get; set; }
    public long fans_id { get; set; }
    public string fans_nickname { get; set; }
}

/// <summary>
/// 来源信息
/// </summary>
public class YouzanSourceInfo
{
    public string order_source_code { get; set; }
    public bool is_offline_order { get; set; }
    public string book_key { get; set; }
    public string order_source { get; set; }
    public string biz_source { get; set; }
    public YouzanSource source { get; set; }
    public string order_mark { get; set; }
}

/// <summary>
/// 来源详细信息
/// </summary>
public class YouzanSource
{
    public string platform { get; set; }
    public string wx_entrance { get; set; }
}

/// <summary>
/// 订单基础信息
/// </summary>
public class YouzanOrderBaseInfo
{
    /// <summary>
    /// 发货时间
    /// </summary>
    public string consign_time { get; set; }

    /// <summary>
    /// 订单扩展信息
    /// </summary>
    public YouzanOrderExtra order_extra { get; set; }

    /// <summary>
    /// 订单创建时间
    /// </summary>
    public string created { get; set; }

    /// <summary>
    /// 订单过期时间
    /// </summary>
    public string expired_time { get; set; }

    /// <summary>
    /// 订单状态描述
    /// </summary>
    public string status_str { get; set; }

    /// <summary>
    /// 订单成功时间
    /// </summary>
    public string success_time { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public int type { get; set; }

    /// <summary>
    /// 店铺名称
    /// </summary>
    public string shop_name { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public string tid { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public string confirm_time { get; set; }

    /// <summary>
    /// 支付时间
    /// </summary>
    public string pay_time { get; set; }

    /// <summary>
    /// 店铺ID
    /// </summary>
    public long node_kdt_id { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public string update_time { get; set; }

    /// <summary>
    /// 支付方式描述
    /// </summary>
    public string pay_type_str { get; set; }

    /// <summary>
    /// 是否为零售订单
    /// </summary>
    public bool is_retail_order { get; set; }

    /// <summary>
    /// 后台订单类型
    /// </summary>
    public string backstage_order_type { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public int pay_type { get; set; }

    /// <summary>
    /// 团队类型
    /// </summary>
    public int team_type { get; set; }

    /// <summary>
    /// 退款状态
    /// </summary>
    public int refund_state { get; set; }

    /// <summary>
    /// 根店铺ID
    /// </summary>
    public long root_kdt_id { get; set; }

    /// <summary>
    /// 关闭类型
    /// </summary>
    public int close_type { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public string status { get; set; }

    /// <summary>
    /// 物流类型
    /// </summary>
    public int express_type { get; set; }

    /// <summary>
    /// 订单标签
    /// </summary>
    public YouzanOrderTags order_tags { get; set; }
}

/// <summary>
/// 订单扩展信息
/// </summary>
public class YouzanOrderExtra
{
    /// <summary>
    /// 是否来自购物车
    /// </summary>
    public string is_from_cart { get; set; }

    /// <summary>
    /// 是否是会员
    /// </summary>
    public string is_member { get; set; }

    /// <summary>
    /// 买家名称
    /// </summary>
    public string buyer_name { get; set; }

    /// <summary>
    /// 是否积分订单
    /// </summary>
    public string is_points_order { get; set; }

    /// <summary>
    /// 导购信息
    /// </summary>
    public object sales_guide { get; set; }

    /// <summary>
    /// 服务导购信息
    /// </summary>
    public YouzanServiceGuide service_guide { get; set; }
}

/// <summary>
/// 服务导购信息
/// </summary>
public class YouzanServiceGuide
{
    /// <summary>
    /// 服务导购员工号
    /// </summary>
    public string service_guide_staff { get; set; }

    /// <summary>
    /// 服务导购店铺编号
    /// </summary>
    public string service_guide_shop_no { get; set; }

    /// <summary>
    /// 服务导购名称
    /// </summary>
    public string service_guide_name { get; set; }
}

/// <summary>
/// 订单标签
/// </summary>
public class YouzanOrderTags
{
    /// <summary>
    /// 是否会员订单
    /// </summary>
    public bool is_member { get; set; }

    /// <summary>
    /// 是否担保交易
    /// </summary>
    public bool is_secured_transactions { get; set; }

    /// <summary>
    /// 是否有反馈
    /// </summary>
    public bool is_feedback { get; set; }

    /// <summary>
    /// 是否已支付
    /// </summary>
    public bool is_payed { get; set; }
}

/// <summary>
/// 订单商品信息
/// </summary>
public class YouzanOrderItem
{
    /// <summary>
    /// 是否跨境
    /// </summary>
    public string is_cross_border { get; set; }

    /// <summary>
    /// 外部商品ID
    /// </summary>
    public string outer_item_id { get; set; }

    /// <summary>
    /// 商品类型。0:普通类型商品
    /// </summary>
    public int item_type { get; set; }

    /// <summary>
    /// 优惠后价格
    /// </summary>
    public string discount_price { get; set; }

    /// <summary>
    /// 商品数量
    /// </summary>
    public int num { get; set; }

    /// <summary>
    /// 订单明细ID
    /// </summary>
    public string oid { get; set; }

    /// <summary>
    /// 商品标题
    /// </summary>
    public string title { get; set; }

    /// <summary>
    /// 分销支付金额
    /// </summary>
    public string fenxiao_payment { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string item_no { get; set; }

    /// <summary>
    /// 买家留言
    /// </summary>
    public string buyer_messages { get; set; }

    /// <summary>
    /// 根SKU ID
    /// </summary>
    public string root_sku_id { get; set; }

    /// <summary>
    /// 是否赠品
    /// </summary>
    public bool is_present { get; set; }

    /// <summary>
    /// 跨境贸易类型
    /// </summary>
    public string cross_border_trade_mode { get; set; }

    /// <summary>
    /// 商品原价
    /// </summary>
    public string price { get; set; }

    /// <summary>
    /// 子订单号
    /// </summary>
    public string sub_order_no { get; set; }

    /// <summary>
    /// 商品总价
    /// </summary>
    public string total_fee { get; set; }

    /// <summary>
    /// 分销价格
    /// </summary>
    public string fenxiao_price { get; set; }

    /// <summary>
    /// 商品别名
    /// </summary>
    public string alias { get; set; }

    /// <summary>
    /// 实付金额
    /// </summary>
    public string payment { get; set; }

    /// <summary>
    /// 是否预售
    /// </summary>
    public string is_pre_sale { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string item_barcode { get; set; }

    /// <summary>
    /// 外部SKU ID
    /// </summary>
    public string outer_sku_id { get; set; }

    /// <summary>
    /// SKU唯一码
    /// </summary>
    public string sku_unique_code { get; set; }

    /// <summary>
    /// 商品链接
    /// </summary>
    public string goods_url { get; set; }

    /// <summary>
    /// 海关编码
    /// </summary>
    public string customs_code { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long item_id { get; set; }

    /// <summary>
    /// 商品标签
    /// </summary>
    [JsonProperty("item_tags")]
    public object item_tags { get; set; }

    /// <summary>
    /// 商品重量
    /// </summary>
    public string weight { get; set; }

    /// <summary>
    /// SKU ID
    /// </summary>
    public long sku_id { get; set; }

    /// <summary>
    /// SKU属性名称
    /// </summary>
    public string sku_properties_name { get; set; }

    /// <summary>
    /// 商品图片
    /// </summary>
    public string pic_path { get; set; }

    /// <summary>
    /// 是否组合商品
    /// </summary>
    public bool is_combo { get; set; }

    /// <summary>
    /// 预售类型
    /// </summary>
    public string pre_sale_type { get; set; }

    /// <summary>
    /// 积分价格
    /// </summary>
    public string points_price { get; set; }

    /// <summary>
    /// SKU编码
    /// </summary>
    public string sku_no { get; set; }

    /// <summary>
    /// 根商品ID
    /// </summary>
    public string root_item_id { get; set; }

    /// <summary>
    /// SKU条码
    /// </summary>
    public string sku_barcode { get; set; }
} 