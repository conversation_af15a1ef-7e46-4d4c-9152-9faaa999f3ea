﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.equity;

/// <summary>
/// 创建权益卡列表
/// </summary>
[SuppressSniffer]
public class createCustomerEquityRequest
{

    /// <summary>
    ///卡别名，不能自定义，需要从接口youzan.scrm.card.list 中获取。
    /// </summary>
    public string card_alias { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public userEquityInfo user { get; set; }

}

public class userEquityInfo
{
    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 帐号类型（支持的用户账号类型 1-有赞粉丝id(有赞不同的合作渠道会生成不同渠道对应在有赞平台下的fans_id); 2-手机号; 4-union_id(同一用户，对同一个微信开放平台下的不同应用，unionid是相同的); 5-yz_open_id，推荐使用））
    /// </summary>
    public int account_type { get; set; } = 2;

}