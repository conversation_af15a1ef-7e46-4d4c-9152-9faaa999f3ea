﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.product;

/// <summary>
/// 更新单价请求
/// </summary>
[SuppressSniffer]
public class updateProducPriceRequest
{
    /// <summary>
    /// 商品Id
    /// </summary>
    public long item_id { get; set; }

    /// <summary>
    /// 商品sku集合
    /// </summary>
    public List<SkuPriceParam> sku_list { get; set; }

    /// <summary>
    /// 无规格
    /// </summary>
    public NoSukPriceParam item_price_param { get; set; }

    /// <summary>
    /// 多渠道价格参数
    /// </summary>
    public List<ItemChannelParam> item_channel_params { get; set; }
}

/// <summary>
/// 渠道价格参数
/// </summary>
public class ItemChannelParam
{
    /// <summary>
    /// 渠道类型：0-网店，1-门店
    /// </summary>
    public int channel { get; set; }

    /// <summary>
    /// 价格参数
    /// </summary>
    public NoSukPriceParam item_price_param { get; set; }
}

/// <summary>
/// sku 请求参数
/// </summary>
public class SkuPriceParam
{
    /// <summary>
    /// 商品的这个Sku的价格；单位：分
    /// </summary>
    public long price { get; set; }

    /// <summary>
    /// 商品规格Id
    /// </summary>
    public long sku_id { get; set; }
}

/// <summary>
/// 无规格价格实体
/// </summary>
public class NoSukPriceParam
{
    /// <summary>
    /// 价格(分)
    /// </summary>
    public long price { get; set; }

    /// <summary>
    /// 划线价格(元)
    /// </summary>
    public string origin { get; set; }
}