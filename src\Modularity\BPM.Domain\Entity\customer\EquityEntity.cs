﻿using SqlSugar;
namespace BPM.Domain.Entity.customer;
/// <summary>
/// 权益实体
/// </summary>
[SugarTable("EQUITY")]
[Tenant("IPOS-CRM")]
public class EquityEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime? create_time { get; set; }

    /// <summary>
    ///  权益卡类型;3:无门槛卡,2:付费卡,1:规则卡
    /// </summary>
    public string grant_type { get; set; }

    /// <summary>
    /// 权益卡名称
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 商家权益卡的id
    /// </summary>
    public string card_id { get; set; }

    /// <summary>
    /// 商家权益卡的唯一标识
    /// </summary>
    public string card_alias { get; set; }

    /// <summary>
    /// 发卡链接
    /// </summary>
    public string card_url { get; set; }

    /// <summary>
    ///  权益卡状态：使用中:true 已禁用:false
    /// </summary>
    public bool is_available { get; set; }

    /// <summary>
    /// 等级id
    /// </summary>
    public string grade_id { get; set; }

}

