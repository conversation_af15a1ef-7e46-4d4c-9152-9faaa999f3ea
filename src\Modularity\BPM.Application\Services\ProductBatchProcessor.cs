using BPM.Application.Configuration;
using BPM.DependencyInjection;
using BPM.Domain.Requests.product;
using BPM.Extras.Youzan.Services;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace BPM.Application.Services;

/// <summary>
/// 商品批量处理器
/// </summary>
[SuppressSniffer]
public class ProductBatchProcessor
{
    private readonly IYouzanService _youzanService;
    private readonly ProductSyncConfiguration _config;
    private readonly ProductSyncResultHandler _resultHandler;

    public ProductBatchProcessor(
        IYouzanService youzanService,
        IOptions<ProductSyncConfiguration> configOptions,
        ProductSyncResultHandler resultHandler)
    {
        _youzanService = youzanService;
        _config = configOptions.Value;
        _resultHandler = resultHandler;
    }

    /// <summary>
    /// 批量重新同步门店商品
    /// </summary>
    /// <param name="barcodes">条码列表</param>
    /// <param name="processProductsFunc">处理商品的函数</param>
    /// <returns>处理结果</returns>
    public async Task<bool> BatchResyncStoreProductsAsync(
        IEnumerable<string> barcodes,
        Func<getOnSaleProductRequest, Task<dynamic>> processProductsFunc)
    {
        var uniqueBarcodes = barcodes.Distinct().ToList();
        if (uniqueBarcodes.Count == 0)
        {
            Console.WriteLine("没有需要重新同步的门店商品");
            return true;
        }

        // 检查是否有正在处理的条码，避免冲突
        var filteredBarcodes = await FilterConflictingBarcodesAsync(uniqueBarcodes, "重新同步门店商品");
        if (filteredBarcodes.Count == 0)
        {
            Console.WriteLine("所有条码都在处理中或已处理，跳过重新同步");
            return true;
        }

        Console.WriteLine($"开始批量重新同步 {filteredBarcodes.Count} 个被删除的门店商品（已过滤 {uniqueBarcodes.Count - filteredBarcodes.Count} 个冲突条码）");

        var semaphore = new SemaphoreSlim(_config.MaxParallelism, _config.MaxParallelism);
        var tasks = new List<Task>();

        foreach (var batch in CreateBatches(filteredBarcodes, _config.CompleteBatchSize))
        {
            tasks.Add(ProcessBatchAsync(batch, semaphore, processProductsFunc, "重新同步门店商品"));
        }

        await Task.WhenAll(tasks);

        Console.WriteLine($"完成批量重新同步门店商品，处理条码数量: {filteredBarcodes.Count}");
        return true;
    }

    /// <summary>
    /// 批量补全商品
    /// </summary>
    /// <param name="barcodes">条码列表</param>
    /// <param name="processProductsFunc">处理商品的函数</param>
    /// <returns>处理结果</returns>
    public async Task<bool> BatchCompleteProductsAsync(
        IEnumerable<string> barcodes,
        Func<getOnSaleProductRequest, Task<dynamic>> processProductsFunc)
    {
        var uniqueBarcodes = barcodes.Distinct().ToList();
        if (uniqueBarcodes.Count == 0)
        {
            Console.WriteLine("没有需要补全的商品");
            return true;
        }

        // 检查是否有正在处理的条码，避免冲突
        var filteredBarcodes = await FilterConflictingBarcodesAsync(uniqueBarcodes, "补全商品");
        if (filteredBarcodes.Count == 0)
        {
            Console.WriteLine("所有条码都在处理中或已处理，跳过补全");
            return true;
        }

        Console.WriteLine($"开始批量补全 {filteredBarcodes.Count} 个商品（已过滤 {uniqueBarcodes.Count - filteredBarcodes.Count} 个冲突条码）");

        var semaphore = new SemaphoreSlim(_config.MaxParallelism, _config.MaxParallelism);
        var tasks = new List<Task>();

        foreach (var batch in CreateBatches(filteredBarcodes, _config.CompleteBatchSize))
        {
            tasks.Add(ProcessBatchAsync(batch, semaphore, processProductsFunc, "补全商品"));
        }

        await Task.WhenAll(tasks);

        Console.WriteLine($"完成批量补全商品，处理条码数量: {filteredBarcodes.Count}");
        return true;
    }

    /// <summary>
    /// 处理单个批次
    /// </summary>
    /// <param name="batch">批次数据</param>
    /// <param name="semaphore">信号量</param>
    /// <param name="processProductsFunc">处理函数</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>处理任务</returns>
    private async Task ProcessBatchAsync(
        IEnumerable<string> batch,
        SemaphoreSlim semaphore,
        Func<getOnSaleProductRequest, Task<dynamic>> processProductsFunc,
        string operationName)
    {
        await semaphore.WaitAsync();
        try
        {
            var batchList = batch.ToList();
            Console.WriteLine($"开始处理{operationName}批次，数量: {batchList.Count}");

            var batchSucceed = 0;
            var batchFail = 0;

            foreach (var barcode in batchList)
            {
                var success = await _resultHandler.SafeExecuteAsync(async () =>
                {
                    var request = new getOnSaleProductRequest
                    {
                        q = barcode,
                        page_size = 20
                    };

                    Console.WriteLine($"{operationName} - barcode: {barcode}");
                    var result = await processProductsFunc(request);

                    if (result?.succeed > 0)
                    {
                        batchSucceed++;
                        Console.WriteLine($"成功{operationName} - barcode: {barcode}, 数量: {result.succeed}");
                    }
                    else
                    {
                        batchFail++;
                        Console.WriteLine($"未找到{operationName}数据 - barcode: {barcode}");
                    }
                }, $"{operationName} - {barcode}");

                if (success)
                {
                    _resultHandler.AddSucceed(1);
                }

                // 控制API调用频率
                if (batchList.IndexOf(barcode) % 5 == 4)
                {
                    await Task.Delay(_config.ApiCallDelay);
                }
            }

            Console.WriteLine($"{operationName}批次处理完成 - 成功: {batchSucceed}, 失败: {batchFail}");
            
            // 批次间延迟
            await Task.Delay(_config.BatchDelay);
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// 创建批次
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="source">源数据</param>
    /// <param name="batchSize">批次大小</param>
    /// <returns>批次集合</returns>
    private static IEnumerable<IEnumerable<T>> CreateBatches<T>(IEnumerable<T> source, int batchSize)
    {
        var sourceList = source.ToList();
        for (int i = 0; i < sourceList.Count; i += batchSize)
        {
            yield return sourceList.Skip(i).Take(batchSize);
        }
    }

    /// <summary>
    /// 并行处理多个条码
    /// </summary>
    /// <param name="barcodes">条码列表</param>
    /// <param name="processFunc">处理函数</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>处理结果</returns>
    public async Task<ConcurrentBag<T>> ProcessInParallelAsync<T>(
        IEnumerable<string> barcodes,
        Func<string, Task<T>> processFunc,
        string operationName)
    {
        var results = new ConcurrentBag<T>();
        var semaphore = new SemaphoreSlim(_config.MaxParallelism, _config.MaxParallelism);

        var tasks = barcodes.Select(async barcode =>
        {
            await semaphore.WaitAsync();
            try
            {
                var result = await _resultHandler.SafeExecuteAsync(
                    () => processFunc(barcode),
                    $"{operationName} - {barcode}");
                
                if (result != null)
                {
                    results.Add(result);
                }
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
        return results;
    }

    /// <summary>
    /// 过滤冲突的条码，避免重复处理
    /// </summary>
    /// <param name="barcodes">条码列表</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>过滤后的条码列表</returns>
    private async Task<List<string>> FilterConflictingBarcodesAsync(List<string> barcodes, string operationName)
    {
        if (!_config.EnableConflictDetection)
        {
            return barcodes;
        }

        var filteredBarcodes = new List<string>();
        var processingBarcodes = new ConcurrentBag<string>();

        // 检查每个条码是否正在被处理
        var checkTasks = barcodes.Select(async barcode =>
        {
            var isProcessing = await IsBarCodeProcessingAsync(barcode);
            if (!isProcessing)
            {
                processingBarcodes.Add(barcode);
            }
            else
            {
                Console.WriteLine($"{operationName} - 跳过正在处理的条码: {barcode}");
            }
        });

        await Task.WhenAll(checkTasks);
        filteredBarcodes.AddRange(processingBarcodes);

        return filteredBarcodes;
    }

    /// <summary>
    /// 检查条码是否正在被处理
    /// </summary>
    /// <param name="barcode">条码</param>
    /// <returns>是否正在处理</returns>
    private async Task<bool> IsBarCodeProcessingAsync(string barcode)
    {
        try
        {
            // 检查是否在最近的时间窗口内被处理过
            var recentTimeWindow = DateTime.Now.AddMinutes(-_config.ConflictDetectionWindowMinutes);

            // 这里可以添加更复杂的冲突检测逻辑
            // 例如检查缓存、数据库锁等

            // 简单的时间窗口检测
            var cacheKey = $"processing:{barcode}";
            var isProcessing = await _resultHandler.SafeExecuteAsync(async () =>
            {
                // 模拟检查处理状态
                await Task.Delay(1);
                return false; // 暂时返回false，可以根据实际需求实现
            }, $"检查条码处理状态 - {barcode}", false);

            return isProcessing;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"检查条码处理状态失败 - {barcode}: {ex.Message}");
            return false; // 出错时假设未在处理
        }
    }
}
