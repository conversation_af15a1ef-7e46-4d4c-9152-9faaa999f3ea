﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.trade;

/// <summary>
/// 交易物流信息结构体.
/// </summary>
[SuppressSniffer]
public class DeliveryOrder
{
    /// <summary>
    /// 物流状态 0:待发货; 1:已发货.
    /// </summary>
    public int express_state { get; set; }

    /// <summary>
    /// 物流类型 0:手动发货; 1:系统自动发货.
    /// </summary>
    public int express_type { get; set; }

    /// <summary>
    /// 包裹id.
    /// </summary>
    public int pk_id { get; set; }
}
