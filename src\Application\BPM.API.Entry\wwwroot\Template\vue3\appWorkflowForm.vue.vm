@{int collapseNum = 0;}
<template>
	<view class="bpm-wrap bpm-wrap-form">
		<u-form :model="@(Model.FormModel)" :rules="@(Model.FormRules)" ref="@(Model.FormModel)" :errorType="['toast']" label-position="@(Model.LabelPosition)" :label-width="@(Model.LabelWidth) * 1.5" label-align="left" class="bpm-form">
			@{GenCodeAppFormControls();}
		</u-form>
	</view>
</template>
<script>
    import {getDictionaryDataSelector, getDataInterfaceRes } from '@@/api/common'
	import { useBaseStore } from '@@/store/modules/base'
@if(Model.IsDateSpecialAttribute || Model.IsTimeSpecialAttribute)
{
	@:import { getDateDay, getLaterData, getBeforeData, getBeforeTime, getLaterTime } from '@@/components/index.js'
}
	import comMixin from '../mixin'
    import request from '@@/utils/request'
    export default {
		mixins: [comMixin],
		name:"",
        data() {
            return {
                btnLoading: false,
				addTableConf:{
@foreach(var item in Model.FormList) {
@if(item.FooterBtnsList!=null){
@foreach (var btnItem in item.FooterBtnsList) {
	@if(btnItem.value!="add" && btnItem.value!="batchRemove")
	{
					@:@(item.Name)List@(btnItem.value) : @(btnItem.actionConfig)
	}
}
}
}
				},
                @(Model.FormModel): {
                    @(Model.PrimaryKey):'',
					flowId:'',
@foreach(var item in Model.FormList)
{
@switch(item.bpmKey)
{
case "barcode":
case "qrcode":
break;
case "checkbox":
case "cascader":
case "uploadImg":
case "uploadFile":
case "areaSelect":
case "organizeSelect":
@if(item.DefaultValue == null || item.DefaultValue == "")
{
					@:@(item.LowerName):[],
}else{
					@:@(item.LowerName):@(item.DefaultValue),
}
break;
case "select":
case "userSelect":
case "usersSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
@if(item.Multiple)
{
@if(item.DefaultValue == null || item.DefaultValue == "")
{
					@:@(item.LowerName):[],
}else{
					@:@(item.LowerName):@(item.DefaultValue),
}
}else{
@if(item.DefaultValue == null || item.DefaultValue == "")
{
					@:@(item.LowerName):undefined,
}else{
					@:@(item.LowerName):@(item.DefaultValue),
}
}
break;
case "inputNumber":
if(@item.DefaultValue == null || item.DefaultValue == "")
{
					@:@(item.LowerName):undefined,
}else{
					@:@(item.LowerName):@(item.DefaultValue),
}
break;
case "datePicker":
case "rate":
case "slider":
if(@item.DefaultValue == null || item.DefaultValue == "")
{
					@:@(item.LowerName):undefined,
}else{
					@:@(item.LowerName):@(item.DefaultValue),
}
break;
case "switch":
					@:@(item.LowerName):@(item.DefaultValue ? "1" : "0"),
break;
case "table":
					@:@(item.OriginalName):[],
break;
default:
if(@item.DefaultValue == null || item.DefaultValue == "")
{
					@:@(item.LowerName):undefined,
}else{
					@:@(item.LowerName):@(item.DefaultValue),
}
break;
}
}
                },
@if(Model.IsChildDataTransfer)
{
				@:addType: 1,
}else{
				@:addType: 0,
}
                rules: {
@foreach(var item in Model.FormList)
{
@switch(item.bpmKey)
{
case "table":
break;
default:
@if(item.Required || (item.RegList!=null && item.RegList.Count > 0))
{
					@:@(item.LowerName):[
@if(item.Required)
{
						@:{
							@:required:true,
							@:message:'请输入@(item.Placeholder)',
@if(item.Trigger.Contains("["))
{
							@:trigger:@(item.Trigger),
}
else
{
							@:trigger:'@(item.Trigger)',
}
switch(item.bpmKey)
{
case "checkbox":
case "cascader":
case "organizeSelect":
case "areaSelect":
case "uploadImg":
case "uploadFile":
							@:type:'array'
break;
case "select":
case "userSelect":
case "usersSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
@*是否为多选*@
@if(item.Multiple)
{
							@:type:'array'
}
break;
case "inputNumber":
case "switch":
case "datePicker":
case "rate":
case "slider":
							@:type:'number'
break;
}

						@:},
}
@if(item.RegList!=null && item.RegList.Count > 0)
{
@foreach(var items in item.RegList)
{
						@:{
							@:pattern:@(items.pattern),
							@:message:'@(items.message)',
if(item.Trigger.Contains("["))
{
							@:trigger:@(item.Trigger),
}
else
{
							@:trigger:'@(item.Trigger)',
}
						@:},
}
}
					@:],
}
break;
}
}
                },
@if(Model.IsDefaultFormControl)
{
				@:userInfo:{},
}
@if(Model.IsDateSpecialAttribute || Model.IsTimeSpecialAttribute)
{
				@:formatType: {
					@:"yyyy": "yyyy",
					@:"yyyy-MM": "yyyy-mm",
					@:"yyyy-MM-dd": "yyyy-mm-dd",
					@:"yyyy-MM-dd HH:mm": "yyyy-mm-dd hh:MM",
					@:"yyyy-MM-dd HH:mm:ss": "yyyy-mm-dd hh:MM:ss",
					@:"HH:mm:ss": "hh:MM:ss",
					@:"HH:mm": "hh:MM"
				@:},
}
				regList: {
@foreach(var item in Model.FormList)
{
@switch(item.bpmKey)
{
case "table":
@if(item.RegList != null && item.RegList.Count > 0)
{
					@:@(item.OriginalName): {
@foreach(var children in item.ChildrenList)
{
@{int childrenReg = 0;}
@if(children.RegList != null && children.RegList.Count > 0)
{
						@:@(children.LowerName): [@foreach(var reg in children.RegList){childrenReg++;@("{\"pattern\":" + reg.pattern + ", \"message\":\"" + reg.message + "\"}")@(childrenReg == children.RegList.Count ? "" : ",")}],
}
}
					@:},
}
break;
}
}
				},
@foreach(var item in Model.OptionsList)
{
@switch(item.bpmKey)
{
case "collapse":
break;
case "tab":
				@:@(item.Name)Title:@(item.Title),
				@:@(item.Name)Current:@(item.Content),
break;
case "autoComplete":
				@:@(item.Name)TemplateJson: @(item.TemplateJson == "" ? "[]" : item.TemplateJson),
break;
case "popupTableSelect":
case "popupSelect":
				@:@(item.Name)TemplateJson: @(item.TemplateJson == "" ? "[]" : item.TemplateJson),
				@:@(item.Content)
break;
default:
@if(item.IsProps)
{
				@:@(item.LowerName)Props:@(item.Props),
}
@if(!item.IsLinkage)
{
				@:@(item.Content)
}
break;
}
}
            };
        },
		watch: {
@if(Model.IsSummary)
{
			@:dataForm: {
				@:handler(val, oldVal) {
@foreach(var item in Model.FormList)
{
@switch(item.bpmKey)
{
case "table":
@if(item.ShowSummary)
{
					@:this.@(item.LowerName)();
}
break;
}
}
				@:},
				@:deep: true
			@:}				
}
        },
        created() {
@if(Model.IsDefaultFormControl)
{
			@:this.userInfo = uni.getStorageSync('userInfo') || {}
}
@foreach(var item in Model.FormList)
{
@switch(item.bpmKey)
{
case "table":
            @:uni.$on('linkPageConfirm', (subVal, Vmodel) => {
                @:if ('@(item.Name)' === Vmodel) subVal.forEach(t => this.@(Model.FormModel).@(item.OriginalName).push(t));
				@:this.initCollapse();
            @:});
break;
}
}
@if(Model.WebType == 1)
{
@if(Model.IsDefaultFormControl)
{
			@:this.initDefaultData();
}
}
			this.dataAll()
        },
        onReady() {
            this.$refs.@(Model.FormModel).setRules(this.rules);
        },
        methods: {
			initCollapse() {
				setTimeout(() => {
@for (int i = 0; i < collapseNum; i++)
{
					@:this.$refs.collapseRef@(i) && this.$refs.collapseRef@(i).init()
}
				}, 50)
			},
			onCollapseChange() {
				this.initCollapse()
			},
			clickIcon(label, tipLabel) {
				uni.showModal({
					title: label || '',
					content: tipLabel || '',
					showCancel: false,
				});
			},
@if(Model.IsDefaultFormControl)
{
    @:conversionDateTime(type) {
      @:const format = type === 'yyyy' ? 'yyyy-01-01 00:00:00' : type === 'yyyy-MM' ? 'yyyy-MM-01 00:00:00' :
        @:type === 'yyyy-MM-dd' ? 'yyyy-MM-dd 00:00:00' : type === 'yyyy-MM-dd HH:mm' ? 'yyyy-MM-dd HH:mm:00' : 'yyyy-MM-dd HH:mm:ss'
      @:const dataTime = this.bpm.toDate(new Date(), format)
      @:return new Date(dataTime).getTime()
    @:},
	@:initDefaultData() {
@if(Model.DefaultFormControlList.IsExistDate)
{
@foreach(var item in Model.DefaultFormControlList.DateField)
{
      @:this.dataForm.@(item.Field) = this.conversionDateTime("@(item.Format)");
}
}
@if(Model.DefaultFormControlList.IsExistTime)
{
@foreach(var item in Model.DefaultFormControlList.TimeField)
{
      @:this.dataForm.@(item.Field) = this.bpm.toDate(new Date(), "@(item.Format)");
}
}
@if(Model.DefaultFormControlList.IsSignField)
{
@foreach(var item in Model.DefaultFormControlList.SignField)
{
      @:this.dataForm.@(item.Field) = this.userInfo.signImg || '';
}
}
@if(Model.DefaultFormControlList.IsExistComSelect)
{
      @:if (this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
@foreach(var item in Model.DefaultFormControlList.ComSelectList)
{
        @:this.dataForm.@(item.Field) = @(item.IsMultiple? "[" : "")this.userInfo.organizeIdList@(item.IsMultiple? "]" : "")
}
	  @:}
}
@if(Model.DefaultFormControlList.IsExistUsersSelect)
{
@foreach(var item in Model.DefaultFormControlList.UsersSelectList)
{
@switch(item.selectType)
{
case "all":
      @:this.dataForm.@(item.Field) = @(item.IsMultiple? "[" : "")this.userInfo.userId@(item.IsMultiple? "]" : "")
break;
case "custom":
break;
}
}
}
@if(Model.DefaultFormControlList.IsExistUserSelect)
{
@foreach(var item in Model.DefaultFormControlList.UserSelectList)
{
@switch(item.selectType)
{
case "all":
      @:this.dataForm.@(item.Field) = @(item.IsMultiple? "[" : "")this.userInfo.userId@(item.IsMultiple? "]" : "")
break;
case "custom":
break;
}
}
}
@if(Model.DefaultFormControlList.IsExistRoleSelect)
{
@foreach(var item in Model.DefaultFormControlList.RoleSelectList)
{
@switch(item.selectType)
{
case "all":
      @:if(this.userInfo.roleId != null && this.userInfo.roleId != '') {
		@:this.dataForm.@(item.Field) = @(!item.IsMultiple? "this.userInfo.roleId" : "this.userInfo.roleIds")
      @:}
break;
case "custom":
break;
}
}
}
@if(Model.DefaultFormControlList.IsExistPosSelect)
{
@foreach(var item in Model.DefaultFormControlList.PosSelectList)
{
@switch(item.selectType)
{
case "all":
      @:if(this.userInfo.positionId != null && this.userInfo.positionId != '') {
		@:this.dataForm.@(item.Field) = @(!item.IsMultiple? "this.userInfo.positionId" : "this.userInfo.positionIds.map(o => o.id)")
      @:}
break;
case "custom":
break;
}
}
}
@if(Model.DefaultFormControlList.IsExistGroupsSelect)
{
@foreach(var item in Model.DefaultFormControlList.GroupsSelectList)
{
@switch(item.selectType)
{
case "all":
      @:if(this.userInfo.groupIds != null && this.userInfo.groupIds != []) {
		@:this.dataForm.@(item.Field) = @(!item.IsMultiple? "this.userInfo.groupIds[0]" : "this.userInfo.groupIds")
      @:}
break;
case "custom":
break;
}
}
}
@if(Model.DefaultFormControlList.IsExistDepSelect)
{
@foreach(var item in Model.DefaultFormControlList.DepSelectList)
{
@switch(item.selectType)
{
case "all":
      @:if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
        @:this.dataForm.@(item.Field) = @(item.IsMultiple? "[" : "")this.userInfo.departmentId@(item.IsMultiple? "]" : "")
      @:}
break;
case "custom":
break;
}
}
}
	@:},
}
@foreach(var item in @Model.OptionsList)
{
@if(item.bpmKey == "tab")
{
			@:@(item.LowerName)Change(index)
			@:{
				@:this.@(item.LowerName)Current = index;
				@:this.initCollapse()
			@:},
}
@if(!item.IsStatic && item.DictionaryType != null)
{
			@:get@(item.LowerName)Options(@(item.IsChildren ? "i" :"")){
switch(@item.DataType)
{
case "dictionary":
				@:getDictionaryDataSelector('@(item.DictionaryType)').then(res => {
					@:this.@(item.LowerName)Options = res.data.list
	break;
case "dynamic":
@if(item.IsChildren){
@if(item.IsLinkage){
				@:this.@(item.OptionsName)Options = []
}else{
				@:this.@(item.LowerName)Options = []
}
}else{
				@:this.@(item.LowerName)Options = []
}
				@:let templateJson = @(item.TemplateJson == "" ? "[]" : item.TemplateJson)
				@:let query = {
					@:paramList: this.getParamList(templateJson, this.dataForm@(item.IsChildren ? " , i" :""))
				@:}
				@:getDataInterfaceRes('@(item.DictionaryType)', query).then(res => {
					@:let data = res.data
@if(item.IsChildren){
@if(item.IsLinkage){
					@:this.@(item.OptionsName)Options = Array.isArray(data) ? data : []
}else{
					@:this.@(item.LowerName)Options = Array.isArray(data) ? data : []
}
}else{
					@:this.@(item.LowerName)Options = Array.isArray(data) ? data : []
}
	break;
}
				@:});
			@:},
}
}
			selfGetInfo(dataForm) {
				this.dataInfo(dataForm)
            },
@if(Model.IsDateSpecialAttribute)
{
	@:getRelationDate(timeRule, timeType, timeTarget, timeValueData, dataValue) {
	  @:let timeDataValue = null;
	  @:let timeValue = Number(timeValueData)
	  @:if (timeRule) {
		@:if (timeType == 1) {
		  @:timeDataValue = timeValue
		@:} else if (timeType == 2) {
		  @:timeDataValue = dataValue
		@:} else if (timeType == 3) {
		  @:timeDataValue = new Date().getTime()
		@:} else if (timeType == 4) {
		  @:let previousDate = '';
		  @:if (timeTarget == 1 || timeTarget == 2) {
			@:previousDate = getDateDay(timeTarget, timeType, timeValue)
			@:timeDataValue = new Date(previousDate).getTime()
		  @:} else if (timeTarget == 3) {
			@:previousDate = getBeforeData(timeValue)
			@:timeDataValue = new Date(previousDate).getTime()
		  @:} else {
			@:timeDataValue = getBeforeTime(timeTarget, timeValue).getTime()
		  @:}
		@:} else if (timeType == 5) {
		  @:let previousDate = '';
		  @:if (timeTarget == 1 || timeTarget == 2) {
			@:previousDate = getDateDay(timeTarget, timeType, timeValue)
			@:timeDataValue = new Date(previousDate).getTime()
		  @:} else if (timeTarget == 3) {
			@:previousDate = getLaterData(timeValue)
			@:timeDataValue = new Date(previousDate).getTime()
		  @:} else {
			@:timeDataValue = getLaterTime(timeTarget, timeValue).getTime()
		  @:}
		@:}
	  @:}
	  @:return timeDataValue;
	@:},
}
@if(Model.IsTimeSpecialAttribute)
{
	@:getRelationTime(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
	  @:let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType
	  @:let timeDataValue = null
	  @:if (timeRule) {
		@:if (timeType == 1) {
		  @:timeDataValue = timeValue || '00:00:00'
		  @:if (timeDataValue.split(':').length == 3) {
			@:timeDataValue = timeDataValue
		  @:} else {
			@:timeDataValue = timeDataValue + ':00'
		  @:}
		@:} else if (timeType == 2) {
		  @:timeDataValue = dataValue
		@:} else if (timeType == 3) {
		  @:timeDataValue = this.bpm.toDate(new Date(), format)
		@:} else if (timeType == 4) {
		  @:let previousDate = '';
		  @:previousDate = getBeforeTime(timeTarget, timeValue)
		  @:timeDataValue = this.bpm.toDate(previousDate, format)
		@:} else if (timeType == 5) {
		  @:let previousDate = '';
		  @:previousDate = getLaterTime(timeTarget, timeValue)
		  @:timeDataValue = this.bpm.toDate(previousDate, format)
		@:}
	  @:}
	  @:return timeDataValue;
	@:},
}
			dataAll() {
@foreach(var item in @Model.OptionsList)
{
@if(!item.IsStatic && !item.IsLinkage && (item.IsChildren || item.IsIndex))
{
				@:this.get@(item.LowerName)Options();
}
}
@if(Model.IsDefaultFormControl)
{
				@:this.initDefaultData();
}
			},
			beforeSubmit() {
				const _data = this.dataList()
				return _data
			},
			selfInit() {
				const baseStore = useBaseStore()
				baseStore.updateRelationData({})
			},
			dataList() {
				var _data = JSON.parse(JSON.stringify(this.dataForm));
				return _data;
			},
			dataInfo(dataAll) {
				let _dataAll = dataAll
				_dataAll.id = _dataAll.id
				this.dataForm = _dataAll
				this.isEdit = true
				this.childIndex = -1
			},
			getParamList(templateJson, formData, index) {
				for (let i = 0; i < templateJson.length; i++) {
					if (templateJson[i].relationField && templateJson[i].sourceType == 1) {
						//区分是否子表
						if (templateJson[i].relationField.includes('-')) {
							let tableVModel = templateJson[i].relationField.split('-')[0]
							let childVModel = templateJson[i].relationField.split('-')[1]
							templateJson[i].defaultValue = formData[tableVModel] && formData[tableVModel][index] &&
								formData[tableVModel][index][childVModel] || ''
						} else {
							templateJson[i].defaultValue = formData[templateJson[i].relationField] || ''
						}
					}
				}
				return templateJson
			},
			exist() {
				let title = []
				let _regList = this.regList
				for (let k in _regList) {
					let childData = this.dataForm[k]
					for (let n in _regList[k]) {
						for (let i = 0; i < _regList[k][n].length; i++) {
							const element = _regList[k][n][i]
							if (element.pattern) {
								element.pattern = element.pattern.toString()
								let start = element.pattern.indexOf('/')
								let stop = element.pattern.lastIndexOf('/')
								let str = element.pattern.substring(start + 1, stop)
								let reg = new RegExp(str)
								element.pattern = reg
							}
							childData.forEach((item, index) => {
								if (item[n] && !element.pattern.test(item[n])) {
									title.push(element.message)
								}
							})
						}
					}
				}
				if (title.length > 0) {
					return title[0]
				}
			},
			openSelectDialog(key,value) {
				let data = {
					actionConfig: this.addTableConf[key+'List'+value],
					formData: this.@(Model.FormModel),
					tableVmodel: key
				}
				uni.navigateTo({
					url: '/pages/apply/tableLinkage/index?data=' + encodeURIComponent(JSON.stringify(data))
				})
			},
@foreach(var item in Model.FormList)
{
@if(item.IsLinked && item.bpmKey != "table")
{
			@:@(item.LowerName)Change(){
@foreach(var linkage in item.LinkageRelationship)
{
@switch(linkage.bpmKey)
{
case "autoComplete":
case "popupSelect":
case "popupTableSelect":
break;
default:
@*主表联动子表控件*@
@if(linkage.isChildren)
{
				@:if(this.dataForm.@(linkage.fieldName).length){
					@:this.dataForm.@(linkage.fieldName).forEach((ele, index) => {
						@:this.get@(linkage.fieldName)_@(linkage.field)Options(index)
					@:})
				@:}
}else{
				@:this.dataForm.@(linkage.field) = @(linkage.IsMultiple ? "[]" : "undefined")
				@:this.get@(linkage.fieldName)Options()
}
break;
}
}
			@:},
}
@switch(item.bpmKey)
{
case "table":
@*子表内有控件联动*@
@if(item.IsLinked)
{
@foreach(var children in item.ChildrenList)
{
@*子表内具体某个控件*@
@if(children.IsLinked)
{
			@:@(children.LowerName)TableChange(i){
@foreach(var linkage in children.LinkageRelationship)
{
				@:this.dataForm.@(item.OriginalName)[i].@(linkage.field) = @(linkage.IsMultiple ? "[]" : "undefined");
@switch(linkage.bpmKey)
{
case "autoComplete":
case "popupSelect":
case "popupTableSelect":
break;
default:
				@:this.get@(item.OriginalName)_@(linkage.field)Options(i);
break;
}
}
			@:},
}
}
}
			@:add@(item.Name)Row() {
				@:let item = {
@foreach(var children in item.ChildrenList)
{
@switch(children.bpmKey)
{
case "checkbox":
case "cascader":
case "uploadImg":
case "uploadFile":
case "areaSelect":
case "organizeSelect":
@if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):[],
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
break;
case "select":
case "userSelect":
case "usersSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
@if(children.Multiple)
{
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):[],
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
}else{
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
}
break;
case "inputNumber":
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
break;
case "timePicker":
@*子表`time`默认值*@
if(Model.IsDefaultFormControl && Model.DefaultFormControlList.IsExistSubTable)
{
@foreach(var table in Model.DefaultFormControlList.SubTabelDefault)
{
@if(table.SubTableName.Equals(item.OriginalName))
{
@if(table.IsExistTime)
{
@foreach(var time in table.TimeField)
{
@if(time.Field.Equals(children.LowerName))
{
					@:@(children.LowerName):this.bpm.toDate(new Date(), "@(time.Format)"),
}
}
}else{
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
}
}
}
}else{
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
}
break;
case "datePicker":
@*子表`date`默认值*@
if(Model.IsDefaultFormControl && Model.DefaultFormControlList.IsExistSubTable)
{
@foreach(var table in Model.DefaultFormControlList.SubTabelDefault)
{
@if(table.SubTableName.Equals(item.OriginalName))
{
@if(table.IsExistDate)
{
@foreach(var date in table.DateField)
{
@if(date.Field.Equals(children.LowerName))
{
					@:@(children.LowerName):this.conversionDateTime("@(date.Format)"),
}
}
}else{
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
}
}
}
}else{
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
}
break;
case "rate":
case "slider":
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
break;
case "switch":
					@:@(children.LowerName):@(children.DefaultValue ? "1" : "0"),
break;
default:
if(children.DefaultValue == null || children.DefaultValue == "")
{
					@:@(children.LowerName):undefined,
}else{
					@:@(children.LowerName):@(children.DefaultValue),
}
break;
}
}
@foreach(var children in item.ChildrenList)
{
@if(children.IsLinkage)
{
@switch(children.bpmKey)
{
case "radio":
case "checkbox":
case "select":
case "cascader":
case "treeSelect":
case "popupTableSelect":
case "popupSelect":
					@:@(children.LowerName)Options:[],
break;
}
}
}
				@:}
				@:this.@(Model.FormModel).@(item.OriginalName).push(item)
@*子表组织、部门、用户、手写签名默认值*@
if(Model.IsDefaultFormControl && Model.DefaultFormControlList.IsExistSubTable)
{
@foreach(var table in Model.DefaultFormControlList.SubTabelDefault)
{
@if(table.IsSignField && table.SubTableName.Contains(item.OriginalName))
{
			@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1];
@foreach(var column in table.SignField)
{
			@:lastItem.@(column.Field) = this.userInfo.signImg || '';
}
			@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
}
@if(table.IsExistComSelect && table.SubTableName.Contains(item.OriginalName))
{
		  @:if (this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
			@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
@foreach(var column in table.ComSelectList)
{
			@:lastItem.@(column.Field) = @(column.IsMultiple? "[" : "")this.userInfo.organizeIdList@(column.IsMultiple? "]" : "")
}
			@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
	  @:}
}
@if(table.IsExistDepSelect && table.SubTableName.Contains(item.OriginalName))
{
@foreach(var column in table.DepSelectList)
{
@switch(column.selectType)
{
case "all":
      @:if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
		@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
        @:lastItem.@(column.Field) = @(column.IsMultiple? "[" : "")this.userInfo.departmentId@(column.IsMultiple? "]" : "")
		@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
      @:}
break;
case "custom":
break;
}
}
}

@if(table.IsExistRoleSelect && table.SubTableName.Contains(item.OriginalName))
{
@foreach(var column in table.RoleSelectList)
{
@switch(column.selectType)
{
case "all":
      @:if(this.userInfo.roleId != null && this.userInfo.roleId != '') {
		@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
        @:lastItem.@(column.Field) = @(!column.IsMultiple? "this.userInfo.roleId" : "this.userInfo.roleIds")
		@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
      @:}
break;
case "custom":
break;
}
}
}
@if(table.IsExistPosSelect && table.SubTableName.Contains(item.OriginalName))
{
@foreach(var column in table.PosSelectList)
{
@switch(column.selectType)
{
case "all":
      @:if(this.userInfo.positionId != null && this.userInfo.positionId != '') {
		@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
        @:lastItem.@(column.Field) = @(!column.IsMultiple? "this.userInfo.positionId" : "this.userInfo.positionIds.map(o => o.id)")
		@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
      @:}
break;
case "custom":
break;
}
}
}
@if(table.IsExistGroupsSelect && table.SubTableName.Contains(item.OriginalName))
{
@foreach(var column in table.GroupsSelectList)
{
@switch(column.selectType)
{
case "all":
      @:if(this.userInfo.groupIds != null && this.userInfo.groupIds != []) {
		@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
        @:lastItem.@(column.Field) = @(!column.IsMultiple? "this.userInfo.groupIds[0]" : "this.userInfo.groupIds")
		@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
      @:}
break;
case "custom":
break;
}
}
}
@if(table.IsExistUsersSelect && table.SubTableName.Contains(item.OriginalName))
{
@foreach(var column in table.UsersSelectList)
{
@switch(column.selectType)
{
case "all":
	  @:if(this.userInfo.userId != null && this.userInfo.userId != '')
	  @:{
		@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
		@:lastItem.@(column.Field) = @(column.IsMultiple? "[" : "")this.userInfo.userId@(column.IsMultiple? "]" : "")
		@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
	  @:}
break;
case "custom":
break;
}
}
}
@if(table.IsExistUserSelect && table.SubTableName.Contains(item.OriginalName))
{
@foreach(var column in table.UserSelectList)
{
@switch(column.selectType)
{
case "all":
	  @:if(this.userInfo.userId != null && this.userInfo.userId != '')
	  @:{
		@:let lastItem = this.dataForm.@(table.SubTableName)[this.dataForm.@(table.SubTableName).length-1]
		@:lastItem.@(column.Field) = @(column.IsMultiple? "[" : "")this.userInfo.userId@(column.IsMultiple? "]" : "")
		@:this.$set(this.dataForm.@(table.SubTableName), this.dataForm.@(table.SubTableName).length-1, lastItem)
	  @:}
break;
case "custom":
break;
}
}
}
}
}
@*循环出主表联动子表控件的事件*@
@foreach(var primaryTableLinkage in Model.FormList)
{
@if(primaryTableLinkage.IsLinked && primaryTableLinkage.bpmKey != "table")
{
@foreach(var linkage in primaryTableLinkage.LinkageRelationship)
{
if(linkage.fieldName == item.OriginalName)
{
@switch(linkage.isChildren)
{
case true:
@switch(linkage.bpmKey)
{
case "autoComplete":
case "popupSelect":
case "popupTableSelect":
break;
default:
				@:this.get@(linkage.fieldName)_@(linkage.field)Options(this.dataForm.@(linkage.fieldName).length - 1)
break;
}
break;
}
}
}
}
@if(primaryTableLinkage.bpmKey == "table" && primaryTableLinkage.IsLinked)
{
@*子表内有控件联动*@
@foreach(var children in primaryTableLinkage.ChildrenList)
{
@*子表内具体某个控件*@
@if(children.IsLinked)
{
@foreach(var linkage in children.LinkageRelationship)
{
@switch(linkage.bpmKey)
{
case "popupSelect":
case "popupTableSelect":
case "autoComplete":
break;
default:
				@:this.get@(linkage.fieldName)_@(linkage.field)Options(this.dataForm.@(linkage.fieldName).length - 1)
break;
}
}
}
}
}
}
				@:this.initCollapse()

			@:},
			@:remove@(item.Name)Row(i,showConfirm=false) {
				@:const handleRemove = () => {
					@:this.@(Model.FormModel).@(item.OriginalName).splice(i, 1)
					@:this.initCollapse()
				@:};
				@:if (!showConfirm) return handleRemove();
				@:uni.showModal({
					@:title: '提示',
					@:content: '确认删除该条信息吗？',
					@:success: (res) => {
						@:if (res.confirm) handleRemove()
					@:}
				@:})
			@:},
			@:copy@(item.Name)Row(i) {
				@:let item = JSON.parse(JSON.stringify(this.@(Model.FormModel).@(item.OriginalName)[i]));
				@:item.@(item.PrimaryKey) = '';
				@:item.length && item.map(o => delete o.rowData);
				@:this.@(Model.FormModel).@(item.OriginalName).push(item);
			@:},
@if(item.ShowSummary)
{
			@:@(item.LowerName)(){
				 @:let table = this.@(Model.FormModel).@(item.OriginalName)
				 @:let summaryField = @(item.SummaryField)
				 @:let thousandsField = @(item.Thousands ? item.ChildrenThousandsField : "[]")
                 @:let summaryFieldName = {
@foreach(var items in item.ChildrenList)
{
@if(items.IsSummary)
{
					@:"@(items.Name)":"@(items.Placeholder)",
}
}
                 @:}
				 @:let data = {}
                 @:for (let i in summaryField) {
					@:let map = {}
					@:let val = 0
					@:for (let j = 0; j < table.length; j++) {
						@:let summary = table[j][summaryField[i]];
						@:if (summary) {
							@:let data = isNaN(summary) ? 0 : Number(summary)
							@:val += data
						@:}
					@:}
					@:map.id = summaryField[i];
					@:map.name = summaryFieldName[summaryField[i]];
					@:map.val = thousandsField.includes(summaryField[i]) ? Number(val).toLocaleString('zh', {
						@:maximumFractionDigits: '2',
						@:minimumFractionDigits: '2'
					@:}) : Number(val).toFixed(2);
					@:data[summaryField[i]] = map;
				@:}
				@:return data;
			@:},
}
break;
}
}
        }
    };
</script>
<style>
        page{
                background-color: #f0f2f6;
        }
</style>
@{
    void GenCodeAppFormControls()
	{
@foreach(var item in Model.FormAllContols)
{
@switch(item.bpmKey)
{
case "table":
			@:<view class="bpm-table">
				@:<view class="bpm-table-title u-line-1" @@click="clickIcon('@(item.Label)','@(item.TipLabel)')">
					@(item.Label)
@if(item.TipLabel != null)
{
					@:<u-icon :name="'question-circle-fill'" class="u-m-l-10" color="#a0acb7" />
}
				@:</view>
				@:<view class="bpm-table-item" v-for="(item,i) in @(Model.FormModel).@(item.Name)" :key="i">
					@:<view class="bpm-table-item-title">
						@:<view class="bpm-table-item-title-num">({{i+1}})</view>
						@:<view class="u-flex" v-if="!setting.readonly && !judgeWrite('@(item.Name)')">
@foreach(var btnItem in item.ColumnBtnsList)
{
    @if(btnItem.show)
	{
        switch(btnItem.value)
	    {
	        case "remove":
		        @:<view class="bpm-table-delete-btn" @@click="remove@(item.ChildTableName)Row(i,@(btnItem.showConfirm))">@(btnItem.label)</view>
	        break;
	        case "copy":
		        @:<view class="bpm-table-copy-btn" @@click="copy@(item.ChildTableName)Row(i)">@(btnItem.label)</view>
	        break;
	    }
	}
}
						@:</view>
                    @:</view>
@foreach(var children in item.Children)
{
					@:<view class="u-p-l-20 u-p-r-20 form-item-box">
@switch(children.bpmKey)
{
case "relationFormAttr":
case "popupAttr":
						@:<u-form-item @(children.ShowLabel ? "label=\"" + children.Label + "\" " : "")@(children.NoShow ? "v-if='false' " : (children.IsStorage == 1 ? "v-if=\"judgeShow('" + item.Name + "-" + children.LowerName + "')\" " : ""))@(children.ShowLabel && (children.Label != null && children.TipLabel != null) ? "left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + children.Label + "','" + children.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : ""):required="requiredList['@(item.Name + "-" + @children.LowerName)']">
break;
default:
						@:<u-form-item @(children.ShowLabel ? "label=\"" + children.Label + "\" " : "")@(children.NoShow ? "v-if='false' " : "v-if=\"judgeShow('" + item.Name + "-" + children.LowerName + "')\" ")@(children.ShowLabel && (children.Label != null && children.TipLabel != null) ? "left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + children.Label + "','" + children.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : ""):required="requiredList['@(item.Name + "-" + @children.LowerName)']">
break;
}
@switch(children.bpmKey)
{
case "uploadFile":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.TipText)@(children.IsDisabled)@(children.Limit)@(children.SizeUnit)@(children.FileSize)@(children.Accept)@(children.PathType)@(children.IsAccount)@(children.Folder)/>
break;
case "uploadImg":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.TipText)@(children.IsDisabled)@(children.SizeUnit)@(children.FileSize)@(children.PathType)@(children.IsAccount)@(children.Folder)/>
break;
case "organizeSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "areaSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Level)@(children.Multiple)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "billRule":
							@:<BpmInput v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" placeholder="系统自动生成" disabled/>
break;
case "treeSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" :options="@(children.IsLinkage ? "dataForm." + item.Name + "[i]." + children.LowerName + "Options" : item.Name + "_" + children.LowerName + "Options")" :props="@(item.Name + "_" + @children.LowerName)Props" @(children.Filterable)@(children.IsDisabled)@(children.Placeholder)@(children.Multiple)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "userSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.UserRelationAttr)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "usersSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "posSelect":
case "depSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Multiple)@(children.Placeholder)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
@*分组选择*@
case "groupSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
@*角色选择*@
case "roleSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "select":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder):options="@(children.IsLinkage ? "dataForm." + item.Name + "[i]." + children.LowerName + "Options" : item.Name + "_" + children.LowerName + "Options")" :props="@(item.Name + "_" + children.LowerName)Props" @(children.Filterable)@(children.Multiple)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "cascader":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder):options="@(children.IsLinkage ? "dataForm." + item.Name + "[i]." + children.LowerName + "Options" : item.Name + "_" + children.LowerName + "Options")" :props="@(item.Name + "_" + children.LowerName)Props" @(children.Multiple)@(children.IsDisabled)@(children.Filterable)@(children.ShowAllLevels)@(children.Clearable)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "rate":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" size="40" @(children.Count!=null && children.Count!="" ? ":max='"+children.Count+"'" : "") @(children.Readonly)@(children.AllowHalf)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"Change'":"")/>
break;
case "slider":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Step)@(children.Min)@(children.Max)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"Change' ":"")style="width: 100%;" />
break;
case "inputNumber":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Precision)@(children.ControlsPosition)@(children.AddonBefore)@(children.AddonAfter)@(children.Thousands)@(children.AmountChinese)@(children.Max)@(children.Min)@(children.Step)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "sign":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled) />
break;
case "signature":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Disaabled)@(children.AbleIds)@(children.Disabled)/>
break;
case "location":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.EnableLocationScope)@(children.AutoLocation)@(children.AdjustmentScope)@(children.EnableDesktopLocation)@(children.LocationScope)@(children.IsDisabled) />
break;
case "datePicker":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Type!="" ? children.Type : "type='date'") @(children.Format)@(children.StartTime)@(children.EndTime)@(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "timePicker":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Type!="" ? children.Type : "type='time'") @(children.Format)@(children.StartTime)@(children.EndTime)@(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "switch":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "input":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@blur='"+ @children.LowerName +"TableChange(i)' ":"")@(children.UseScan)@(children.AddonBefore)@(children.AddonAfter)@(children.ShowCount)/>
break;
@*关联表单*@
case "relationForm":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled)@(children.RelationField)@(children.ColumnOptions)@(children.ModelId != "" ? "modelId='"+ children.ModelId +"' " :"")@(children.Placeholder)@(children.HasPage)@(children.PageSize)@(children.TemplateJson)@(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name)-@(children.Name)'+i" type="relation"/>
break;
@*关联表单属性*@
case "relationFormAttr":
							@:<@(children.Tag) showField="@(children.ShowField)" @(children.IsStorage == 1 ? "v-model='" + Model.FormModel + "." + item.Name + "[i]." + children.LowerName + "'" : "") :relationField="'@(item.Name)-@(children.RelationField)'+i" :isStorage="@(children.IsStorage)" type='relationFormAttr'/>
break;
@*关联表单属性*@
case "popupSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled)@(children.PropsValue)@(children.RelationField)@(children.ColumnOptions)@(children.InterfaceId)@(children.Placeholder)@(children.HasPage)@(children.PageSize)@(children.PopupType)@(children.PopupTitle)@(children.PopupWidth)@(children.TemplateJson)@(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name)-@(children.Name)'+i" type="popup"/>
break;
@*弹窗选择属性*@
case "popupAttr":
							@:<@(children.Tag) showField="@(children.ShowField)" @(children.IsStorage == 1 ? "v-model='" + Model.FormModel + "." + item.Name + "[i]." + children.LowerName + "'" : "") :relationField="'@(item.Name)-@(children.RelationField)'+i" :isStorage="@(children.IsStorage)" type='popupAttr'/>
break;
case "textarea":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.MaxLength)@(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")@(children.ShowCount)/>
break;
case "popupTableSelect":
							@:<BpmPopupSelect v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.InterfaceId)@(children.Placeholder)@(children.IsDisabled)@(children.ColumnOptions)@(children.RelationField)@(children.PropsValue)@(children.HasPage)@(children.PageSize)@(children.PopupTitle)@(children.Multiple)@(children.Filterable)@(children.TemplateJson)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name + "-" +@children.LowerName)'+i" @(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")/>
break;
case "autoComplete":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Total)@(children.InterfaceId)@(children.Placeholder)@(children.IsDisabled)@(children.Clearable)@(children.RelationField)@(children.TemplateJson)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name + "-" +@children.LowerName)'+i" @(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")/>
break;
}
						@:</u-form-item>
					@:</view>
}
                @:</view>
@if(item.FooterBtnsList.Count>0)
{		
				@:<view class="bpm-table-footer-btn" v-if="!setting.readonly && !judgeWrite('@(item.Name)')">
@foreach(var btnItem in item.FooterBtnsList)
{
        @if(btnItem.value!="batchRemove")
		{
					@:<view class="bpm-table-btn bpm-table-@(btnItem.btnType)-btn" @@click="@(btnItem.value=="add" ? "add"+item.ChildTableName+"Row()" : "openSelectDialog('"+item.ChildTableName+"','"+btnItem.value+"')")">
						@:<text class="bpm-table-btn-icon @(btnItem.btnIcon)"/>
						@:<text class="bpm-table-btn-text">@(btnItem.label)</text>
					@:</view>
		}
}
				@:</view>
}
@if(item.ShowSummary)
{
				@:<view class="bpm-table-item">
					@:<view class="bpm-table-item-title u-flex u-row-between">
						@:<text class="bpm-table-item-title-num">@(item.Label)合计</text>
					@:</view>
					@:<view class="u-p-l-20 u-p-r-20 form-item-box">
						@:<u-form-item v-for="(item,i) in @(item.LowerChildTableName)()" :key="i" :label="item.name">
							@:<BpmInput v-model="item.val" disabled placeholder="" />
						@:</u-form-item>
					@:</view>
				@:</view>
}
			@:</view>
break;
case "tableGrid":
@{GenCodeAppFormChildrenControls(item.Children);}
break;
case "tableGridTr":
@{GenCodeAppFormChildrenControls(item.Children);}
break;
case "tableGridTd":
@{GenCodeAppFormChildrenControls(item.Children);}
break;
case "groupTitle":
			@:<@(item.Tag) content="@(item.Content)" content-position="@(item.Contentposition)" @(item.TipLabel != null ? "helpMessage=\"" + item.TipLabel + "\" @groupIcon=\"clickIcon('" + item.Content + "','" + item.TipLabel + "')\" " : "")/>
break;
case "divider":
			@:<@(item.Tag) content="@(item.Default)" half-width="200" height="80"/>
break;
case "card":
case "row":
			@:<view class="bpm-card">
				@:<view class="bpm-card-cap u-line-1">@(item.Content)
@if(item.TipLabel != null)
{
					@:<u-icon name="question-circle-fill" class="u-m-l-10" color="#a0acb7"
						@:@@click="clickIcon('@(item.Content)','@(item.TipLabel)')" />
}
				@:</view>
@{GenCodeAppFormChildrenControls(item.Children);}
			@:</view>
break;
case "tab":
			@:<view prop="@(item.LowerName)">
				@:<u-tabs :is-scroll="false" :list="@(item.LowerName)Title" name="title" v-model="@(item.LowerName)Current" @@change="@(item.LowerName)Change"></u-tabs>
				@:<view>
@{ int n = 0;}
@foreach(var tab in item.Children)
{
					@:<view v-show="@(n) == @(item.LowerName)Current">
@{GenCodeAppFormChildrenControls(tab.Children);}
					@:</view>
					@{n++;}
}
				@:</view>
			@:</view>
break;
case "collapse":
			@:<view class="collapse">
				@:<u-collapse :accordion="@(item.Accordion)" ref="collapseRef@(collapseNum)">
@{collapseNum++;}
@foreach(var collapse in item.Children)
{
					@:<u-collapse-item class="collapse-item" name="@(collapse.Name)" title="@(collapse.Title)" @(collapse.Open) @@change="onCollapseChange">
@{GenCodeAppFormChildrenControls(collapse.Children);}
					@:</u-collapse-item>
}
				@:</u-collapse>
			@:</view>
break;
case "text":
			@:<@(item.Tag) content="@(item.Content)" :textStyle='@(item.TextStyle)'/>
break;
default:
			@:<view class="u-p-l-20 u-p-r-20 form-item-box">
@switch(item.bpmKey)
{
@*弹窗选择属性*@
case "popupAttr":
case "relationFormAttr":
				@:<u-form-item @(item.ShowLabel ? "label='"+item.Label+"' " : "")prop="@(item.RelationField)" @(item.NoShow ? "v-if='false' " : (item.IsStorage == 1 ? "v-if=\"judgeShow('" + item.OriginalName + "')\" " : ""))@(item.IsRequired)@(item.ShowLabel && (item.Label != null && item.TipLabel != null) ? " left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + item.Label + "','" + item.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : "")>
break;
case "button":
case "alert":
case "link":
				@:<u-form-item>
break;
case "editor":
				@:<u-form-item prop="@(item.LowerName)">
break;
case "input":
case "textarea":
case "inputNumber":
case "switch":
case "radio":
case "checkbox":
case "select":
case "cascader":
case "areaSelect":
case "treeSelect":
case "uploadImg":
case "uploadFile":
case "rate":
case "slider":
case "timePicker":
case "datePicker":
case "organizeSelect":
case "depSelect":
case "posSelect":
case "userSelect":
case "usersSelect":
case "groupSelect":
case "roleSelect":
case "relationForm":
case "popupSelect":
case "popupTableSelect":
case "colorPicker":
case "createUser":
case "createTime":
case "currOrganize":
case "currPosition":
case "autoComplete":
case "billRule":
case "sign":
case "signature":
case "location":
				@:<u-form-item @(item.ShowLabel ? "label='"+item.Label+"' " : "")prop="@(item.LowerName)" @(item.IsRequired)@(item.NoShow ? "v-if='false' " : "v-if=\"judgeShow('" + item.OriginalName + "')\" ")@(item.Label != null && item.TipLabel != null ? " left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + item.Label + "','" + item.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : "")>
break;
default:
				@:<u-form-item @(item.ShowLabel ? "label='"+item.Label+"' " : "")prop="@(item.LowerName)" @(item.NoShow ? "v-if='false' " : "v-if=\"judgeShow('" + item.OriginalName + "')\" ")>
break;
}
@switch(item.bpmKey)
{
@*弹窗选择属性*@
case "popupAttr":
case "relationFormAttr":
					@:<@(item.Tag) showField="@(item.ShowField)" relationField="@(item.RelationField)" :isStorage="@(item.IsStorage)" @(item.vModel) type='@(item.bpmKey)' @(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "colorPicker":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.ColorFormat)@(item.IsDisabled)/>
break;
case "editor":
					@:<@(item.Tag) @(item.vModel)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "button":
					@:<@(item.Tag) buttonText="@(item.ButtonText)" align="@(item.Align)" type="@(item.Type)"/>
break;
case "alert":
					@:<@(item.Tag) type="@(item.Type)" title="@(item.Title)" tagIcon='icon-ym icon-ym-generator-alert' :showIcon="@(item.ShowIcon)" :closable="@(item.Closable.ToString().ToLower())" description="@(item.Description)" closeText="@(item.CloseText)"/>
break;
case "link":
					@:<@(item.Tag) content="@(item.Content)" href="@(item.Href)" target='@(item.Target)' :textStyle='@(item.TextStyle)' />
break;
case "input":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.MaxLength)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@blur='"+ @item.LowerName +"Change'":"")@(item.UseScan)@(item.AddonBefore)@(item.AddonAfter)@(item.ShowCount)/>
break;
case "textarea":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.MaxLength)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")@(item.ShowCount)/>
break;
case "inputNumber":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Precision)@(item.ControlsPosition)@(item.AddonBefore)@(item.AddonAfter)@(item.Thousands)@(item.AmountChinese)@(item.Max)@(item.Min)@(item.Step)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "sign":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.IsDisabled) />
break;
case "signature":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Disaabled)@(item.AbleIds)@(item.Disabled)/>
break;
case "location":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.EnableLocationScope)@(item.AutoLocation)@(item.AdjustmentScope)@(item.EnableDesktopLocation)@(item.LocationScope)@(item.IsDisabled) />
break;
case "switch":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Direction)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>

break;
case "radio":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Direction)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "checkbox":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Direction)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "select":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Filterable)@(item.Placeholder)@(item.Multiple)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "cascader":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.Filterable)@(item.ShowAllLevels)@(item.Clearable)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "areaSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Level)@(item.Placeholder)@(item.IsDisabled)@(item.Multiple)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "treeSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Filterable)@(item.Placeholder)@(item.Multiple)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "uploadImg":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.TipText)@(item.IsDisabled)@(item.SizeUnit)@(item.FileSize)@(item.PathType)@(item.IsAccount)@(item.Folder)/>
break;
case "uploadFile":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.TipText)@(item.IsDisabled)@(item.Limit)@(item.SizeUnit)@(item.FileSize)@(item.Accept)@(item.PathType)@(item.IsAccount)@(item.Folder)/>
break;
case "rate":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" size="40" @(item.Count!=null && item.Count!="" ? ":max='"+item.Count+"'" : "") @(item.Readonly)@(item.AllowHalf)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "slider":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Step)@(item.Min)@(item.Max)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")style="width: 100%;" />
break;
case "timePicker":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Type!="" ? item.Type : "type='time'") @(item.Format)@(item.StartTime)@(item.EndTime)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "datePicker":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Type!="" ? item.Type : "type='date'") @(item.Format)@(item.StartTime)@(item.EndTime)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "organizeSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "depSelect":
case "posSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change'":"")/>
break;
case "userSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.UserRelationAttr)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "usersSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "groupSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "roleSelect":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "barcode":
					@:<@(item.Tag) @(item.Width) @(item.Height) @(item.Format) @(item.LineColor) @(item.Background) @(item.StaticText) @(item.DataType) @(item.RelationField) :formData="dataForm" />
break;
case "qrcode":
					@:<@(item.Tag) @(item.Width) @(item.ColorDark) @(item.ColorLight) @(item.StaticText) @(item.DataType) @(item.RelationField) :formData="dataForm" />
break;
@*关联表单*@
case "relationForm":
					@:<@(item.Tag) @(item.vModel)@(item.IsDisabled)@(item.RelationField)@(item.ColumnOptions)@(item.ModelId != "" ? "modelId='"+ item.ModelId +"' " :"")@(item.Placeholder)@(item.HasPage)@(item.PageSize)@(item.TemplateJson)vModel="@(item.Name)" type="relation" @(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")@(item.IsLinkage ? ":formData='dataForm' ": "")/>
break;
case "popupSelect":
					@:<@(item.Tag) @(item.vModel)@(item.IsDisabled)@(item.PropsValue)@(item.RelationField)@(item.ColumnOptions)@(item.InterfaceId)@(item.Placeholder)@(item.HasPage)@(item.PageSize)@(item.PopupType)@(item.PopupTitle)@(item.PopupWidth)@(item.TemplateJson)vModel="@(item.Name)" @(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"") @(item.IsLinkage ? ":formData='dataForm' ": "")type="popup"/>
break;
case "popupTableSelect":
					@:<BpmPopupSelect @(item.vModel)@(item.InterfaceId)@(item.Placeholder)@(item.IsDisabled)@(item.ColumnOptions)@(item.RelationField)@(item.PropsValue)@(item.HasPage)@(item.PageSize)@(item.PopupTitle)@(item.Multiple)@(item.Filterable)@(item.TemplateJson)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":""):vModel="'@(item.LowerName)'"  @(item.IsLinkage ? ":formData='dataForm' ": "")/>
break;
case "createUser":
case "createTime":
case "currOrganize":
case "currPosition":
					@:<BpmOpenData v-model="@(Model.FormModel).@(item.LowerName)" @(item.Type) disabled/>
break;
case "autoComplete":
					@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" vModel="@(item.Name)" @(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")@(item.IsLinkage ? ":formData='dataForm' ": "")@(item.IsDisabled)@(item.TemplateJson)@(item.Total)@(item.InterfaceId)@(item.Placeholder)@(item.Clearable)@(item.RelationField)/>
break;
default:
					@:<BpmInput v-model="@(Model.FormModel).@(item.LowerName)" placeholder="系统自动生成" disabled/>
break;
}
				@:</u-form-item>
			@:</view>
break;
}
}
	}
	void GenCodeAppFormChildrenControls(ICollection<FormControlDesignModel> childrenList)
	{
@foreach(var item in childrenList)
{
@switch(item.bpmKey)
{
case "table":
				@:<view class="bpm-table">
				@:<view class="bpm-table-title u-line-1" @@click="clickIcon('@(item.Label)','@(item.TipLabel)')">
					@(item.Label)
@if(item.TipLabel != null)
{
					@:<u-icon :name="'question-circle-fill'" class="u-m-l-10" color="#a0acb7" />
}
				@:</view>
				@:<view class="bpm-table-item" v-for="(item,i) in @(Model.FormModel).@(item.Name)" :key="i">
					@:<view class="bpm-table-item-title">
						@:<view class="bpm-table-item-title-num">({{i+1}})</view>
						@:<view class="u-flex" v-if="!setting.readonly && !judgeWrite('@(item.Name)')">
@foreach(var btnItem in item.ColumnBtnsList)
{
    @if(btnItem.show)
	{
        switch(btnItem.value)
	    {
	        case "remove":
		        @:<view class="bpm-table-delete-btn" @@click="remove@(item.ChildTableName)Row(i,@(btnItem.showConfirm))">@(btnItem.label)</view>
	        break;
	        case "copy":
		        @:<view class="bpm-table-copy-btn" @@click="copy@(item.ChildTableName)Row(i)">@(btnItem.label)</view>
	        break;
	    }
	}
}
						@:</view>
					@:</view>
@foreach(var children in item.Children)
{
						@:<view class="u-p-l-20 u-p-r-20 form-item-box">
@switch(children.bpmKey)
{
case "relationFormAttr":
case "popupAttr":
							@:<u-form-item @(children.ShowLabel ? "label=\"" + children.Label + "\" " : "")@(children.NoShow ? "v-if='false' " : (children.IsStorage == 1 ? "v-if=\"judgeShow('" + item.Name + "-" + children.LowerName + "')\" " : "")):required="requiredList['@(item.Name + "-" + @children.LowerName)']" @(children.ShowLabel && (children.Label != null && children.TipLabel != null) ? "left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + children.Label + "','" + children.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\"" : "")>
break;
default:
							@:<u-form-item @(children.ShowLabel ? "label=\"" + children.Label + "\" " : "")@(children.NoShow ? "v-if='false' " : "v-if=\"judgeShow('" + item.Name + "-" + children.LowerName + "')\" "):required="requiredList['@(item.Name + "-" + @children.LowerName)']" @(children.ShowLabel && (children.Label != null && children.TipLabel != null) ? "left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + children.Label + "','" + children.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : "")>
break;
}
@switch(children.bpmKey)
{
case "uploadFile":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.TipText)@(children.IsDisabled)@(children.Limit)@(children.SizeUnit)@(children.FileSize)@(children.Accept)@(children.PathType)@(children.IsAccount)@(children.Folder)/>
break;
case "uploadImg":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.TipText)@(children.IsDisabled)@(children.SizeUnit)@(children.FileSize)@(children.PathType)@(children.IsAccount)@(children.Folder)/>
break;
case "organizeSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "areaSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Level)@(children.Multiple)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "billRule":
							@:<BpmInput v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" placeholder="系统自动生成" disabled/>
break;
case "treeSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" :options="@(children.IsLinkage ? "dataForm." + item.Name + "[i]." + children.LowerName + "Options" : item.Name + "_" + children.LowerName + "Options")" :props="@(item.Name + "_" + @children.LowerName)Props" @(children.Filterable)@(children.IsDisabled)@(children.Placeholder)@(children.Multiple)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "userSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.UserRelationAttr)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "usersSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "posSelect":
case "depSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Multiple)@(children.Placeholder)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
@*分组选择*@
case "groupSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
@*角色选择*@
case "roleSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.Multiple)@(children.IsDisabled)@(children.SelectType != "" ? "selectType='" + children.SelectType + "' " : "")@(children.IsCustomSelect ? @children.AbleIds : "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "select":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder):options="@(children.IsLinkage ? "dataForm." + item.Name + "[i]." + children.LowerName + "Options" : item.Name + "_" + children.LowerName + "Options")" :props="@(item.Name + "_" + @children.LowerName)Props" @(children.Filterable)@(children.Multiple)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "cascader":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder):options="@(children.IsLinkage ? "dataForm." + item.Name + "[i]." + children.LowerName + "Options" : item.Name + "_" + children.LowerName + "Options")" :props="@(item.Name + "_" + @children.LowerName)Props" @(children.Multiple)@(children.IsDisabled)@(children.Filterable)@(children.ShowAllLevels)@(children.Clearable)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "rate":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" size="40" @(children.Count!=null && children.Count!="" ? ":max='"+children.Count+"'" : "") @(children.Readonly)@(children.AllowHalf)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"Change'":"")/>
break;
case "slider":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Step)@(children.Min)@(children.Max)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"Change' ":"")style="width: 100%;" />
break;
case "inputNumber":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Precision)@(children.ControlsPosition)@(children.AddonBefore)@(children.AddonAfter)@(children.Thousands)@(children.AmountChinese)@(children.Max)@(children.Min)@(children.Step)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "sign":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled) />
break;
case "signature":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Disaabled)@(children.AbleIds)@(children.Disabled)/>
break;
case "location":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.EnableLocationScope)@(children.AutoLocation)@(children.AdjustmentScope)@(children.EnableDesktopLocation)@(children.LocationScope)@(children.IsDisabled) />
break;
case "datePicker":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Type!="" ? children.Type : "type='date'") @(children.Format)@(children.StartTime)@(children.EndTime)@(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "timePicker":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Type!="" ? children.Type : "type='time'") @(children.Format)@(children.StartTime)@(children.EndTime)@(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "switch":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")/>
break;
case "input":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@blur='"+ @children.LowerName +"TableChange(i)' ":"")@(children.UseScan)@(children.AddonBefore)@(children.AddonAfter)@(children.ShowCount)/>
break;
@*关联表单*@
case "relationForm":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled)@(children.RelationField)@(children.ColumnOptions)@(children.ModelId != "" ? "modelId='"+ children.ModelId +"' " :"")@(children.Placeholder)@(children.HasPage)@(children.PageSize)@(children.TemplateJson)@(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name)-@(children.Name)'+i" type="relation"/>
break;
@*关联表单属性*@
case "relationFormAttr":
							@:<@(children.Tag) showField="@(children.ShowField)" @(children.IsStorage == 1 ? "v-model='" + Model.FormModel + "." + item.Name + "[i]." + children.LowerName + "'" : "") :relationField="'@(item.Name)-@(children.RelationField)'+i" :isStorage="@(children.IsStorage)" type='relationFormAttr'/>
break;
@*弹窗选择属性*@
case "popupSelect":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.IsDisabled)@(children.PropsValue)@(children.RelationField)@(children.ColumnOptions)@(children.InterfaceId)@(children.Placeholder)@(children.HasPage)@(children.PageSize)@(children.PopupType)@(children.PopupTitle)@(children.PopupWidth)@(children.TemplateJson)@(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name)-@(children.Name)'+i" type="popup"/>
break;
@*弹窗选择属性*@
case "popupAttr":
							@:<@(children.Tag) showField="@(children.ShowField)" @(children.IsStorage == 1 ? "v-model='" + Model.FormModel + "." + item.Name + "[i]." + children.LowerName + "'" : "") :relationField="'@(item.Name)-@(children.RelationField)'+i" :isStorage="@(children.IsStorage)" type='popupAttr'/>
break;
case "textarea":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.MaxLength)@(children.Placeholder)@(children.IsDisabled)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":"")@(children.ShowCount)/>
break;
case "popupTableSelect":
							@:<BpmPopupSelect v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.InterfaceId)@(children.Placeholder)@(children.IsDisabled)@(children.ColumnOptions)@(children.RelationField)@(children.PropsValue)@(children.HasPage)@(children.PageSize)@(children.PopupTitle)@(children.Multiple)@(children.Filterable)@(children.TemplateJson)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name + "-" +@children.LowerName)'+i" @(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")/>
break;
case "autoComplete":
							@:<@(children.Tag) v-model="@(Model.FormModel).@(item.Name)[i].@(children.LowerName)" @(children.Total)@(children.InterfaceId)@(children.Placeholder)@(children.IsDisabled)@(children.Clearable)@(children.RelationField)@(children.TemplateJson)@(children.IsLinked ? "@change='"+ @children.LowerName +"TableChange(i)' ":""):vModel="'@(item.Name + "-" +@children.LowerName)'+i" @(children.IsLinkage ? ":formData='dataForm' :rowIndex='i' ": "")/>
break;
}
						@:</u-form-item>
						@:</view>
}
					@:</view>
@if(item.FooterBtnsList.Count>0)
{		
				@:<view class="bpm-table-footer-btn" v-if="!setting.readonly && !judgeWrite('@(item.Name)')">
@foreach(var btnItem in item.FooterBtnsList)
{
        @if(btnItem.value!="batchRemove")
		{
					@:<view class="bpm-table-btn bpm-table-@(btnItem.btnType)-btn" @@click="@(btnItem.value=="add" ? "add"+item.ChildTableName+"Row()" : "openSelectDialog('"+item.ChildTableName+"','"+btnItem.value+"')")">
						@:<text class="bpm-table-btn-icon @(btnItem.btnIcon)"/>
						@:<text class="bpm-table-btn-text">@(btnItem.label)</text>
					@:</view>
		}
}
				@:</view>
}
@if(item.ShowSummary)
{
					@:<view class="bpm-table-item">
						@:<view class="bpm-table-item-title u-flex u-row-between">
							@:<text class="bpm-table-item-title-num">@(item.Label)合计</text>
						@:</view>
						@:<view class="u-p-l-20 u-p-r-20 form-item-box">
							@:<u-form-item v-for="(item,i) in @(item.LowerChildTableName)()" :key="i" :label="item.name">
								@:<BpmInput v-model="item.val" disabled placeholder="" />
							@:</u-form-item>
						@:</view>
					@:</view>
}
				@:</view>
break;
case "tableGrid":
@{GenCodeAppFormChildrenControls(item.Children);}
break;
case "tableGridTr":
@{GenCodeAppFormChildrenControls(item.Children);}
break;
case "tableGridTd":
@{GenCodeAppFormChildrenControls(item.Children);}
break;
case "groupTitle":
				@:<@(item.Tag) content="@(item.Content)" content-position="@(item.Contentposition)" @(item.TipLabel != null ? "helpMessage=\"" + item.TipLabel + "\" @groupIcon=\"clickIcon('" + item.Content + "','" + item.TipLabel + "')\"" : "")/>
break;
case "divider":
				@:<@(item.Tag) content="@(item.Default)" half-width="200" height="80"/>
break;
case "card":
case "row":
				@:<view class="bpm-card">
					@:<view class="bpm-card-cap u-line-1">@(item.Content)
@if(item.TipLabel != null)
{
						@:<u-icon name="question-circle-fill" class="u-m-l-10" color="#a0acb7"
							@:@@click="clickIcon('@(item.Content)','@(item.TipLabel)')" />
}
					@:</view>
@{GenCodeAppFormChildrenControls(item.Children);}
				@:</view>
break;
case "text":
				@:<@(item.Tag) content="@(item.Content)" :textStyle='@(item.TextStyle)'/>
break;
case "tab":
				@:<view prop="@(item.LowerName)">
					@:<u-tabs :is-scroll="false" :list="@(item.LowerName)Title" name="title" v-model="@(item.LowerName)Current" @@change="@(item.LowerName)Change"></u-tabs>
					@:<view>
@{ int m = 0;}
@foreach(var collapse in item.Children)
{
						@:<view v-show="@(m) == @(item.LowerName)Current">
@{GenCodeAppFormChildrenControls(collapse.Children);}
						@:</view>
						@{m++;}
}
					@:</view>
				@:</view>
break;
case "collapse":
				@:<view class="collapse">
					@:<u-collapse :accordion="@(item.Accordion)" ref="collapseRef@(collapseNum)">
@{collapseNum++;}
@foreach(var collapse in item.Children)
{
						@:<u-collapse-item class="collapse-item" name="@(collapse.Name)" title="@(collapse.Title)" @(collapse.Open) @@change="onCollapseChange">
@{GenCodeAppFormChildrenControls(collapse.Children);}
						@:</u-collapse-item>
}
					@:</u-collapse>
				@:</view>
break;
default:
				@:<view class="u-p-l-20 u-p-r-20 form-item-box">
@switch(item.bpmKey)
{
case "input":
case "textarea":
case "inputNumber":
case "switch":
case "radio":
case "checkbox":
case "select":
case "cascader":
case "areaSelect":
case "treeSelect":
case "uploadImg":
case "uploadFile":
case "rate":
case "slider":
case "timePicker":
case "datePicker":
case "organizeSelect":
case "depSelect":
case "posSelect":
case "userSelect":
case "usersSelect":
case "groupSelect":
case "roleSelect":
case "relationForm":
case "popupSelect":
case "colorPicker":
case "popupTableSelect":
case "createUser":
case "createTime":
case "currOrganize":
case "currPosition":
case "autoComplete":
case "billRule":
case "sign":
case "signature":
case "location":
					@:<u-form-item @(item.ShowLabel ? "label='"+item.Label+"' " : "")prop="@(item.LowerName)" @(item.IsRequired)@(item.NoShow ? "v-if='false' " : "v-if=\"judgeShow('" + item.OriginalName + "')\" ")@(item.ShowLabel && (item.Label != "null" && item.TipLabel != null) ? " left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + item.Label + "','" + item.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : "")>
break;
case "relationFormAttr":
case "popupAttr":
					@:<u-form-item @(item.ShowLabel ? "label='"+item.Label+"' " : "")prop="@(item.RelationField)" @(item.IsRequired)@(item.NoShow ? "v-if='false' " : (item.IsStorage == 1 ? "v-if=\"judgeShow('" + item.OriginalName + "')\" " : ""))@(item.ShowLabel && (item.Label != "null" && item.TipLabel != null) ? " left-icon=\"question-circle-fill\" @clickIcon=\"clickIcon('" + item.Label + "','" + item.TipLabel + "')\" :left-icon-style=\"{'color':'#a0acb7'}\" " : "")>
break;
case "editor":
					@:<u-form-item prop="@(item.LowerName)">
break;
case "button":
case "alert":
case "link":
					@:<u-form-item>
break;
default:
					@:<u-form-item @(item.ShowLabel ? "label='"+item.Label+"' " : "")prop="@(item.LowerName)" @(item.NoShow ? "v-if='false' " : "v-if=\"judgeShow('" + item.OriginalName + "')\" ")>
break;
}
@switch(item.bpmKey)
{
case "input":
case "textarea":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.MaxLength)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@blur='"+ @item.LowerName +"Change' ":"")@(item.UseScan)@(item.AddonBefore)@(item.AddonAfter)@(item.ShowCount)/>
break;
case "inputNumber":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Precision)@(item.ControlsPosition)@(item.AddonBefore)@(item.AddonAfter)@(item.Thousands)@(item.AmountChinese)@(item.Max)@(item.Min)@(item.Step)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "sign":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.IsDisabled) />
break;
case "signature":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Disaabled)@(item.AbleIds)@(item.Disabled)/>
break;
case "location":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.EnableLocationScope)@(item.AutoLocation)@(item.AdjustmentScope)@(item.EnableDesktopLocation)@(item.LocationScope)@(item.IsDisabled) />
break;
case "switch":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "radio":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Direction)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "checkbox":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Direction)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "select":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Filterable)@(item.Placeholder)@(item.Multiple)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "cascader":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.Filterable)@(item.ShowAllLevels)@(item.Clearable)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "areaSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Level)@(item.Placeholder)@(item.IsDisabled)@(item.Multiple)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "treeSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" :options="@(item.LowerName)Options" :props="@(item.LowerName)Props" @(item.Filterable)@(item.Placeholder)@(item.Multiple)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "uploadImg":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.TipText)@(item.IsDisabled)@(item.SizeUnit)@(item.FileSize)@(item.PathType)@(item.IsAccount)@(item.Folder)/>
break;
case "uploadFile":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.TipText)@(item.IsDisabled)@(item.Limit)@(item.SizeUnit)@(item.FileSize)@(item.Accept)@(item.PathType)@(item.IsAccount)@(item.Folder)/>
break;
case "rate":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" size="40" @(item.Count!=null && item.Count!="" ? ":max='"+item.Count+"'" : "") @(item.Readonly)@(item.AllowHalf)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "slider":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Step)@(item.Min)@(item.Max)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")style="width: 100%;"/>
break;
case "timePicker":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Type!="" ? item.Type : "type='time'") @(item.Format)@(item.StartTime)@(item.EndTime)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "datePicker":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Type!="" ? item.Type : "type='date'") @(item.Format)@(item.StartTime)@(item.EndTime)@(item.Placeholder)@(item.IsDisabled)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "organizeSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "depSelect":
case "posSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "userSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.UserRelationAttr)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "usersSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "groupSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "roleSelect":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.Multiple)@(item.Placeholder)@(item.IsDisabled)@(item.SelectType != "" ? "selectType='" + item.SelectType + "' " : "")@(item.IsCustomSelect ? @item.AbleIds : "")@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "barcode":
						@:<@(item.Tag) @(item.Width) @(item.Height) @(item.Format) @(item.LineColor) @(item.Background) @(item.StaticText) @(item.DataType) @(item.RelationField) :formData="dataForm" />
break;
case "qrcode":
						@:<@(item.Tag) @(item.Width) @(item.ColorDark) @(item.ColorLight) @(item.StaticText) @(item.DataType) @(item.RelationField) :formData="dataForm" />
break;
@*关联表单*@
case "relationForm":
						@:<@(item.Tag) @(item.vModel)@(item.IsDisabled)@(item.RelationField)@(item.ColumnOptions)@(item.ModelId != "" ? "modelId='"+ item.ModelId +"' " :"")@(item.Placeholder)@(item.HasPage)@(item.PageSize) @(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")@(item.TemplateJson)@(item.IsLinkage ? ":formData='dataForm' ": "")vModel="@(item.Name)" type="relation"/>
break;
@*弹窗选择*@
case "popupSelect":
						@:<@(item.Tag) @(item.vModel)@(item.IsDisabled)@(item.PropsValue)@(item.RelationField)@(item.ColumnOptions)@(item.InterfaceId)@(item.Placeholder)@(item.HasPage)@(item.PageSize)@(item.PopupType)@(item.PopupTitle)@(item.PopupWidth)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")@(item.TemplateJson)@(item.IsLinkage ? ":formData='dataForm' ": "")vModel="@(item.Name)" type="popup"/>
break;
case "colorPicker":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" @(item.ColorFormat)@(item.IsDisabled)/>
break;
case "popupTableSelect":
						@:<BpmPopupSelect @(item.vModel)@(item.InterfaceId)@(item.Placeholder)@(item.IsDisabled)@(item.ColumnOptions)@(item.RelationField)@(item.PropsValue)@(item.HasPage)@(item.PageSize)@(item.PopupTitle)@(item.Multiple)@(item.Filterable)@(item.TemplateJson)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":""):vModel="'@(item.LowerName)'" @(item.IsLinkage ? ":formData='dataForm' ": "")/>
break;
case "createUser":
case "createTime":
case "currOrganize":
case "currPosition":
						@:<BpmOpenData v-model="@(Model.FormModel).@(item.LowerName)" @(item.Type) disabled/>
break;
case "autoComplete":
						@:<@(item.Tag) v-model="@(Model.FormModel).@(item.LowerName)" vModel="@(item.Name)" @(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")@(item.IsLinkage ? ":formData='dataForm' ": "")@(item.IsDisabled)@(item.TemplateJson)@(item.Total)@(item.InterfaceId)@(item.Placeholder)@(item.Clearable)@(item.RelationField)/>
break;
case "relationFormAttr":
case "popupAttr":
						@:<@(item.Tag) showField="@(item.ShowField)" @(item.IsStorage == 1 ? item.IsDisabled : "")relationField="@(item.RelationField)" :isStorage="@(item.IsStorage)" @(item.vModel) type='@(item.bpmKey)' @(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "editor":
						@:<@(item.Tag) @(item.vModel)@(item.IsLinked ? "@change='"+ @item.LowerName +"Change' ":"")/>
break;
case "button":
						@:<@(item.Tag) buttonText="@(item.ButtonText)" align="@(item.Align)" type="@(item.Type)"/>
break;
case "alert":
						@:<@(item.Tag) type="@(item.Type)" title="@(item.Title)" tagIcon='icon-ym icon-ym-generator-alert' :showIcon="@(item.ShowIcon)" :closable="@(item.Closable.ToString().ToLower())" description="@(item.Description)" closeText="@(item.CloseText)"/>
break;
case "link":
						@:<@(item.Tag) content="@(item.Content)" href="@(item.Href)" target='@(item.Target)' :textStyle='@(item.TextStyle)' />
break;
default:
						@:<BpmInput v-model="@(Model.FormModel).@(item.LowerName)" placeholder="系统自动生成" disabled/>
break;
}
					@:</u-form-item>
				@:</view>
break;
}
}
	}
}