﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.user;

/// <summary>
/// 用户查询列表
/// </summary>
[SuppressSniffer]
public class userQueryRequest
{
    /// <summary>
    ///   weixin_open_id类型，1-微信公众号，2-微信小程序，9-微信大账号，weixin_open_id不为空时该参数必填
    /// </summary>
    public int? open_id_type { get; set; }

    /// <summary>
    /// 微信体系的openId；和yz_open_id、mobile、weixin_union_id至少传一个
    /// </summary>
    public string  weixin_open_id { get; set;}

    /// <summary>
    /// 手机号，默认使用+86区号,如果需要使用其他地区的手机号需要加上区，例如"+54-123123"；和yz_open_id、weixin_union_id、weixin_open_id至少传一个
    /// </summary>
    public string  mobile { get; set; }

    /// <summary>
    /// 有赞用户id，用户在有赞的唯一id。推荐使用；和mobile、weixin_union_id、weixin_open_id至少传一个
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 微信体系的unionId；和yz_open_id、mobile、weixin_open_id至少传一个
    /// </summary>
    public string weixin_union_id { get; set; }

    /// <summary>
    /// 返回结果类型列表，0-手机帐号；1-微信公众号；2-微信小程序；9-微信大账号；默认0。开发者可以根据自己需要的选择传入。补充说明：；在微信公众号场景下，正常会进行两次授权，一次通过商家公众号授权，一次通过有赞公众号授权，分别产生不同的用户数据： 1）1-微信公众号：通过商家的公众号授权产生的用户 2）9-微信大账号：通过”有赞“公众号授权产生的用户
    /// </summary>
    public List<int> result_type_list { get; set; }

}