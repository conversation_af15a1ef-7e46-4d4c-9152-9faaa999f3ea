﻿using SqlSugar;

namespace BPM.Domain.Entity.order;

/// <summary>
/// 订单明细表
/// </summary>
[SugarTable("ORDER_ITEM")]
[Tenant("IPOS-ORDER")]
public class orderItemEntity
{
    /// <summary>
    /// 明细序号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string order_seq { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string order_no { get; set; }

    /// <summary>
    /// 订单明细id
    /// </summary>
    public string oid { get; set; }

    /// <summary>
    /// 商品id
    /// </summary>
    public string item_id { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string item_no { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    public int item_type { get; set; }

    /// <summary>
    /// 商品别称
    /// </summary>
    public string alias { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string item_barcode { get; set; }

    /// <summary>
    /// 是否赠品
    /// </summary>
    public int is_present { get; set; }

    /// <summary>
    /// 订单数量
    /// </summary>
    public int num { get; set; }

    /// <summary>
    /// 外部商品id
    /// </summary>
    public string outer_item_id { get; set; }

    /// <summary>
    /// 外部SKUid
    /// </summary>
    public string outer_sku_id { get; set; }

    /// <summary>
    /// 商品原价
    /// </summary>
    public decimal price { get; set; }

    /// <summary>
    /// 促销价格
    /// </summary>
    public decimal points_price { get; set; }

    /// <summary>
    /// 销售价格
    /// </summary>
    public decimal discount_price { get; set; }

    /// <summary>
    /// 均摊价格
    /// </summary>
    public decimal payment { get; set; }

    /// <summary>
    /// SKU条码
    /// </summary>
    public string sku_barcode { get; set; }

    /// <summary>
    /// SKUID
    /// </summary>
    public string sku_id { get; set; }

    /// <summary>
    /// SKU编码
    /// </summary>
    public string sku_no { get; set; }

    /// <summary>
    /// SKU描述
    /// </summary>
    public string sku_properties_name { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string title { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal total_fee { get; set; }
}
