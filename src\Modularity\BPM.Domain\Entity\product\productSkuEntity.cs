﻿using SqlSugar;

namespace BPM.Domain.Entity.product;

/// <summary>
/// 版 本 BPM敏捷开发框架
/// Copyright (c) 2018-2022 深圳市中畅源科技开发有限公司
/// 创建人：Aarons
/// 日 期：2022-05-27
/// 描 述：商品规格实体
/// </summary>
[SugarTable("PDT_SKU")]
[Tenant("IPOS-PRODUCT")]
public class productSkuEntity
{
    /// <summary> 
    /// 商品SKUID 
    /// </summary> 
    /// <returns></returns> 
    public string id { get; set; }
    /// <summary> 
    /// 商品编号（pdt_product表id） 
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "product_id", IsPrimaryKey = true)]
    public string product_id { get; set; }
    /// <summary> 
    /// 商品SKU编号 
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "sku_sn", IsPrimaryKey = true)]
    public string sku_sn { get; set; }
    /// <summary> 
    /// 商品标题 
    /// </summary> 
    /// <returns></returns> 
    public string sku_name { get; set; }
    /// <summary> 
    /// 商品市场价 
    /// </summary> 
    /// <returns></returns> 
    public decimal? make_price { get; set; }
    /// <summary> 
    /// 商品销售价,需要在Sku价格所决定的的区间内 
    /// </summary> 
    /// <returns></returns> 
    public decimal? sale_price { get; set; }
    /// <summary> 
    /// 商品成本价 
    /// </summary> 
    /// <returns></returns> 
    public decimal? cost_price { get; set; }
    /// <summary> 
    /// 商品SKU规格描述, 格式：pText:vText;pText:vText，多个sku之间用逗号分隔，如：颜色:黄色;尺寸:M。pText和vText文本中不可以存在冒号和分号以及逗号 
    /// </summary> 
    /// <returns></returns> 
    public string des { get; set; }
    /// <summary> 
    /// 商品SKU键值,由商品SKU明细的规格值ID按小到大组成的数字,便于检索 
    /// </summary> 
    /// <returns></returns> 
    public string group { get; set; }
    /// <summary> 
    /// 商品SKU状态（固定值：0.正常、1.删除，2-禁用） 
    /// </summary> 
    /// <returns></returns> 
    public int? sku_state { get; set; }

    /// <summary>
    /// 店铺渠道类型;-1 :全部渠道;0 :网店;1: 门店
    /// </summary>
    public int channel { get; set; }
}

[SugarTable("PDT_SKU_PRICE")]
[Tenant("IPOS-PRODUCT")]
public class productSkuShopPriceEntity
{
    /// <summary> 
    /// 主键
    /// </summary> 
    /// <returns></returns> 
    public string id { get; set; }

    /// <summary> 
    /// 商品编号
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "product_id", IsPrimaryKey = true)]
    public long product_id { get; set; }

    /// <summary> 
    /// 是否多规格
    /// </summary> 
    /// <returns></returns> 
    public int has_sku { get; set; }

    /// <summary> 
    /// 规格编号
    /// </summary> 
    /// <returns></returns> 
    [SugarColumn(ColumnName = "sku_id", IsPrimaryKey = true)]
    public long sku_id { get; set; }

    /// <summary> 
    /// 门店编号
    /// </summary> 
    /// <returns></returns> 
    public string store_id { get; set; }

    /// <summary> 
    /// 门店销售价
    /// </summary> 
    /// <returns></returns> 
    public long sale_price { get; set; }

    /// <summary> 
    /// 创建时间 
    /// </summary> 
    /// <returns></returns> 
    public DateTime? create_date { get; set; }

    /// <summary> 
    /// 更新日期 
    /// </summary> 
    /// <returns></returns> 
    public DateTime? modify_date { get; set; }

    /// <summary>
    /// 标记状态(select,add,edit)
    /// </summary>
    public string tag_status { get; set; }


    /// <summary>
    /// 同步主体
    /// </summary>
    public string tag_body { get; set; }
    /// <summary>
    /// ERP条码
    /// </summary>
    public string PLUCODE { get; set; }

}

/// <summary>
/// 商品规格详情
/// </summary>
[SugarTable("PDT_SKU_DETAIL")]
[Tenant("IPOS-PRODUCT")]
public class productSkuDetailEntity
{
    /// <summary> 
    /// id 
    /// </summary> 
    /// <returns></returns> 
    public string id { get; set; }
    /// <summary> 
    /// 商品SPUID 
    /// </summary> 
    /// <returns></returns> 
    public string product_id { get; set; }
    /// <summary> 
    ///   
    /// </summary> 
    /// <returns></returns> 
    public string sku_id { get; set; }
    /// <summary> 
    /// 商品规格编号（pdt_spec_name表id） 
    /// </summary> 
    /// <returns></returns> 
    public int? spec_name_id { get; set; }
    /// <summary> 
    /// 商品规格值编号（tb_pdt_spec_value表id） 
    /// </summary> 
    /// <returns></returns> 
    public int? spec_value_id { get; set; }
}

