﻿using BPM.DependencyInjection;
namespace BPM.Domain.Requests.product;

/// <summary>
/// 更新库存请求
/// </summary>
[SuppressSniffer]
public class updateProductStoreRequest
{
    /// <summary>
    /// 总店商品ID
    /// </summary>
    public long root_item_id { get; set; }
    /// <summary>
    /// 场景：多sku商品的分店更新参数列表，不同skuId下的multi_sku_update_open_param_list数量总和不允许超过20
    /// </summary>
    public List<multiSkuList> multi_sku_update_open_param_list { get; set; }

    /// <summary>
    ///  更新无sku库存集合
    /// </summary>
    public List<noSkuList> no_sku_update_open_param_list { get; set; }

    public class multiSkuList
    {
        /// <summary>
        /// skuId
        /// </summary>
        public long root_sku_id { get; set; }

        /// <summary>
        ///  更新sku库存集合
        /// </summary>
        public List<updateStockList> update_price_stock_open_param_list { get; set; }

        /// <summary>
        ///  更新库存集合
        /// </summary>
        public class updateStockList
        {
            /// <summary>
            /// 门店id
            /// </summary>
            public long node_kdt_id { get; set; }

            /// <summary>
            /// 库存数量
            /// </summary>
            public long stock_num { get; set; }

        }
    }

    /// <summary>
    ///  无sku集合
    /// </summary>
    public class noSkuList
    {
        /// <summary>
        /// 门店id
        /// </summary>
        public long node_kdt_id { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public long stock_num { get; set; }

    }


}

public class MParamList
{
    /// <summary>
    /// 总店商品的skuid
    /// </summary>
    public long root_sku_id { get; set; }

    /// <summary>
    /// 更新参数列表
    /// </summary>
    public List<NParamList> update_price_stock_open_param_list { get; set; }
}

public class NParamList
{
    /// <summary>
    /// 分店店铺id
    /// </summary>
    public long node_kdt_id { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public long stock_num { get; set; }

    /// <summary>
    /// 价格,单位:分
    /// </summary>
    public long price { get; set; }
}
