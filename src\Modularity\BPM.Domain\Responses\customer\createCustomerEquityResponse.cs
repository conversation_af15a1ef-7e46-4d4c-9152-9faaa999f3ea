﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.customer;

/// <summary>
/// 创建客户权益卡响应
/// </summary>
[SuppressSniffer]
public class createCustomerEquityResponse
{
    /// <summary>
    /// 权益卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 激活地址
    /// </summary>
    public string validate_url { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool is_success { get; set; }
}



/// <summary>
/// 客户信息响应
/// </summary>

public class CustomerInfoResponse
{
    /// <summary>
    /// openid
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 客户信息
    /// </summary>
    public socialInfo social_info { get; set; }

    /// <summary>
    /// 客户信息
    /// </summary>
    public class socialInfo
    { 
        public string mobile { get; set; }
    
    }

}