<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BPM.Application</name>
    </assembly>
    <members>
        <member name="T:BPM.Application.Bill.AlipayBillService">
            <summary>
            支付宝对账单
            </summary>
        </member>
        <member name="F:BPM.Application.Bill.AlipayBillService._alipayClient">
            <summary>
            支付宝客户端
            </summary>
        </member>
        <member name="F:BPM.Application.Bill.AlipayBillService._sugarClient">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="M:BPM.Application.Bill.AlipayBillService.#ctor(Essensoft.Paylink.Alipay.IAlipayClient,SqlSugar.ISqlSugarClient)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:BPM.Application.Bill.AlipayBillService.DownloadAlipayBill(System.String)">
            <summary>
            下载支付宝对账单
            </summary>
            <param name="bill_date">对账单日期，格式：yyyy-MM-dd，默认为前一天</param>
            <returns>返回下载结果的JSON字符串，包含状态码、消息、记录数等信息</returns>
            <remarks>
            方法流程：
            1. 获取对账单日期，默认为前一天
            2. 获取所有支付宝配置信息
            3. 遍历每个支付宝配置，下载并解析对账单
            4. 保存解析后的账单数据
            
            返回结果说明：
            - status: 200表示成功，500表示失败
            - count: 成功处理的记录数
            - meg: 处理结果说明
            - trade_date: 对账单日期
            - syn_date: 同步时间
            </remarks>
        </member>
        <member name="M:BPM.Application.Bill.AlipayBillService.getAlipayList">
            <summary>
            获取支付宝配置列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.Bill.AlipayBillService.saveAlipayBill(System.Collections.Generic.List{BPM.Domain.Entity.shop.shopAlipayBillEntity},System.String)">
            <summary>
            保存支付宝对账单
            </summary>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="T:BPM.Application.Bill.AliPayBillMap">
            <summary>
            支付宝对账单关系映射
            </summary>
        </member>
        <member name="T:BPM.Application.Bill.WeChatPayBillService">
            <summary>
            微信对账单服务
            </summary>
        </member>
        <member name="F:BPM.Application.Bill.WeChatPayBillService._weChatPayClient">
            <summary>
            微信支付客户端
            </summary>
        </member>
        <member name="F:BPM.Application.Bill.WeChatPayBillService._sugarClient">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="F:BPM.Application.Bill.WeChatPayBillService._db">
            <summary>
            多租户事务.
            </summary>
        </member>
        <member name="M:BPM.Application.Bill.WeChatPayBillService.#ctor(Essensoft.Paylink.WeChatPay.V2.IWeChatPayClient,SqlSugar.ISqlSugarClient,Microsoft.Extensions.Logging.ILogger{BPM.Application.Bill.WeChatPayBillService})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:BPM.Application.Bill.WeChatPayBillService.DownloadWechatPayBill(System.String)">
            <summary>
            下载微信对账单
            </summary>
            <param name="bill_date">对账单日期，格式：yyyy-MM-dd，默认为前一天</param>
            <returns>返回下载结果的JSON字符串，包含状态码、消息、记录数等信息</returns>
            <remarks>
            方法流程：
            1. 获取对账单日期，默认为前一天
            2. 获取所有微信支付配置信息
            3. 遍历每个微信支付配置，下载并解析对账单
            4. 保存解析后的账单数据
            
            返回结果说明：
            - status: 200表示成功，500表示失败
            - count: 成功处理的记录数
            - meg: 处理结果说明
            - trade_date: 对账单日期
            - syn_date: 同步时间
            </remarks>
        </member>
        <member name="M:BPM.Application.Bill.WeChatPayBillService.ParseBillResponse(System.String)">
            <summary>
            解析账单响应内容
            </summary>
        </member>
        <member name="M:BPM.Application.Bill.WeChatPayBillService.SaveWechatBillsWithRetry(System.Collections.Generic.List{BPM.Domain.Entity.shop.shopWechatBillEntity},System.String)">
            <summary>
            保存微信对账单（带重试机制）
            </summary>
        </member>
        <member name="M:BPM.Application.Bill.WeChatPayBillService.getWechatList">
            <summary>
            获取微信配置列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:BPM.Application.Configuration.ProductSyncConfiguration">
            <summary>
            商品同步配置
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.MaxPageSize">
            <summary>
            最大页大小
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.MaxPageNo">
            <summary>
            最大页码限制
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.MaxDataThreshold">
            <summary>
            最大数据量阈值
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.MinDataThreshold">
            <summary>
            最小数据量阈值
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.BatchSize">
            <summary>
            批量处理大小
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.CompleteMaxProcessCount">
            <summary>
            商品补全最大处理数量
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.CompleteBatchSize">
            <summary>
            商品补全批次大小
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.ApiCallDelay">
            <summary>
            API调用延迟（毫秒）
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.BatchDelay">
            <summary>
            批次间延迟（毫秒）
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.RetryDelay">
            <summary>
            重试延迟（毫秒）
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.EnableProductCompletion">
            <summary>
            是否启用商品补全
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.EnableStoreProductResync">
            <summary>
            是否启用门店商品重新同步
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.MaxParallelism">
            <summary>
            并行处理最大线程数
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.CacheExpirationMinutes">
            <summary>
            缓存过期时间（分钟）
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.DelayBetweenBatches">
            <summary>
            批次间延迟时间（毫秒）
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.MaxRetryAttempts">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.RetryDelaySeconds">
            <summary>
            重试延迟时间（秒）
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.EnableBatchProcessing">
            <summary>
            是否启用批量处理
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.EnableParallelProcessing">
            <summary>
            是否启用并行处理
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.EnableDetailedLogging">
            <summary>
            是否启用详细日志
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.DefaultTimeRangeDays">
            <summary>
            默认时间范围天数
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.EnableConflictDetection">
            <summary>
            启用冲突检测，bool 类型，默认true，是否启用条码冲突检测机制
            </summary>
        </member>
        <member name="P:BPM.Application.Configuration.ProductSyncConfiguration.ConflictDetectionWindowMinutes">
            <summary>
            冲突检测时间窗口，int 类型，单位分钟，默认5，在此时间窗口内的条码被认为可能存在冲突
            </summary>
        </member>
        <member name="T:BPM.Application.CustomerEquityService">
            <summary>
            会员管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerEquityService._repository">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerEquityService._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerEquityService._eventPublisher">
            <summary>
            事件总线.
            </summary>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,BPM.EventBus.IEventPublisher,Microsoft.Extensions.Logging.ILogger{BPM.Application.CustomerEquityService})">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
            <param name="youzanService">有赞服务接口</param>
            <param name="eventPublisher">事件发布服务</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.getCustomerEquity">
            <summary>
            获取权益卡列表模板
            </summary>
            <returns>操作结果，true表示成功，false表示失败</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.createCustomerEquity">
            <summary>
            创建客户权益卡
            </summary>
            <returns>处理结果，包含成功和失败的统计信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.ProcessCustomerEquityAsync(System.String,BPM.Domain.Requests.equity.createCustomerEquityRequest)">
            <summary>
            处理单个客户权益卡
            </summary>
            <param name="token">有赞接口访问令牌</param>
            <param name="item">客户权益卡请求信息</param>
            <returns>处理结果，包含成功/失败状态和错误信息</returns>
        </member>
        <member name="T:BPM.Application.CustomerEquityService.DateTimeJsonConverter">
            <summary>
            自定义DateTime JSON转换器
            </summary>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.NormalizeDateTimeValue(System.DateTime)">
            <summary>
            规范化日期时间值，处理超出范围的情况
            </summary>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.ConvertToLocalTime(System.DateTime)">
            <summary>
            转换时间为UTC时间（从中国时区）
            </summary>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.deleteCustomerEquity">
            <summary>
            删除客户权益卡
            </summary>
            <returns>处理结果，包含成功和失败的统计信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.equityRenewal">
            <summary>
            权益卡续期
            </summary>
            <returns>处理结果，包含成功和失败的统计信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.deleteCustomerVoucher">
            <summary>
            删除客户优惠券
            </summary>
            <returns>处理结果，包含成功和失败的统计信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.ProcessVoucherDeleteAsync(System.String,System.String)">
            <summary>
            处理单个客户优惠券删除
            </summary>
            <param name="yzOpenId">用户有赞Open ID</param>
            <param name="phone">用户手机号</param>
            <returns>处理结果，包含成功/失败状态和错误信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.resyncEquityInfo">
            <summary>
            重新下载权益卡信息
            </summary>
            <returns>处理结果，包含成功和失败的统计信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.modifySyncTagByPhone(System.String,System.String,System.String,System.String,System.Int32,System.String)">
            <summary>
            更新客户权益卡同步标记
            </summary>
            <param name="phone">手机号</param>
            <param name="equity_card_alias_id">权益卡别名ID</param>
            <param name="equity_card_no">权益卡号</param>
            <param name="tag_status">标记状态</param>
            <param name="status">状态</param>
            <param name="tag_body">标记内容</param>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.modifyEquityRenewalTag(System.String,System.String,System.String,System.String)">
            <summary>
            更新客户权益卡续期标记
            </summary>
            <param name="yz_open_id">有赞开放ID</param>
            <param name="equity_card_no">权益卡号</param>
            <param name="tag_status">标记状态</param>
            <param name="tag_body">标记内容</param>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.deleteSyncTagByPhone(System.String,System.String,System.String,System.String)">
            <summary>
            删除客户权益卡同步标记
            </summary>
            <param name="phone">手机号</param>
            <param name="equity_card_no">权益卡号</param>
            <param name="tag_status">标记状态</param>
            <param name="tag_body">标记内容</param>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.crateEquity(System.Collections.Generic.List{BPM.Domain.Entity.customer.EquityEntity})">
            <summary>
            创建或更新权益卡模板
            </summary>
            <param name="list">权益卡模板列表</param>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.UpdateErrorStatus(BPM.Domain.Entity.customer.customerEquityEntity,System.String,System.String)">
            <summary>
            更新错误状态
            </summary>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.getPageCreateCustomerEquityList(BPM.Domain.Queries.customerQuery)">
            <summary>
            获取待创建的客户权益卡列表
            </summary>
            <param name="query">查询条件</param>
            <returns>分页后的客户权益卡列表</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.getPageDeleteCustomerEquityList(BPM.Domain.Queries.customerQuery)">
            <summary>
            获取待删除的客户权益卡列表
            </summary>
            <param name="query">查询条件</param>
            <returns>分页后的客户权益卡列表</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.getPageCustomerEquityRenewalList(BPM.Domain.Queries.customerQuery)">
            <summary>
            获取待续期的客户权益卡列表
            </summary>
            <param name="query">查询参数</param>
            <returns>分页后的客户权益卡列表</returns>
        </member>
        <member name="M:BPM.Application.CustomerEquityService.equityRenewalByCardNo(System.String)">
            <summary>
            指定卡号权益卡续期
            </summary>
            <param name="cardNo">权益卡号</param>
            <returns>处理结果，包含成功和失败的统计信息</returns>
        </member>
        <member name="T:BPM.Application.CustomerService">
            <summary>
            客户管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerService._repository">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerService._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerService._eventPublisher">
            <summary>
            事件总线.
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerService._cache">
            <summary>
            缓存管理器
            </summary>
        </member>
        <member name="F:BPM.Application.CustomerService._shopCacheService">
            <summary>
            店铺缓存服务
            </summary>
        </member>
        <member name="M:BPM.Application.CustomerService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,BPM.EventBus.IEventPublisher,BPM.Common.Manager.ICacheManager,BPM.Application.Services.ShopCacheService)">
            <summary>
            构造函数
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:BPM.Application.CustomerService.createCustomer">
            <summary>
            创建客户
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateCustomer">
            <summary>
            更新客户
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.createCustomerLevel">
            <summary>
             获取会员等级模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.setCustomerLevel">
            <summary>
            批量设置会员等级
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateCustomerOpenId">
            <summary>
            更新客户OpenId
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateCustomerOpenId2">
            <summary>
            更新客户OpenId,处理未授权手机
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.resyncCustomerInfo">
            <summary>
            重新同步会员资料
            </summary>
            <returns>返回同步结果，包含成功数量、失败数量和错误信息</returns>
        </member>
        <member name="M:BPM.Application.CustomerService.getPageCrateCustomerList(BPM.Domain.Queries.customerQuery)">
            <summary>
            获取客户列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.getPageUpdateCustomerList(BPM.Domain.Queries.customerQuery)">
            <summary>
            获取客户列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.addSyncTagByPhone(System.String,System.String,System.String,System.String)">
            <summary>
            修改客户同步标记
            </summary>
            <param name="phone">手机号</param>
            <param name="tag_status">同步标记</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateCustomerByPhone(System.Collections.Generic.List{BPM.Domain.Responses.customer.CustomerInfoResponse},System.Collections.Generic.List{System.String})">
            <summary>
            更新客户openId
            </summary>
            <param name="list">有赞返回的客户信息列表</param>
            <param name="phones">需要更新的手机号列表</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateCustomerByPhone(System.String,System.String)">
            <summary>
            更新客户openId
            </summary>
            <param name="phone">手机号</param>
            <param name="yz_open_id">有赞openId</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateErrorTagByPhone(System.String,System.String)">
            <summary>
            修改客户同步标记
            </summary>
            <param name="phone">手机号</param>
            <param name="tag_body">同步标记</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.modifySyncTagById(System.String,System.String,System.String)">
            <summary>
            修改客户同步标记
            </summary>
            <param name="customer_id">客户ID</param>
            <param name="tag_status">同步标记</param>
            <param name="tag_body">标记内容</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.getPageCustomerNotOpenId">
            <summary>
            获取客户列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.getPageCustomerNotOpenId2">
            <summary>
            获取客户列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.getPageMemberGradeList(BPM.Domain.Queries.customerQuery)">
            <summary>
            获取会员等级同步列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateSuccessMemberGradeTagByPhone(System.String,System.String)">
            <summary>
            修改客户同步标记
            </summary>
            <param name="phone">手机号</param>
            <param name="tag_status">同步标记</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.updateErrorMemberGradeTagByPhone(System.String,System.String)">
            <summary>
            修改会员等级同步标记
            </summary>
            <param name="phone">手机号</param>
            <param name="tag_body">同步标记</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CustomerService.GetStoreIdAsync(System.Int64)">
            <summary>
            获取店铺ID
            </summary>
        </member>
        <member name="T:BPM.Application.DTOs.updateProducPriceDto">
            <summary>
            更新商品价格DTO
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.updateProducPriceDto.product_id">
            <summary> 
            商品编号
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducPriceDto.has_sku">
            <summary> 
            是否多规格
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducPriceDto.sku_id">
            <summary> 
            规格编号
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducPriceDto.sale_price">
            <summary> 
            门店销售价
            </summary> 
        </member>
        <member name="T:BPM.Application.DTOs.updateProducStockDto">
            <summary>
            更新商品库存DTO
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.updateProducStockDto.store_id">
            <summary> 
            门店编号
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducStockDto.product_id">
            <summary> 
            商品编号
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducStockDto.has_sku">
            <summary> 
            是否多规格
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducStockDto.sku_id">
            <summary> 
            规格编号
            </summary> 
        </member>
        <member name="P:BPM.Application.DTOs.updateProducStockDto.stock">
            <summary> 
            门店库存
            </summary> 
        </member>
        <member name="T:BPM.Application.DTOs.updateMultiSku">
            <summary>
            更新多规格SKU
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.updateMultiSku.sku_id">
            <summary>
            SKU编号
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.updateMultiSku.update_price_stock_open_param_list">
            <summary>
            更新库存参数列表
            </summary>
        </member>
        <member name="T:BPM.Application.DTOs.updateStockParam">
            <summary>
            更新库存参数
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.updateStockParam.node_kdt_id">
            <summary>
            节点店铺ID
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.updateStockParam.stock">
            <summary>
            库存数量
            </summary>
        </member>
        <member name="T:BPM.Application.DTOs.CompleteProductsRequest">
            <summary>
            补全商品请求参数
            </summary>
        </member>
        <member name="P:BPM.Application.DTOs.CompleteProductsRequest.maxCount">
            <summary>
            最大处理数量
            </summary>
        </member>
        <member name="T:BPM.Application.EventSubscriber.CustomerEventSubscriber">
            <summary>
            客户事件订阅.
            </summary>
        </member>
        <member name="F:BPM.Application.EventSubscriber.CustomerEventSubscriber._repository">
            <summary>
             服务提供.
            </summary>
        </member>
        <member name="F:BPM.Application.EventSubscriber.CustomerEventSubscriber._cache">
            <summary>
            缓存管理器
            </summary>
        </member>
        <member name="F:BPM.Application.EventSubscriber.CustomerEventSubscriber._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="F:BPM.Application.EventSubscriber.CustomerEventSubscriber._shopCacheService">
            <summary>
             服务提供.
            </summary>
        </member>
        <member name="F:BPM.Application.EventSubscriber.CustomerEventSubscriber._context">
            <summary>
             服务提供.
            </summary>
        </member>
        <member name="F:BPM.Application.EventSubscriber.CustomerEventSubscriber.CACHE_EXPIRE_SECONDS">
            <summary>
            缓存过期时间(秒)
            </summary>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.#ctor(SqlSugar.ISqlSugarClient,BPM.Common.Core.Manager.Tenant.ITenantManager,BPM.Common.Manager.ICacheManager,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,BPM.Application.Services.ShopCacheService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.AddCustomer(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户添加处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.UpdateCustomer(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户更新处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.UpdateId(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户更新处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.UpdateCustomerList(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户更新列表处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.UpdateTag(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户更新处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.PushCustomer(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户添加处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.PushUpdaterCustomer(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户添加处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.SyncCustomer(BPM.EventBus.EventHandlerExecutingContext)">
            <summary>
            客户信息同步处理.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.CustomerEvent(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            客户事件处理方法.
            </summary>
            <param name="pushRequest">推送请求参数，包含客户相关的事件信息</param>
            <returns>处理结果消息，成功返回空字符串，失败返回错误信息</returns>
            <remarks>
            该方法主要处理以下功能：
            1. 解析推送的客户信息
            2. 根据客户ID或手机号查找已存在的客户记录
            3. 处理客户创建和更新的逻辑
            4. 同步客户信息到数据库
            5. 发布相应的事件通知
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.FindCustomerAsync(BPM.Domain.Requests.subscriber.pushRequest,BPM.Domain.Dto.customer.customerPushDto)">
            <summary>
            查找客户信息的异步方法
            </summary>
            <param name="pushRequest">推送请求参数，包含有赞OpenID等信息</param>
            <param name="custPush">客户推送数据，包含账户ID和手机号等信息</param>
            <returns>返回查找到的客户实体，如果未找到则返回null</returns>
            <remarks>
            该方法按以下优先级查找客户：
            1. 首先通过有赞OpenID查找
            2. 如果未找到，则通过账户ID查找
            3. 最后通过手机号查找
            所有查询都使用行锁确保数据一致性
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.ValidateCustomerVersionAsync(BPM.Domain.Entity.customer.customerEntity,BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            验证客户版本信息的异步方法
            </summary>
            <param name="customer">客户实体对象</param>
            <param name="pushRequest">推送请求参数</param>
            <returns>返回验证结果：true表示验证通过，false表示验证失败</returns>
            <remarks>
            该方法主要验证：
            1. 如果是创建事件但客户已存在，则跳过处理
            2. 如果推送版本号小于等于当前版本号，则跳过处理
            用于避免重复处理和保证数据版本一致性
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.GetCustomerInfoAsync(BPM.Domain.Requests.subscriber.pushRequest,System.String)">
            <summary>
            获取客户详细信息的异步方法
            </summary>
            <param name="pushRequest">推送请求参数，包含有赞开放平台的相关信息</param>
            <param name="originalAccountId">原始账户ID，用于保持客户账户标识的一致性</param>
            <returns>返回客户详细信息DTO对象，如果获取失败则返回null</returns>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.GetStoreInfoAsync(BPM.Domain.Dto.customer.customerDto,BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            获取店铺信息
            </summary>
            <param name="cust">客户信息对象，包含归属店铺ID</param>
            <param name="pushRequest">推送请求对象，包含店铺ID</param>
            <returns>店铺实体对象，如果找不到返回null</returns>
            <remarks>
            该方法通过以下步骤获取店铺信息：
            1. 优先使用客户的归属店铺ID(ascription_kdt_id)
            2. 如果客户没有归属店铺，则使用推送请求中的店铺ID(kdt_id)
            3. 根据确定的店铺ID查询数据库获取完整的店铺信息
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.BuildCustomerInfoAsync(BPM.Domain.Entity.customer.customerEntity,BPM.Domain.Dto.customer.customerDto,BPM.Domain.Requests.subscriber.pushRequest,BPM.Domain.Dto.customer.customerPushDto,System.String)">
            <summary>
            构建客户信息实体的异步方法
            </summary>
            <param name="existingCustomer">已存在的客户实体，对于新客户为null</param>
            <param name="cust">从有赞API获取的客户信息数据</param>
            <param name="pushRequest">原始推送请求对象，包含版本号等信息</param>
            <param name="custPush">推送消息解析后的客户信息对象</param>
            <param name="store_id">关联的店铺ID</param>
            <returns>构建好的客户实体对象</returns>
            <remarks>
            该方法根据以下规则构建客户实体：
            1. 对于已存在的客户，保留原有的ID、创建时间、来源等信息
            2. 对昵称和真实姓名进行特殊字符过滤，只保留中文、英文和数字
            3. 限制头像URL长度不超过200字符，防止过长字段
            4. 根据是否为新客户设置不同的标签状态(add/edit)
            5. 记录有赞版本号，用于后续更新判断和乐观锁控制
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.SaveCustomerDataAsync(BPM.Domain.Entity.customer.customerEntity,BPM.Domain.Entity.customer.customerEntity,BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            保存客户数据到数据库的异步方法
            </summary>
            <param name="customerInfo">要保存的客户实体对象</param>
            <param name="existingCustomer">已存在的客户实体，对于新客户为null</param>
            <param name="pushRequest">原始推送请求对象，包含版本号等信息</param>
            <returns>错误信息字符串，成功返回空字符串</returns>
            <remarks>
            该方法负责将客户数据保存到数据库，包含以下关键处理：
            1. 启动数据库事务确保数据一致性
            2. 对于已存在的客户，首先更新版本号防止并发冲突
            3. 根据客户是否存在采用不同的保存策略：
               - 新客户：使用MERGE语句处理潜在的并发插入冲突
               - 已有客户：更新客户信息，保留原有关键字段
            4. 事务执行成功则提交，失败则回滚并返回错误信息
            5. 特别处理数据库死锁情况，抛出明确异常供上层函数进行重试
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.SaveNewCustomerAsync(BPM.Domain.Entity.customer.customerEntity)">
            <summary>
            保存新客户数据的异步方法
            </summary>
            <param name="customerInfo">要保存的新客户实体对象</param>
            <remarks>
            该方法使用MERGE语句处理新客户数据：
            1. 首先检查是否存在相同ID、手机号或OpenID的记录
            2. 如果不存在，则插入新记录
            3. 使用参数化查询防止SQL注入
            4. 如果MERGE失败，则尝试直接插入
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.UpdateExistingCustomerAsync(BPM.Domain.Entity.customer.customerEntity,BPM.Domain.Entity.customer.customerEntity,System.Int64)">
            <summary>
            更新现有客户数据的异步方法
            </summary>
            <param name="customerInfo">要更新的客户信息</param>
            <param name="existingCustomer">已存在的客户实体</param>
            <param name="version">时间戳</param>
            <remarks>
            该方法分两步更新客户数据：
            1. 首先尝试更新基本信息（姓名、手机号等）
            2. 如果OpenID不存在或已变更，则更新OpenID
            3. 如果基本信息更新失败，则尝试完整更新
            4. 保留某些字段不被更新（创建时间等）
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.VerifyCustomerDataAsync(BPM.Domain.Entity.customer.customerEntity,BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            验证客户数据完整性的异步方法
            </summary>
            <param name="customerInfo">要验证的客户信息</param>
            <param name="pushRequest">推送请求参数</param>
            <returns>验证结果消息，成功返回空字符串</returns>
            <remarks>
            该方法进行最终的数据验证：
            1. 最多重试3次验证
            2. 验证客户ID、OpenID或手机号至少有一个匹配
            3. 确保客户状态正常（未删除）
            4. 记录验证过程的日志
            5. 出现异常时进行适当的重试
            </remarks>
        </member>
        <member name="M:BPM.Application.EventSubscriber.CustomerEventSubscriber.Dispose">
            <summary>
            
            </summary>
        </member>
        <member name="T:BPM.Application.Helpers">
            <summary>
            帮助类
            </summary>
        </member>
        <member name="M:BPM.Application.Helpers.GenerateTimeStamp">
            <summary>
            产生TimeStamp
            </summary>
            <returns></returns>
        </member>
        <member name="T:BPM.Application.AlipayBillJobService">
            <summary>
            本地任务-下载支付宝账单.
            </summary>
        </member>
        <member name="F:BPM.Application.AlipayBillJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.AlipayBillJobService._alipayBillService">
            <summary>
            支付宝账单服务.
            </summary>
        </member>
        <member name="M:BPM.Application.AlipayBillJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.Bill.AlipayBillService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.AlipayBillJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.AlipayBillJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.createCustomerJobService">
            <summary>
            本地任务-创建客户.
            </summary>
        </member>
        <member name="F:BPM.Application.createCustomerJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.createCustomerJobService._customerService">
            <summary>
            crm服务.
            </summary>
        </member>
        <member name="M:BPM.Application.createCustomerJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.createCustomerJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.createCustomerJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmCreateEquityJobService">
            <summary>
            本地任务-创建CRM权益.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmCreateEquityJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmCreateEquityJobService._customerEquityService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmCreateEquityJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerEquityService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmCreateEquityJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmCreateEquityJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmCreateEquityTemplateJobService">
            <summary>
            本地任务-创建CRM权益模板.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmCreateEquityTemplateJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmCreateEquityTemplateJobService._customerEquityService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmCreateEquityTemplateJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerEquityService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmCreateEquityTemplateJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmCreateEquityTemplateJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmDeleteEquityJobService">
            <summary>
            本地任务-删除CRM权益.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmDeleteEquityJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmDeleteEquityJobService._customerEquityService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmDeleteEquityJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerEquityService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmDeleteEquityJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmDeleteEquityJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmResyncJobService">
            <summary>
            本地任务-重新同步CRM.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmResyncJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmResyncJobService._customerService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmResyncJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmResyncJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmResyncJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmUpdateJobService">
            <summary>
            本地任务-更新CRM信息.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmUpdateJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmUpdateJobService._customerService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmUpdateJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmUpdateJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmUpdateJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmUpdateOpenId2JobService">
            <summary>
            本地任务-更新CRM OpenId2.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmUpdateOpenId2JobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmUpdateOpenId2JobService._customerService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmUpdateOpenId2JobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmUpdateOpenId2JobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmUpdateOpenId2JobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.CrmUpdateOpenIdJobService">
            <summary>
            本地任务-更新CRM OpenId.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmUpdateOpenIdJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.CrmUpdateOpenIdJobService._customerService">
            <summary>
            CRM服务.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmUpdateOpenIdJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.CrmUpdateOpenIdJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.CrmUpdateOpenIdJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.customerEquityJobService">
            <summary>
            本地任务-创建客户.
            </summary>
        </member>
        <member name="F:BPM.Application.customerEquityJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.customerEquityJobService._customerEquityService">
            <summary>
            crm服务.
            </summary>
        </member>
        <member name="M:BPM.Application.customerEquityJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerEquityService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.customerEquityJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.customerEquityJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.customerEquityRenewalJobService">
            <summary>
            本地任务-创建客户.
            </summary>
        </member>
        <member name="F:BPM.Application.customerEquityRenewalJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.customerEquityRenewalJobService._customerEquityService">
            <summary>
            crm服务.
            </summary>
        </member>
        <member name="M:BPM.Application.customerEquityRenewalJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.CustomerEquityService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.customerEquityRenewalJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.customerEquityRenewalJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.GetBatchProductJobService">
            <summary>
            本地任务-批量获取商品.
            </summary>
        </member>
        <member name="F:BPM.Application.GetBatchProductJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.GetBatchProductJobService._productService">
            <summary>
            商品服务.
            </summary>
        </member>
        <member name="M:BPM.Application.GetBatchProductJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ProductService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.GetBatchProductJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.GetBatchProductJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.GetDetailProductJobService">
            <summary>
            本地任务-获取商品详情.
            </summary>
        </member>
        <member name="F:BPM.Application.GetDetailProductJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.GetDetailProductJobService._productService">
            <summary>
            商品服务.
            </summary>
        </member>
        <member name="M:BPM.Application.GetDetailProductJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ProductService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.GetDetailProductJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.GetDetailProductJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.OnSaleProductJobService">
            <summary>
            本地任务-获取在售商品.
            </summary>
        </member>
        <member name="F:BPM.Application.OnSaleProductJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.OnSaleProductJobService._productService">
            <summary>
            商品服务.
            </summary>
        </member>
        <member name="M:BPM.Application.OnSaleProductJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ProductService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.OnSaleProductJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.OnSaleProductJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.OrderSyncJobService">
            <summary>
            本地任务-同步订单.
            </summary>
        </member>
        <member name="F:BPM.Application.OrderSyncJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.OrderSyncJobService._orderService">
            <summary>
            订单服务.
            </summary>
        </member>
        <member name="M:BPM.Application.OrderSyncJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.OrderService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.OrderSyncJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.OrderSyncJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.ProductPriceJobService">
            <summary>
            本地任务-同步商品价格.
            </summary>
        </member>
        <member name="F:BPM.Application.ProductPriceJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.ProductPriceJobService._productService">
            <summary>
            商品服务.
            </summary>
        </member>
        <member name="M:BPM.Application.ProductPriceJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ProductService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.ProductPriceJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductPriceJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.ShopSearchJobService">
            <summary>
            本地任务-搜索门店.
            </summary>
        </member>
        <member name="F:BPM.Application.ShopSearchJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.ShopSearchJobService._shopService">
            <summary>
            门店服务.
            </summary>
        </member>
        <member name="M:BPM.Application.ShopSearchJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ShopService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.ShopSearchJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ShopSearchJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.SyncMemberJobService">
            <summary>
            本地任务-同步会员信息.
            </summary>
        </member>
        <member name="F:BPM.Application.SyncMemberJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.SyncMemberJobService._memberService">
            <summary>
            会员服务.
            </summary>
        </member>
        <member name="M:BPM.Application.SyncMemberJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.MemberService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.SyncMemberJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SyncMemberJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.UpdateProductPriceJobService">
            <summary>
            本地任务-更新商品价格.
            </summary>
        </member>
        <member name="F:BPM.Application.UpdateProductPriceJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.UpdateProductPriceJobService._productService">
            <summary>
            商品服务.
            </summary>
        </member>
        <member name="M:BPM.Application.UpdateProductPriceJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ProductService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.UpdateProductPriceJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.UpdateProductPriceJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.UpdateProductStoreJobService">
            <summary>
            本地任务-更新商品库存.
            </summary>
        </member>
        <member name="F:BPM.Application.UpdateProductStoreJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.UpdateProductStoreJobService._productService">
            <summary>
            商品服务.
            </summary>
        </member>
        <member name="M:BPM.Application.UpdateProductStoreJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.ProductService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.UpdateProductStoreJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.UpdateProductStoreJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.WeChatPayBillJobService">
            <summary>
            本地任务-下载微信支付账单.
            </summary>
        </member>
        <member name="F:BPM.Application.WeChatPayBillJobService._serviceScope">
            <summary>
            服务提供器.
            </summary>
        </member>
        <member name="F:BPM.Application.WeChatPayBillJobService._weChatPayBillService">
            <summary>
            微信支付账单服务.
            </summary>
        </member>
        <member name="M:BPM.Application.WeChatPayBillJobService.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory,BPM.Application.Bill.WeChatPayBillService)">
            <summary>
            构造函数.
            </summary>
        </member>
        <member name="M:BPM.Application.WeChatPayBillJobService.ExecuteAsync(BPM.Schedule.JobExecutingContext,System.Threading.CancellationToken)">
            <summary>
            执行任务.
            </summary>
            <param name="context">上下文.</param>
            <param name="stoppingToken">是否取消.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.WeChatPayBillJobService.Dispose">
            <summary>
            回收.
            </summary>
        </member>
        <member name="T:BPM.Application.MemberService">
            <summary>
            会员管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11
            </summary>
        </member>
        <member name="F:BPM.Application.MemberService._repository">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="M:BPM.Application.MemberService.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            构造函数
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:BPM.Application.MemberService.syncMember">
            <summary>
            同步会员信息
            </summary>
            <returns>同步结果</returns>
        </member>
        <member name="T:BPM.Application.OrderService">
            <summary>
            订单管理服务类
            主要功能：
            1. 从有赞平台批量获取订单数据
            2. 解析和过滤订单信息
            3. 将订单数据保存到本地数据库
            4. 处理订单相关的业务逻辑
            </summary>
            <remarks>
            使用说明：
            - 支持分页获取订单数据
            - 自动处理订单去重
            - 支持手机号解密
            - 支持多租户数据库操作
            - 订单数据包含主订单、订单明细和支付信息三个部分
            </remarks>
        </member>
        <member name="F:BPM.Application.OrderService._db">
            <summary>
            数据库服务实例
            用于处理多租户数据库连接和事务管理
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService._repository">
            <summary>
            订单库数据仓储
            用于处理订单主表数据
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService._repository1">
            <summary>
            订单明细库数据仓储
            用于处理订单商品明细数据
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService._repository2">
            <summary>
            订单支付库数据仓储
            用于处理订单支付信息数据
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService._youzanService">
            <summary>
            有赞服务接口
            用于调用有赞开放平台API
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService.PAGE_SIZE">
            <summary>
            每页数据大小
            用于分页查询订单数据，默认每页100条
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService.ORDER_TYPE">
            <summary>
            订单类型
            NORMAL表示普通订单
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService.MAX_QUERY_DAYS">
            <summary>
            最大查询天数
            限制单次查询的时间范围不超过90天
            </summary>
        </member>
        <member name="F:BPM.Application.OrderService._logger">
            <summary>
            日志记录器
            用于记录服务运行时的关键信息和错误
            </summary>
        </member>
        <member name="M:BPM.Application.OrderService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,Microsoft.Extensions.Logging.ILogger{BPM.Application.OrderService})">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
            <param name="youzanService">有赞服务接口</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:BPM.Application.OrderService.GetTradesSold(System.Object)">
            <summary>
            批量查询订单接口
            </summary>
            <param name="request">查询参数，包含开始时间和结束时间。如果未传入时间，默认查询最近5天的订单</param>
            <returns>订单列表及查询结果信息</returns>
            <remarks>
            功能说明：
            1. 支持分页查询有赞平台订单数据
            2. 自动过滤已支付订单
            3. 支持item_type为0或20的订单
            4. 自动处理手机号解密
            5. 自动保存订单数据到本地数据库
            6. 未传入时间参数时，默认查询最近5天的订单
            
            使用限制：
            - 查询时间范围不能超过90天
            - 时间格式必须为：yyyy-MM-dd HH:mm:ss
            - 开始时间不能大于结束时间
            </remarks>
        </member>
        <member name="M:BPM.Application.OrderService.SaveOrderData(BPM.Domain.Dto.trade.YouzanFullOrderInfo,System.String,System.String)">
            <summary>
            保存订单数据到数据库
            </summary>
            <param name="orderInfo">订单详细信息，包含订单主体、商品明细和支付信息</param>
            <param name="phone">解密后的买家手机号</param>
            <param name="orderNo">订单编号</param>
            <returns>成功返回空字符串，失败返回错误信息</returns>
            <remarks>
            处理流程：
            1. 保存订单主体信息
            2. 保存订单商品明细
            3. 保存订单支付信息
            
            注意事项：
            - 使用事务确保数据一致性
            - 自动处理字段长度限制
            - 自动生成必要的ID和序号
            </remarks>
        </member>
        <member name="T:BPM.Application.GetTradesSoldRequest">
            <summary>
            批量查询订单请求参数类
            </summary>
            <remarks>
            使用说明：
            - 时间格式：yyyy-MM-dd HH:mm:ss
            - 时间范围不超过90天
            - 开始时间必须小于结束时间
            </remarks>
        </member>
        <member name="P:BPM.Application.GetTradesSoldRequest.start_created">
            <summary>
            订单创建开始时间
            </summary>
            <example>2024-01-01 00:00:00</example>
        </member>
        <member name="P:BPM.Application.GetTradesSoldRequest.end_created">
            <summary>
            订单创建结束时间
            </summary>
            <example>2024-01-31 23:59:59</example>
        </member>
        <member name="P:BPM.Application.GetTradesSoldRequest.tid">
            <summary>
            订单号
            </summary>
            <example>E20240101123456789</example>
        </member>
        <member name="T:BPM.Application.ProductService">
            <summary>
            产品管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11
            </summary>
        </member>
        <member name="F:BPM.Application.ProductService._repository">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="F:BPM.Application.ProductService._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="F:BPM.Application.ProductService._cache">
            <summary>
            缓存管理器
            </summary>
        </member>
        <member name="F:BPM.Application.ProductService._db">
            <summary>
            多租户事务.
            </summary>
        </member>
        <member name="M:BPM.Application.ProductService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,BPM.Common.Manager.ICacheManager,BPM.Application.Services.ShopCacheService,Microsoft.Extensions.Options.IOptions{BPM.Application.Configuration.ProductSyncConfiguration},BPM.Application.Services.ProductSyncResultHandler,BPM.Application.Services.ProductBatchProcessor,BPM.Application.Services.ProductCompensationService)">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
            <param name="youzanService">有赞服务</param>
            <param name="cache">缓存管理器</param>
            <param name="shopCacheService">店铺缓存服务</param>
            <param name="configOptions">商品同步配置选项</param>
            <param name="resultHandler">结果处理器</param>
            <param name="batchProcessor">批量处理器</param>
            <param name="compensationService">补偿服务</param>
        </member>
        <member name="M:BPM.Application.ProductService.GetStrategyFactory">
            <summary>
            获取策略工厂（延迟初始化）
            </summary>
        </member>
        <member name="M:BPM.Application.ProductService.getOnSaleProduct(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            获取出售中的商品列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.PreprocessRequestParameters(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            预处理请求参数
            </summary>
            <param name="request">请求参数</param>
        </member>
        <member name="M:BPM.Application.ProductService.ProcessTimeParameters(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            处理时间参数格式转换
            </summary>
            <param name="request">请求参数</param>
        </member>
        <member name="M:BPM.Application.ProductService.ParseTimeParameter(System.String,System.String[],System.String)">
            <summary>
            解析时间参数
            </summary>
            <param name="timeStr">时间字符串</param>
            <param name="supportedFormats">支持的格式</param>
            <param name="paramName">参数名称</param>
            <returns>Unix时间戳</returns>
        </member>
        <member name="M:BPM.Application.ProductService.ValidateRequestParameters(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            验证请求参数有效性
            </summary>
            <param name="request">请求参数</param>
        </member>
        <member name="M:BPM.Application.ProductService.SetDefaultTimeRangeIfNeeded(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            如果需要，设置默认时间范围
            </summary>
            <param name="request">请求参数</param>
        </member>
        <member name="M:BPM.Application.ProductService.HasTimeParameters(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            检查是否有时间参数
            </summary>
            <param name="request">请求参数</param>
            <returns>是否有时间参数</returns>
        </member>
        <member name="M:BPM.Application.ProductService.AutoDivideAndProcessByTime(BPM.Domain.Requests.product.getOnSaleProductRequest,System.Int64,System.Int64)">
            <summary>
            自动递归分割时间段并处理数据
            </summary>
            <param name="originalRequest">原始请求</param>
            <param name="startTime">开始时间戳</param>
            <param name="endTime">结束时间戳</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:BPM.Application.ProductService.RecordError(System.String,System.Int32@,System.Collections.Generic.List{System.String},System.Boolean)">
            <summary>
            记录处理错误，更新计数器并添加错误消息
            </summary>
            <param name="errorMessage">错误消息</param>
            <param name="localFailCount">本地失败计数器引用</param>
            <param name="localErrorData">本地错误数据列表引用</param>
            <param name="addToGlobal">是否同时添加到全局错误列表</param>
        </member>
        <member name="M:BPM.Application.ProductService.ProcessTimeSegment(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            处理时间段内的商品
            </summary>
            <param name="request">时间段请求</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:BPM.Application.ProductService.MergeAndDeduplicate(System.Object,System.Object)">
            <summary>
            合并并去重两个错误列表
            </summary>
        </member>
        <member name="T:BPM.Application.ProductService.TimeSegmentResult">
            <summary>
            时间段处理结果类
            </summary>
        </member>
        <member name="P:BPM.Application.ProductService.TimeSegmentResult.exceed_limit">
            <summary>
            是否超过API限制（4000条）
            </summary>
        </member>
        <member name="P:BPM.Application.ProductService.TimeSegmentResult.total_count">
            <summary>
            当前时间段查询到的数据总量
            </summary>
        </member>
        <member name="M:BPM.Application.ProductService.ConvertToUnixTimestamp(System.DateTime)">
            <summary>
            将 DateTime 转换为 13 位 Unix 时间戳
            </summary>
            <param name="date">DateTime 对象</param>
            <returns>13 位 Unix 时间戳</returns>
        </member>
        <member name="M:BPM.Application.ProductService.ConvertFromUnixTimestamp(System.Int64)">
            <summary>
            将 13 位 Unix 时间戳转换为北京时间 DateTime
            </summary>
            <param name="unixTimestamp">13 位 Unix 时间戳</param>
            <returns>对应的北京时间 DateTime 对象</returns>
        </member>
        <member name="M:BPM.Application.ProductService.updateProductStore">
            <summary>
            更新商品库存
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.UpdateStockStatusForStores(BPM.Domain.Requests.product.updateProductStoreRequest,System.Collections.Generic.List{System.String},System.String,System.String)">
            <summary>
            批量更新指定店铺的商品库存状态
            </summary>
            <param name="stockGroup">包含商品库存更新信息的请求对象</param>
            <param name="storeIds">需要更新状态的店铺ID列表</param>
            <param name="status">要更新的状态值（如："select"表示成功，"add-error"表示失败）</param>
            <param name="message">可选的错误消息，用于记录失败原因</param>
            <returns>异步任务</returns>
            <remarks>
            此方法主要用于商品库存同步过程中，用于标记处理状态。
            成功时标记为"select"，失败时标记为"add-error"并记录错误信息。
            通过调用BatchUpdateStockStatus方法实现批量更新操作。
            </remarks>
        </member>
        <member name="M:BPM.Application.ProductService.ExtractAllStoreIds(BPM.Domain.Requests.product.updateProductStoreRequest)">
            <summary>
            从请求中提取所有店铺ID
            </summary>
        </member>
        <member name="M:BPM.Application.ProductService.getGroupedStockList(BPM.Domain.Queries.product.productStoreQuery)">
            <summary>
            获取分组后的库存列表
            </summary>
        </member>
        <member name="M:BPM.Application.ProductService.BatchUpdateStockStatus(System.Collections.Generic.List{System.ValueTuple{System.Int64,System.Int64,System.Collections.Generic.List{System.String}}},System.String,System.String)">
            <summary>
            批量更新库存状态
            </summary>
        </member>
        <member name="M:BPM.Application.ProductService.updateProductPrice">
            <summary>
            更新商品价格
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.getBatchProduct">
            <summary>
            获取批量商家编码的商品列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.getDetailProduct">
            <summary>
            获取商品详情
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.getYzProductSkuList(System.String,System.Int32)">
            <summary>
            获取有赞商品多规格SKU - 单个商品使用批量方法优化实现
            </summary>
            <param name="item_id">商品ID</param>
            <param name="channel">渠道</param>
            <returns>SKU列表</returns>
        </member>
        <member name="M:BPM.Application.ProductService.getYzProductSkuBatchList(System.Collections.Generic.List{System.String},System.Int32)">
            <summary>
            获取有赞商品多规格SKU - 支持批量处理
            </summary>
            <param name="item_ids">商品ID列表</param>
            <param name="channel">渠道</param>
            <returns>每个商品的SKU列表字典</returns>
        </member>
        <member name="M:BPM.Application.ProductService.getYzNoProductSkuList(System.String,System.Int32)">
            <summary>
            获取有赞商品无规格SKU
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.getYzNoProductSkuList(System.Collections.Generic.List{System.String},System.Int32)">
            <summary>
            获取有赞商品无规格SKU - 支持批量处理
            </summary>
            <param name="item_no_list">商品编码列表</param>
            <param name="channel">渠道</param>
            <returns>商品的SKU列表</returns>
        </member>
        <member name="M:BPM.Application.ProductService.getBatchYzProductSkuList(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            批量获取多个商品的SKU信息
            </summary>
            <param name="itemIdList">商品ID列表</param>
            <param name="channel">渠道</param>
            <returns>以商品ID为键，SKU列表为值的字典</returns>
        </member>
        <member name="M:BPM.Application.ProductService.createProduct(BPM.Domain.Entity.product.productEntity,System.Collections.Generic.List{BPM.Domain.Entity.product.productSkuEntity})">
            <summary>
            同步商品记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.batchCreateProducts(System.Collections.Generic.List{BPM.Domain.Entity.product.productEntity},System.Collections.Generic.List{BPM.Domain.Entity.product.productSkuEntity})">
            <summary>
            批量同步商品记录，提高处理效率
            </summary>
            <param name="products">商品列表</param>
            <param name="productSkus">商品SKU列表</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.removeProduct(System.String)">
            <summary>
            删除商品记录及相关SKU数据
            </summary>
            <param name="product_id">商品编号</param>
            <returns>删除是否成功</returns>
        </member>
        <member name="M:BPM.Application.ProductService.getPageUpdateStoreStockList(BPM.Domain.Queries.product.productStoreQuery)">
            <summary>
            获取商品库存列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.modifySyncStoreStock(System.Int64,System.Int64,System.String,System.String,System.String)">
            <summary>
             修改库存同步标记
            </summary>
            <param name="product_id">商品id</param>
            <param name="sku_id">规格id</param>
            <param name="store_id">门店id</param>
            <param name="tag_status">同步状态</param>
            <param name="tag_body">内容</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.getPageUpdateProductPriceList(BPM.Domain.Queries.product.productPriceQuery)">
            <summary>
            获取商品单价列表
            </summary>
            <param name="query">查询参数</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.modifySyncPriceTag(System.String,System.Int64,System.String,System.String)">
            <summary>
            更新同步价格标记
            </summary>
            <param name="sku_id">规格id</param>
            <param name="tag_status">标记状态</param>
            <param name="tag_body">标记内容</param>
            <returns></returns>
            <summary>
            更新同步价格标记
            </summary>
            <param name="sku_id">规格id</param>
            <param name="tag_status">标记状态</param>
            <param name="tag_body">标记内容</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.GetOptimalCacheTime(BPM.Domain.Entity.product.productEntity)">
            <summary>
            计算商品的最佳缓存时间
            </summary>
            <param name="product">商品实体</param>
            <returns>缓存过期时间</returns>
        </member>
        <member name="M:BPM.Application.ProductService.CompleteIncompleteProductsApi">
            <summary>
            补全只有单条记录的商品资料（缺少门店或网店版本）
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.ProductService.CompleteIncompleteProductsWithLimitApi(BPM.Application.DTOs.CompleteProductsRequest)">
            <summary>
            补全只有单条记录的商品资料（可配置处理数量）
            </summary>
            <param name="request">请求参数，包含最大处理数量</param>
            <returns></returns>
        </member>
        <member name="T:BPM.Application.Services.IProductSyncStrategy">
            <summary>
            商品同步策略接口
            </summary>
        </member>
        <member name="M:BPM.Application.Services.IProductSyncStrategy.SyncProductsAsync(BPM.Domain.Requests.product.getOnSaleProductRequest,BPM.Application.Services.ProductSyncResultHandler)">
            <summary>
            同步商品数据
            </summary>
            <param name="request">请求参数</param>
            <param name="resultHandler">结果处理器</param>
            <returns>同步结果</returns>
        </member>
        <member name="M:BPM.Application.Services.IProductSyncStrategy.CanHandle(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            是否支持该请求类型
            </summary>
            <param name="request">请求参数</param>
            <returns>是否支持</returns>
        </member>
        <member name="T:BPM.Application.Services.TimeRangeSyncStrategy">
            <summary>
            时间范围同步策略
            </summary>
        </member>
        <member name="T:BPM.Application.Services.DirectSyncStrategy">
            <summary>
            直接同步策略
            </summary>
        </member>
        <member name="T:BPM.Application.Services.ProductSyncStrategyFactory">
            <summary>
            商品同步策略工厂抽象类
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncStrategyFactory.GetStrategy(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            获取适合的策略
            </summary>
            <param name="request">请求参数</param>
            <returns>同步策略</returns>
        </member>
        <member name="T:BPM.Application.Services.ProductBatchProcessor">
            <summary>
            商品批量处理器
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.BatchResyncStoreProductsAsync(System.Collections.Generic.IEnumerable{System.String},System.Func{BPM.Domain.Requests.product.getOnSaleProductRequest,System.Threading.Tasks.Task{System.Object}})">
            <summary>
            批量重新同步门店商品
            </summary>
            <param name="barcodes">条码列表</param>
            <param name="processProductsFunc">处理商品的函数</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.BatchCompleteProductsAsync(System.Collections.Generic.IEnumerable{System.String},System.Func{BPM.Domain.Requests.product.getOnSaleProductRequest,System.Threading.Tasks.Task{System.Object}})">
            <summary>
            批量补全商品
            </summary>
            <param name="barcodes">条码列表</param>
            <param name="processProductsFunc">处理商品的函数</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.ProcessBatchAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.SemaphoreSlim,System.Func{BPM.Domain.Requests.product.getOnSaleProductRequest,System.Threading.Tasks.Task{System.Object}},System.String)">
            <summary>
            处理单个批次
            </summary>
            <param name="batch">批次数据</param>
            <param name="semaphore">信号量</param>
            <param name="processProductsFunc">处理函数</param>
            <param name="operationName">操作名称</param>
            <returns>处理任务</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.CreateBatches``1(System.Collections.Generic.IEnumerable{``0},System.Int32)">
            <summary>
            创建批次
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="source">源数据</param>
            <param name="batchSize">批次大小</param>
            <returns>批次集合</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.ProcessInParallelAsync``1(System.Collections.Generic.IEnumerable{System.String},System.Func{System.String,System.Threading.Tasks.Task{``0}},System.String)">
            <summary>
            并行处理多个条码
            </summary>
            <param name="barcodes">条码列表</param>
            <param name="processFunc">处理函数</param>
            <param name="operationName">操作名称</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.FilterConflictingBarcodesAsync(System.Collections.Generic.List{System.String},System.String)">
            <summary>
            过滤冲突的条码，避免重复处理
            </summary>
            <param name="barcodes">条码列表</param>
            <param name="operationName">操作名称</param>
            <returns>过滤后的条码列表</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductBatchProcessor.IsBarCodeProcessingAsync(System.String)">
            <summary>
            检查条码是否正在被处理
            </summary>
            <param name="barcode">条码</param>
            <returns>是否正在处理</returns>
        </member>
        <member name="T:BPM.Application.Services.ProductCompensationService">
            <summary>
            商品补偿服务
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ProductCompensationService.ExecuteCompensationAsync(System.Func{BPM.Domain.Requests.product.getOnSaleProductRequest,System.Threading.Tasks.Task{System.Object}})">
            <summary>
            执行补偿逻辑
            </summary>
            <param name="processProductsFunc">处理商品的函数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductCompensationService.ExecuteProductCompletionAsync(System.Func{BPM.Domain.Requests.product.getOnSaleProductRequest,System.Threading.Tasks.Task{System.Object}})">
            <summary>
            执行商品补全
            </summary>
            <param name="processProductsFunc">处理商品的函数</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductCompensationService.GetIncompleteProductsAsync(System.Int32)">
            <summary>
            获取不完整的商品列表
            </summary>
            <param name="maxCount">最大数量</param>
            <returns>不完整的商品列表</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductCompensationService.GetExistingProductsAsync(System.Collections.Generic.List{System.String})">
            <summary>
            获取现有商品列表
            </summary>
            <param name="barCodes">条码列表</param>
            <returns>现有商品列表</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductCompensationService.ShouldExecuteCompensation(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            检查是否需要执行补偿逻辑
            </summary>
            <param name="request">请求参数</param>
            <returns>是否需要执行</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductCompensationService.GetCompensationStatistics">
            <summary>
            获取补偿统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="T:BPM.Application.Services.ProductSyncResultHandler">
            <summary>
            商品同步结果处理器
            </summary>
        </member>
        <member name="P:BPM.Application.Services.ProductSyncResultHandler.Succeed">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:BPM.Application.Services.ProductSyncResultHandler.Fail">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:BPM.Application.Services.ProductSyncResultHandler.ErrorDataList">
            <summary>
            错误数据列表
            </summary>
        </member>
        <member name="P:BPM.Application.Services.ProductSyncResultHandler.DeletedStoreBarcodes">
            <summary>
            被删除的门店商品条码列表
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.Reset">
            <summary>
            重置计数器
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.AddSucceed(System.Int32)">
            <summary>
            添加成功数量
            </summary>
            <param name="count">数量</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.AddFail(System.Int32)">
            <summary>
            添加失败数量
            </summary>
            <param name="count">数量</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.AddError(System.String)">
            <summary>
            添加错误信息
            </summary>
            <param name="error">错误信息</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.AddErrors(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            添加错误信息列表
            </summary>
            <param name="errors">错误信息列表</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.AddDeletedStoreBarcode(System.String)">
            <summary>
            添加被删除的门店商品条码
            </summary>
            <param name="barcode">条码</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.Merge(BPM.Application.Services.ProductSyncResultHandler)">
            <summary>
            合并其他结果
            </summary>
            <param name="other">其他结果处理器</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.MergeResult(System.Object)">
            <summary>
            合并结果DTO
            </summary>
            <param name="result">结果DTO</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.GetFinalResult">
            <summary>
            获取最终结果
            </summary>
            <returns>结果DTO</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.LogStatistics(System.String)">
            <summary>
            记录处理统计
            </summary>
            <param name="operation">操作名称</param>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.SafeExecuteAsync(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            安全执行操作并处理异常
            </summary>
            <param name="operation">操作</param>
            <param name="operationName">操作名称</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncResultHandler.SafeExecuteAsync``1(System.Func{System.Threading.Tasks.Task{``0}},System.String,``0)">
            <summary>
            安全执行操作并处理异常（带返回值）
            </summary>
            <typeparam name="T">返回类型</typeparam>
            <param name="operation">操作</param>
            <param name="operationName">操作名称</param>
            <param name="defaultValue">默认值</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:BPM.Application.Services.ProductSyncStrategyFactoryImpl">
            <summary>
            商品同步策略工厂实现
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncStrategyFactoryImpl.GetStrategy(BPM.Domain.Requests.product.getOnSaleProductRequest)">
            <summary>
            获取适合的策略
            </summary>
            <param name="request">请求参数</param>
            <returns>同步策略</returns>
        </member>
        <member name="M:BPM.Application.Services.ProductSyncStrategyFactoryImpl.InitializeStrategies">
            <summary>
            初始化策略列表
            </summary>
        </member>
        <member name="T:BPM.Application.Services.ShopCacheService">
            <summary>
            店铺缓存服务
            </summary>
        </member>
        <member name="T:BPM.Application.Services.ShopCacheService.CacheKeys">
            <summary>
            缓存相关常量
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ShopCacheService.#ctor(SqlSugar.ISqlSugarClient,BPM.Common.Manager.ICacheManager)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ShopCacheService.LoadAllShopsToCache">
            <summary>
            获取所有店铺信息并缓存
            </summary>
        </member>
        <member name="M:BPM.Application.Services.ShopCacheService.GetShopFromCache(System.String)">
            <summary>
            从缓存获取单个店铺信息
            </summary>
            <param name="netSourceNo">店铺网络来源编号</param>
            <returns>店铺实体</returns>
        </member>
        <member name="M:BPM.Application.Services.ShopCacheService.GetSourceShopFromCache(System.String)">
            <summary>
            从缓存获取单个店铺信息
            </summary>
            <param name="sourceNo">来源编号</param>
            <returns>店铺实体</returns>
        </member>
        <member name="M:BPM.Application.Services.ShopCacheService.GetStoreIdFromCache(System.Int64)">
            <summary>
            根据店铺编号获取店铺ID
            </summary>
            <param name="sourceNo">店铺编号</param>
            <returns>店铺ID</returns>
        </member>
        <member name="M:BPM.Application.Services.ShopCacheService.GetShopsFromCache(System.Collections.Generic.List{System.String})">
            <summary>
            批量从缓存获取店铺信息
            </summary>
            <param name="netSourceNos">店铺网络来源编号列表</param>
            <returns>店铺信息列表</returns>
        </member>
        <member name="T:BPM.Application.ShopService">
            <summary>
            门店管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11
            </summary>
        </member>
        <member name="F:BPM.Application.ShopService._repository">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="F:BPM.Application.ShopService._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="F:BPM.Application.ShopService._db">
            <summary>
            多租户事务.
            </summary>
        </member>
        <member name="F:BPM.Application.ShopService._cache">
            <summary>
            缓存管理.
            </summary>
        </member>
        <member name="M:BPM.Application.ShopService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,BPM.Common.Manager.ICacheManager)">
            <summary>
            构造函数
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:BPM.Application.ShopService.getShopList">
            <summary>
            获取门店列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:BPM.Application.SubscriberService">
            <summary>
            订阅管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11.
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._repository">
            <summary>
             服务提供.
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._repository1">
            <summary>
             服务提供.
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._db">
            <summary>
            多租户事务.
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._eventPublisher">
            <summary>
            事件总线.
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._cache">
            <summary>
            缓存管理器
            </summary>
        </member>
        <member name="F:BPM.Application.SubscriberService._orderService">
            <summary>
            订单服务
            </summary>
        </member>
        <member name="M:BPM.Application.SubscriberService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService,BPM.EventBus.IEventPublisher,BPM.Common.Manager.ICacheManager,BPM.Application.OrderService)">
            <summary>
            构造函数.
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:BPM.Application.SubscriberService.Push(System.Object)">
            <summary>
            订阅消息推送.
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SubscriberService.ProcessPendingCustomerUpdates(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            处理待处理的客户更新
            </summary>
        </member>
        <member name="M:BPM.Application.SubscriberService.PonitsEvent(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            积分事件.
            </summary>
            <param name="pushRequest">请求参数.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SubscriberService.AuthMobile(BPM.Domain.Requests.subscriber.authMobileRequest)">
            <summary>
            手机号更新事件处理.
            </summary>
            <param name="pushRequest">请求参数.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SubscriberService.TradeEventNew(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            交易支付事件(新版本).
            </summary>
            <param name="pushRequest">请求参数.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SubscriberService.TradeEvent(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            交易支付事件.
            </summary>
            <param name="pushRequest">请求参数.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SubscriberService.RefundEvent(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            退货事件.
            </summary>
            <param name="pushRequest">请求参数.</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Application.SubscriberService.CustomerCardEvent(BPM.Domain.Requests.subscriber.pushRequest)">
            <summary>
            客户权益卡订阅事件处理方法.
            </summary>
            <remarks>
            本方法处理有赞平台推送的会员卡、权益卡相关事件，包括：
            1. 卡片发放/购买 (CUSTOMER_CARD_GIVEN/CUSTOMER_CARD_BOUGHT)
            2. 卡片激活 (CUSTOMER_CARD_ACTIVATED)
            3. 卡片延期 (CUSTOMER_CARD_EXTENSION)
            4. 卡片禁用/删除 (CUSTOMER_CARD_DELETED)
            5. 第三方发卡 (CUSTOMER_CARD_GRANTED_FROM_OPEN)
            
            优化说明：
            - 所有数据库操作都采用直接执行方式，不使用事务
            - 每个操作都是独立的，不需要保证原子性
            - 提高了性能并减少了数据库资源占用
            </remarks>
            <param name="pushRequest">有赞推送的请求数据</param>
            <returns>处理结果，空字符串表示成功，否则返回错误信息</returns>
        </member>
        <member name="T:BPM.Application.UserService">
            <summary>
            会员管理
            版 本：V3.6
            版 权：BPM信息技术有限公司
            作 者：Aarons
            日 期：2024-09-11
            </summary>
        </member>
        <member name="F:BPM.Application.UserService._repository">
            <summary>
             服务提供
            </summary>
        </member>
        <member name="F:BPM.Application.UserService._youzanService">
            <summary>
            有赞服务接口
            </summary>
        </member>
        <member name="M:BPM.Application.UserService.#ctor(SqlSugar.ISqlSugarClient,BPM.Extras.Youzan.Services.Abstractions.IYouzanService)">
            <summary>
            构造函数
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:BPM.Application.UserService.getUserList">
            <summary>
            更新客户
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
