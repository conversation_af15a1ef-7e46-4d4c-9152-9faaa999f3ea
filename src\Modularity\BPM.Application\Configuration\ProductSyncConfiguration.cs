using BPM.DependencyInjection;
using BPM.ConfigurableOptions;

namespace BPM.Application.Configuration;

/// <summary>
/// 商品同步配置
/// </summary>
[SuppressSniffer]
public class ProductSyncConfiguration : IConfigurableOptions
{
    /// <summary>
    /// 最大页大小
    /// </summary>
    public int MaxPageSize { get; set; } = 200;

    /// <summary>
    /// 最大页码限制
    /// </summary>
    public int MaxPageNo { get; set; } = 500;

    /// <summary>
    /// 最大数据量阈值
    /// </summary>
    public int MaxDataThreshold { get; set; } = 10000;

    /// <summary>
    /// 最小数据量阈值
    /// </summary>
    public int MinDataThreshold { get; set; } = 200;

    /// <summary>
    /// 批量处理大小
    /// </summary>
    public int BatchSize { get; set; } = 50;

    /// <summary>
    /// 商品补全最大处理数量
    /// </summary>
    public int CompleteMaxProcessCount { get; set; } = 100;

    /// <summary>
    /// 商品补全批次大小
    /// </summary>
    public int CompleteBatchSize { get; set; } = 10;

    /// <summary>
    /// API调用延迟（毫秒）
    /// </summary>
    public int ApiCallDelay { get; set; } = 1000;

    /// <summary>
    /// 批次间延迟（毫秒）
    /// </summary>
    public int BatchDelay { get; set; } = 2000;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public int RetryDelay { get; set; } = 5000;

    /// <summary>
    /// 是否启用商品补全
    /// </summary>
    public bool EnableProductCompletion { get; set; } = true;

    /// <summary>
    /// 是否启用门店商品重新同步
    /// </summary>
    public bool EnableStoreProductResync { get; set; } = true;

    /// <summary>
    /// 并行处理最大线程数
    /// </summary>
    public int MaxParallelism { get; set; } = 5;

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    public int CacheExpirationMinutes { get; set; } = 30;

    /// <summary>
    /// 批次间延迟时间（毫秒）
    /// </summary>
    public int DelayBetweenBatches { get; set; } = 100;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// 重试延迟时间（秒）
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// 是否启用批量处理
    /// </summary>
    public bool EnableBatchProcessing { get; set; } = true;

    /// <summary>
    /// 是否启用并行处理
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 默认时间范围天数
    /// </summary>
    public int DefaultTimeRangeDays { get; set; } = 5;

    /// <summary>
    /// 启用冲突检测，bool 类型，默认true，是否启用条码冲突检测机制
    /// </summary>
    public bool EnableConflictDetection { get; set; } = true;

    /// <summary>
    /// 冲突检测时间窗口，int 类型，单位分钟，默认5，在此时间窗口内的条码被认为可能存在冲突
    /// </summary>
    public int ConflictDetectionWindowMinutes { get; set; } = 5;
}
