﻿@if(Model.IsUploading)
{
@:using BPM.Common.Models;
}

namespace BPM.@(Model.NameSpace).<EMAIL>;
 
/// <summary>
/// @(Model.BusName)输出参数.
/// </summary>
public class @(Model.ClassName)InfoOutput
{
@foreach (var column in Model.TableField){
@if(column.bpmKey != null)
{
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "usersSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<string> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "int" : "string") bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
@if(column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<List<string>> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
else
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<@(column.NetType == "int?" ? "int" : "string")> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
}
break;
case "checkbox":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<@(column.NetType == "int?" ? "int" : "string")> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
case "radio":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @(column.NetType == "int?" ? "int" : "string") bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
case "uploadImg":
case "uploadFile":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public List<FileControlsModel> bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType bpm_@(column.TableName)<EMAIL> { get; set; }
@:
break;
}
}
}
}