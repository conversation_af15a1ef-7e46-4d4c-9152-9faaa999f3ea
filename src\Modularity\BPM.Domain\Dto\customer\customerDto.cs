﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.customer;

/// <summary>
/// 客户信息 - API返回结构
/// </summary>
[SuppressSniffer]
public class customerDto
{
    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 所属门店
    /// </summary>
    public long ascription_kdt_id { get; set; }

    /// <summary>
    /// 来源门店ID
    /// </summary>
    public long member_source_kdt_id { get; set; }

    /// <summary>
    /// 会员卡列表
    /// </summary>
    public object[] cards { get; set; }

    /// <summary>
    /// 性别（0：未知；1：男；2：女）
    /// </summary>
    public int gender { get; set; }

    /// <summary>
    /// 来源渠道
    /// </summary>
    public int source_channel { get; set; }

    /// <summary>
    /// 等级信息
    /// </summary>
    public object[] level_infos { get; set; }

    /// <summary>
    /// 会员来源渠道描述
    /// </summary>
    public string member_source_channel_desc { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string show_name { get; set; }

    /// <summary>
    /// 最新昵称
    /// </summary>
    public string latest_nickname { get; set; }

    /// <summary>
    /// 最新头像
    /// </summary>
    public string latest_avatar { get; set; }

    /// <summary>
    /// 微信头像
    /// </summary>
    public string wx_avatar { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string mobile { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public long created_at { get; set; }

    /// <summary>
    /// 会员创建时间
    /// </summary>
    public long member_created_at { get; set; }

    /// <summary>
    /// 有赞开放ID
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 区县名
    /// </summary>
    public string county_name { get; set; }

    /// <summary>
    /// 手机国家代码
    /// </summary>
    public string mobile_country_code { get; set; }

    /// <summary>
    /// 来源渠道描述
    /// </summary>
    public string source_channel_desc { get; set; }

    /// <summary>
    /// 省份名
    /// </summary>
    public string province_name { get; set; }

    /// <summary>
    /// 标签列表
    /// </summary>
    public object[] tags { get; set; }

    /// <summary>
    /// 区域代码
    /// </summary>
    public int areaCode { get; set; }

    /// <summary>
    /// 城市名
    /// </summary>
    public string city_name { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public long updated_at { get; set; }

    /// <summary>
    /// 来源渠道
    /// </summary>
    public int member_source_channel { get; set; }

    /// <summary>
    /// 权限列表
    /// </summary>
    public object[] rights { get; set; }

    /// <summary>
    /// 微信昵称
    /// </summary>
    public string wx_nickname { get; set; }

    /// <summary>
    /// 客户属性信息
    /// </summary>
    public object[] customer_attrInfos { get; set; }
}
 
/// <summary>
/// 客户推送信息 - 推送消息结构
/// </summary>
[SuppressSniffer]
public class customerPushDto
{
    /// <summary>
    /// 帐号类型
    /// </summary>
    public string account_type { get; set; }

    /// <summary>
    /// 帐号ID
    /// </summary>
    public string account_id { get; set; }

    /// <summary>
    /// 客户来源
    /// </summary>
    public int src { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string mobile { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; }

    /// <summary>
    /// 判断账号是否被注销
    /// </summary>
    public bool is_log_off { get; set; }
}
