﻿namespace BPM.@(Model.NameSpace).<EMAIL>;

/// <summary>
/// @(Model.BusName)输入参数.
/// </summary>
public class @(Model.ClassName)ListOutput
{
@foreach (var column in Model.TableField)
{
@if (column.PrimaryKey){
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
}
else if (column.IsShow)
{
switch(column.bpmKey)
{
case "createTime":
case "modifyTime":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_name { get; set; }
@:
break;
case "uploadFile":
case "uploadImg":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public object? @(column.LowerColumnName)_name { get; set; }
@:
break;
case "timePicker":
case "input":
case "textarea":
case "billRule":
case "datePicker":
case "switch":
case "autoComplete":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_name { get; set; }
@:
break;
case "modifyUser":
case "createUser":
case "currPosition":
case "currOrganize":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_name { get; set; }
@:
break;
case "select":
case "radio":
case "treeSelect":
@switch(column.ControlsDataType)
{
case "dictionary":
@if(!column.IsMultiple)
{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_name { get; set; }
@:
}else{
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName) { get; set; }
@:
}
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName) { get; set; }
@:
break;
}
break;
case "inputNumber":
case "popupSelect":
case "rate":
case "slider":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(column.LowerColumnName)_name { get; set; }
@:
break;
case "relationForm":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(column.LowerColumnName)_name { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(column.LowerColumnName)_id { get; set; }
@:
break;
case "depSelect":
case "posSelect":
case "userSelect":
case "usersSelect":
case "roleSelect":
case "groupSelect":
case "popupTableSelect":
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_name { get; set; }
@:
break;
case "popupAttr":
case "relationFormAttr":
@switch(column.isStorage){
case true:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @(column.ColumnComment)存储字段.
    @:/// </summary>
    @:public string @(column.LowerColumnName)_name { get; set; }
@:
break;
}
break;
default:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @column.LowerColumnName { get; set; }
@:
    @:/// <summary>
    @:/// @column.ColumnComment.
    @:/// </summary>
    @:public @column.NetType @(column.LowerColumnName)_name { get; set; }
@:
break;
}
}
}
@if(Model.ConcurrencyLock)
{
    @:/// <summary>
    @:/// 乐观锁.
    @:/// </summary>
    @:public long version { get; set; }
@:
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
    @:/// <summary>
    @:/// 流程真实ID.
    @:/// </summary>
    @:public string flowTaskId { get; set; }
@:
}
@if(Model.EnableFlow)
{
    @:/// <summary>
    @:/// 流程状态.
    @:/// </summary>
    @:public int flowState { get; set; } = 0;
@:
    @:/// <summary>
    @:/// 流程ID.
    @:/// </summary>
    @:public string flowId { get; set; }
@:
}
}