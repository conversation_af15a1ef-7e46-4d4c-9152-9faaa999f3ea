﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.product;

/// <summary>
/// 在售商品响应
/// </summary>
[SuppressSniffer]
public class batchProcuctResponse
{
    /// <summary>
    /// 
    /// </summary>
    public int count { get; set; }

    /// <summary>
    /// 商品信息集合
    /// </summary>
    public List<ItemsItem> items { get; set; }

    /// <summary>
    ///  sku扩展属性
    /// </summary>
    public class SkuExtensionAttributesItem
    {
        /// <summary>
        /// 店铺ID
        /// </summary>
        public string kdt_id { get; set; }
        /// <summary>
        /// 商品id
        /// </summary>
        public string item_id { get; set; }
        /// <summary>
        ///  skuId
        /// </summary>
        public string sku_id { get; set; }
    }



    public class ItemsItem
    {
        /// <summary>
        /// 商品详情链接
        /// </summary>
        public string detail_url { get; set; }
        /// <summary>
        /// 商品价格和price值一致，单位:分
        /// </summary>
        public long share_detail { get; set; }
        /// <summary>
        /// 运费 单位：分
        /// </summary>
        public long post_fee { get; set; }
        /// <summary>
        ///  商品类型： 0—普通商品 3—UMP降价拍 5—外卖商品 10—分销商品 20—会员卡商品 21—礼品卡商品 22—团购券 25—批发商品 30—收银台商品 31—知识付费商品 35—酒店商品 40—美业商品 60—虚拟商品 61—电子卡券
        /// </summary>
        public int item_type { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int num { get; set; }
        /// <summary>
        /// 划线价 单位：元
        /// </summary>
        public string origin { get; set; }
        /// <summary>
        /// 店铺渠道类型0 - 网店 1 - 门店
        /// </summary>
        public int channel { get; set; }
        /// <summary>
        /// 派克钢笔
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 商品编码，商家可以自定义参数，支持英文和数据组合。商家为商品设置的外部编号，可与商家外部系统对接
        /// </summary>
        public string item_no { get; set; }
        /// <summary>
        /// 最后更新时间 格式"yyyy-MM-dd HH:mm:ss"
        /// </summary>
        public string update_time { get; set; }
        /// <summary>
        ///  商品价格 单位:分
        /// </summary>
        public long price { get; set; }
        /// <summary>
        /// 商品别名 微商城店铺下商品唯一标识
        /// </summary>
        public string @alias { get; set; }
        /// <summary>
        /// 运费类型，1是统一运费，2是运费模板
        /// </summary>
        public int post_type { get; set; }
        /// <summary>
        /// 商品SPU维度的条形码，用于扫码快速搜索商品，使用场景如：扫码购、扫码搜索商品（仅支持实物商品）
        /// </summary>
        public string barcode { get; set; }
        /// <summary>
        ///  创建时间，格式"yyyy-MM-dd HH:mm:ss"
        /// </summary>
        public string created_time { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int quantity { get; set; }
        /// <summary>
        /// 总库存
        /// </summary>
        public List<SkuExtensionAttributesItem> skuExtensionAttributes { get; set; }
        /// <summary>
        ///  商品id
        /// </summary>
        public string item_id { get; set; }

        /// <summary>
        /// 实际总库存，当为门店渠道的时候，quantity会扩大1000倍，而网店不会，该字段会自动处理单位转换成合理单位
        /// </summary>
        public string actual_quantity { get; set; }

        /// <summary>
        /// 有赞连锁总部店铺id
        /// </summary>
        public int root_kdt_id { get; set; }
    }
}
