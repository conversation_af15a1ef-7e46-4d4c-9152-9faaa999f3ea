using BPM.DependencyInjection;

namespace BPM.Application.Services;

/// <summary>
/// 商品同步结果处理器
/// </summary>
[SuppressSniffer]
public class ProductSyncResultHandler
{
    private int _succeed = 0;
    private int _fail = 0;
    private readonly List<string> _errorDataList = new();
    private readonly List<string> _deletedStoreBarcodes = new();

    /// <summary>
    /// 成功数量
    /// </summary>
    public int Succeed => _succeed;

    /// <summary>
    /// 失败数量
    /// </summary>
    public int Fail => _fail;

    /// <summary>
    /// 错误数据列表
    /// </summary>
    public List<string> ErrorDataList => _errorDataList;

    /// <summary>
    /// 被删除的门店商品条码列表
    /// </summary>
    public List<string> DeletedStoreBarcodes => _deletedStoreBarcodes;

    /// <summary>
    /// 重置计数器
    /// </summary>
    public void Reset()
    {
        _succeed = 0;
        _fail = 0;
        _errorDataList.Clear();
        _deletedStoreBarcodes.Clear();
    }

    /// <summary>
    /// 添加成功数量
    /// </summary>
    /// <param name="count">数量</param>
    public void AddSucceed(int count)
    {
        _succeed += count;
    }

    /// <summary>
    /// 添加失败数量
    /// </summary>
    /// <param name="count">数量</param>
    public void AddFail(int count)
    {
        _fail += count;
    }

    /// <summary>
    /// 添加错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    public void AddError(string error)
    {
        if (!string.IsNullOrEmpty(error))
        {
            _errorDataList.Add(error);
        }
    }

    /// <summary>
    /// 添加错误信息列表
    /// </summary>
    /// <param name="errors">错误信息列表</param>
    public void AddErrors(IEnumerable<string> errors)
    {
        if (errors != null)
        {
            _errorDataList.AddRange(errors.Where(e => !string.IsNullOrEmpty(e)));
        }
    }

    /// <summary>
    /// 添加被删除的门店商品条码
    /// </summary>
    /// <param name="barcode">条码</param>
    public void AddDeletedStoreBarcode(string barcode)
    {
        if (!string.IsNullOrEmpty(barcode))
        {
            _deletedStoreBarcodes.Add(barcode);
        }
    }

    /// <summary>
    /// 合并其他结果
    /// </summary>
    /// <param name="other">其他结果处理器</param>
    public void Merge(ProductSyncResultHandler other)
    {
        if (other != null)
        {
            _succeed += other._succeed;
            _fail += other._fail;
            _errorDataList.AddRange(other._errorDataList);
            _deletedStoreBarcodes.AddRange(other._deletedStoreBarcodes);
        }
    }

    /// <summary>
    /// 合并结果DTO
    /// </summary>
    /// <param name="result">结果DTO</param>
    public void MergeResult(dynamic result)
    {
        if (result != null)
        {
            _succeed += result.succeed ?? 0;
            _fail += result.fail ?? 0;
            
            if (result.fail_data is List<string> errorList)
            {
                _errorDataList.AddRange(errorList);
            }
        }
    }

    /// <summary>
    /// 获取最终结果
    /// </summary>
    /// <returns>结果DTO</returns>
    public dynamic GetFinalResult()
    {
        // 去重错误数据
        var uniqueErrors = _errorDataList.Distinct().ToList();

        Console.WriteLine($"商品同步完成 - 成功: {_succeed}, 失败: {_fail}, 错误数量: {uniqueErrors.Count}");

        return new
        {
            succeed = _succeed,
            fail = _fail,
            fail_data = uniqueErrors
        };
    }

    /// <summary>
    /// 记录处理统计
    /// </summary>
    /// <param name="operation">操作名称</param>
    public void LogStatistics(string operation)
    {
        Console.WriteLine($"{operation} - 成功: {_succeed}, 失败: {_fail}, 错误数量: {_errorDataList.Count}");
    }

    /// <summary>
    /// 安全执行操作并处理异常
    /// </summary>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>是否成功</returns>
    public async Task<bool> SafeExecuteAsync(Func<Task> operation, string operationName)
    {
        try
        {
            await operation();
            return true;
        }
        catch (Exception ex)
        {
            var errorMessage = $"{operationName}失败: {ex.Message}";
            Console.WriteLine($"错误: {errorMessage}");
            AddError(errorMessage);
            AddFail(1);
            return false;
        }
    }

    /// <summary>
    /// 安全执行操作并处理异常（带返回值）
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>操作结果</returns>
    public async Task<T> SafeExecuteAsync<T>(Func<Task<T>> operation, string operationName, T defaultValue = default)
    {
        try
        {
            return await operation();
        }
        catch (Exception ex)
        {
            var errorMessage = $"{operationName}失败: {ex.Message}";
            Console.WriteLine($"错误: {errorMessage}");
            AddError(errorMessage);
            AddFail(1);
            return defaultValue;
        }
    }
}
