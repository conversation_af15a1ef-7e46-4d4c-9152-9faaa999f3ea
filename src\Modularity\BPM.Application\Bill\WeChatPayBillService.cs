﻿using BPM.DatabaseAccessor;
using BPM.Domain.Dto.shop;
using BPM.Domain.Entity.shop;
using BPM.LinqBuilder;
using Essensoft.Paylink.WeChatPay;
using Essensoft.Paylink.WeChatPay.V2;
using Essensoft.Paylink.WeChatPay.V2.Request;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace BPM.Application.Bill;

/// <summary>
/// 微信对账单服务
/// </summary>
[ApiDescriptionSettings(Tag = "Bill", Name = "Bill", Order = 215)]
[Route("api/bill")]
public class WeChatPayBillService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 微信支付客户端
    /// </summary>
    private readonly IWeChatPayClient _weChatPayClient;

    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly ISqlSugarClient _sugarClient;

    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    private readonly ILogger<WeChatPayBillService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public WeChatPayBillService(
        IWeChatPayClient weChatPayClient, 
        ISqlSugarClient sugarClient,
        ILogger<WeChatPayBillService> logger)
    {
        _weChatPayClient = weChatPayClient;
        _sugarClient = sugarClient;
        _logger = logger;
    }

    /// <summary>
    /// 下载微信对账单
    /// </summary>
    /// <param name="bill_date">对账单日期，格式：yyyy-MM-dd，默认为前一天</param>
    /// <returns>返回下载结果的JSON字符串，包含状态码、消息、记录数等信息</returns>
    /// <remarks>
    /// 方法流程：
    /// 1. 获取对账单日期，默认为前一天
    /// 2. 获取所有微信支付配置信息
    /// 3. 遍历每个微信支付配置，下载并解析对账单
    /// 4. 保存解析后的账单数据
    /// 
    /// 返回结果说明：
    /// - status: 200表示成功，500表示失败
    /// - count: 成功处理的记录数
    /// - meg: 处理结果说明
    /// - trade_date: 对账单日期
    /// - syn_date: 同步时间
    /// </remarks>
    [HttpGet("download/wechatpay")]
    [AllowAnonymous]
    public async Task<dynamic> DownloadWechatPayBill(string bill_date = "")
    {
        try
        {
            // 验证和处理日期参数
            if (!string.IsNullOrEmpty(bill_date) && !Regex.IsMatch(bill_date, @"^\d{4}-\d{2}-\d{2}$"))
            {
                return new { status = 400, meg = "账单日期格式错误，应为yyyy-MM-dd格式" }.ToJsonString();
            }

            var billDate = string.IsNullOrEmpty(bill_date) 
                ? DateTime.Today.AddDays(-1).ToString("yyyy-MM-dd")
                : bill_date;

            // 获取微信支付配置
            var wechatList = await getWechatList();
            if (!wechatList.Any())
            {
                _logger.LogWarning("未找到有效的微信支付配置");
                return new { status = 400, meg = "未找到有效的微信支付配置" }.ToJsonString();
            }

            var wechatBills = new List<shopWechatBillEntity>();
            var request = new WeChatPayDownloadBillRequest
            {
                BillDate = billDate.Replace("-", ""), // 转换为微信支付API需要的格式
                BillType = "ALL"
            };

            foreach (var wechat in wechatList)
            {
                try
                {
                    var config = new WeChatPayOptions()
                    {
                        AppId = wechat.app_id,
                        MchId = wechat.mch_id,
                        SubAppId = wechat.sub_app_id,
                        SubMchId = wechat.sub_mch_id,
                        APIKey = wechat.pay_key
                    };

                var response = await _weChatPayClient.ExecuteAsync(request, config);
                    if (string.IsNullOrEmpty(response?.Body))
                    {
                        _logger.LogWarning($"商户 {wechat.mch_id} 未返回账单数据");
                        continue;
                    }

                    var bills = await ParseBillResponse(response.Body);
                    wechatBills.AddRange(bills);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"处理商户 {wechat.mch_id} 账单时发生错误");
                }
            }

            if (wechatBills.Any())
            {
                await SaveWechatBillsWithRetry(wechatBills, billDate);
            }

            return new { 
                status = 200, 
                count = wechatBills.Count, 
                meg = "同步成功", 
                trade_date = billDate, 
                syn_date = DateTime.Now 
            }.ToJsonString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载微信对账单时发生错误");
            return new { 
                status = 500, 
                meg = "处理账单时发生错误，请查看系统日志" 
            }.ToJsonString();
        }
    }

    /// <summary>
    /// 解析账单响应内容
    /// </summary>
    private async Task<List<shopWechatBillEntity>> ParseBillResponse(string responseBody)
    {
        var bills = new List<shopWechatBillEntity>();
        
        if (!responseBody.Contains("交易时间,公众账号ID,商户号,"))
        {
            return bills;
        }

        // 提取账单明细数据
        var billContent = responseBody.Substring(0, responseBody.IndexOf("总交易单数", StringComparison.Ordinal));
        var startIndex = billContent.IndexOf("`", StringComparison.Ordinal);
        if (startIndex == -1) return bills;

        billContent = billContent.Substring(startIndex).Replace('`', ' ');
        var tradeList = billContent.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);

        foreach (var item in tradeList)
        {
            try
            {
                var bill = item.Split(',');
                if (bill.Length < 23) continue;

                var deviceInfo = bill[4].Trim().Split('-');
                var model = new shopWechatBillEntity
                {
                    id = SnowflakeIdHelper.NextId(),
                    transaction_id = bill[5].Trim(),
                    out_trade_no = bill[6].Trim(),
                    shop_id = deviceInfo[0],
                    terminal_id = deviceInfo.Length > 1 ? deviceInfo[1] : "",
                    trade_time = DateTime.Parse(bill[0].Trim()),
                    app_id = bill[1].Trim(),
                    mch_id = bill[2].Trim(),
                    sub_mch_id = bill[3].Trim(),
                    device_info = bill[4].Trim(),
                    trade_type = bill[8].Trim(),
                    trade_state = bill[9].Trim(),
                    trade_fee = decimal.TryParse(bill[12], out var fee) ? fee : 0m,
                    charge = decimal.TryParse(bill[22], out var charge) ? charge : 0m,
                    refund_id = bill[14].Trim(),
                    out_refund_no = bill[15].Trim(),
                    refund_fee = decimal.TryParse(bill[16], out var refundFee) ? refundFee : 0m,
                    refund_state = bill[19].Trim(),
                    body = bill[20].Trim(),
                    bill_date = DateTime.Parse(bill[0].Trim()).ToString("yyyy-MM-dd")
                };

                bills.Add(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析账单行时发生错误: {item}");
            }
        }

        return bills;
    }

    /// <summary>
    /// 保存微信对账单（带重试机制）
    /// </summary>
    private async Task SaveWechatBillsWithRetry(List<shopWechatBillEntity> bills, string billDate)
    {
        const int maxRetries = 3;
        var retryCount = 0;

        while (retryCount < maxRetries)
        {
            try
            {
                await _sugarClient.Ado.BeginTranAsync();

                // 分批处理，每批1000条
                const int batchSize = 1000;
                await _sugarClient.Deleteable<shopWechatBillEntity>()
                    .Where(p => p.bill_date == billDate)
                    .ExecuteCommandAsync();

                for (var i = 0; i < bills.Count; i += batchSize)
                {
                    var batch = bills.Skip(i).Take(batchSize).ToList();
                    await _sugarClient.Insertable(batch).ExecuteCommandAsync();
                }

                await _sugarClient.Ado.CommitTranAsync();
                return;
            }
            catch (Exception ex)
            {
                await _sugarClient.Ado.RollbackTranAsync();
                retryCount++;

                if (retryCount == maxRetries)
                {
                    _logger.LogError(ex, "保存微信对账单最终失败");
                    throw;
                }

                _logger.LogWarning($"保存微信对账单失败，正在进行第 {retryCount} 次重试");
                await Task.Delay(1000 * retryCount); // 指数退避
            }
        }
    }

    /// <summary>
    /// 获取微信配置列表
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task<List<shopWechatInput>> getWechatList()
    {
        return await _sugarClient.Queryable<shopWechatConfigEntity>()
            //.Where(x => x.shop_id == "1003")
            .GroupBy(x => new { x.app_id, x.mch_id, x.sub_app_id, x.sub_mch_id, x.pay_key })
            .Select(a => new shopWechatInput
            {
                app_id = a.app_id,
                mch_id = a.mch_id,
                sub_app_id = a.sub_app_id,
                sub_mch_id = a.sub_mch_id,
                pay_key = a.pay_key
            }).ToListAsync();
    }
}