﻿using SqlSugar;

namespace BPM.Domain.Entity.shop;

/// <summary>
/// 门店信息
/// 版 本：V3.2
/// 版 权：中畅源科技开发有限公司（https://www.szclouds.com）
/// 作 者：Aarons
/// 日 期：2023-01-05.
/// </summary>
[SugarTable("sto_store")]
[Tenant("IPOS-PRODUCT")]
public class shopEntity
{
    /// <summary>
    /// 门店编号
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    public string store_no { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public string store_name { get; set; }

    /// <summary>
    /// 上级id
    /// </summary>
    public string parent_id { get; set; }

    /// <summary>
    /// 门店名称
    /// </summary>
    public int? status { get; set; }

    /// <summary>
    /// 省份
    /// </summary>
    public string province { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string city { get; set; }

    /// <summary>
    /// 区县
    /// </summary>
    public string area { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string address { get; set; }

    /// <summary>
    /// 精度
    /// </summary>
    public decimal? longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public decimal? latitude { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string tel { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string email { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string intro { get; set; }


    /// <summary>
    /// 外部门店id
    /// </summary>
    public long source_no { get; set; }

    /// <summary>
    /// 网店id
    /// </summary>
    public long net_source_no { get; set; }

    /// <summary>
    /// 创建人id
    /// </summary>

    public string created_user_id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime created_date { get; set; }

    /// <summary>
    /// 更新人id
    /// </summary>
    public string modify_user_id { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime modify_date { get; set; }
}

