﻿using NPOI.SS.Formula.Functions;

namespace BPM.Extras.Youzan.Result;

/// <summary>
/// 有赞返回对象
/// </summary>
public class YouzanResult<T>
{
    /// <summary>
    /// 跟踪id
    /// </summary>
    public string trace_id { get; set; }

    /// <summary>
    /// 交易代码
    /// </summary>
    public string code { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool success { get; set; }

    /// <summary>
    /// 返回对象
    /// </summary>
    public T data { get; set; }

    /// <summary>
    /// 消息
    /// </summary>

    public string message { get; set; }
}

