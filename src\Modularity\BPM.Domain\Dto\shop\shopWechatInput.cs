﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.shop;

/// <summary>
/// 门店微信输出.
/// </summary>
[SuppressSniffer]
public class shopWechatInput
{
    /// <summary>
    /// 门店编号
    /// </summary>
    public string shop_id { get; set; }

    /// <summary>
    /// 应用编号.
    /// </summary>
    public string app_id { get; set; }

    /// <summary>
    /// 商户号.
    /// </summary>
    public string mch_id { get; set; }

    /// <summary>
    /// 子商户应用编号.
    /// </summary>
    public string sub_app_id { get; set; }

    /// <summary>
    /// 子商户号.
    /// </summary>
    public string sub_mch_id { get; set; }

    /// <summary>
    /// 支付密钥.
    /// </summary>
    public string pay_key { get; set; }

    /// <summary>
    /// 证书路径.
    /// </summary>
    public string cert_path { get; set; }

}