﻿using BPM.Domain.Entity.customer;
using BPM.Domain.Requests.user;
namespace BPM.Application;

/// <summary>
/// 会员管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11
/// </summary>
[ApiDescriptionSettings(Tag = "用户管理", Name = "User", Order = 600)]
[Route("api/[controller]")]
public class UserService : IDynamicApiController, ITransient
{

    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly SqlSugarProvider _repository;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context"></param>
    public UserService(ISqlSugarClient context, IYouzanService youzanService)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<customerCardEntity>();
        _youzanService = youzanService;
    }

    /// <summary>
    /// 更新客户
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("info")]
    public async Task<dynamic> getUserList()
    {
        var token = await _youzanService.GetTokenAsync();
        var param = new YouzanParameter();
        param.url = "youzan.users.info.query/1.0.1";
        param.method = "POST";
        param.body = new userQueryRequest()
        {
            open_id_type = 1,
            mobile = "13510189783",
        }.ToJsonString();
        var res = await _youzanService.GetData(param);
        return true;
    }

}
