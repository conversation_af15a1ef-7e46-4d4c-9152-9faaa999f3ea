﻿using BPM.Common.Extension;
@*是否需要上传文件引用*@
@if(Model.IsUploading)
{
@:using BPM.Common.Models;
}
using BPM.Common.Security;
using BPM.@(Model.NameSpace).Entitys.Dto.@(Model.ClassName);
using Mapster;
 
namespace BPM.@(Model.NameSpace).<EMAIL>;

public class Mapper : IRegister
{
	public void Register(TypeAdapterConfig config)
	{
@*判断是否需要映射*@
@if(Model.IsMapper)
{
@*判断WebType 1-纯表单,2-常规表单,3-流程列表单*@
@switch(Model.WebType)
{
case 1:
@{GetCrInputToEntity(); }
@if(Model.EnableFlow || Model.Type == 3){
GetEntityToInfoOutput();
}
break;
case 2:
@{GetCrInputToEntity(); }
@{GetEntityToInfoOutput();}
break;
}
}
	}
}
@{
	@*新增输入转实体*@
	void GetCrInputToEntity()
	{	
		@:config.ForType<@(Model.ClassName)CrInput, @(Model.ClassName)Entity>()
@*循环表字段*@
@foreach (var column in Model.TableField)
{
@*判断bpmKey不为空*@
if (@column.bpmKey != null)
{
switch(column.bpmKey)
{
@*下拉框控件*@
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@*是否为多选*@
@if(column.IsMultiple)
{
			@:.Map(dest => dest.@(column.ColumnName), src => src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName) != null && src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).Count > 0 ? src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).ToJsonString() : null)
}
else
{
			@:.Map(dest => dest.@(column.ColumnName), src => src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName))
}
break;
@*复选框、级联、省市区、图片上传、文件上传*@
case "checkbox":
case "cascader":
case "organizeSelect":
case "areaSelect":
case "uploadImg":
case "uploadFile":
			@:.Map(dest => dest.@(column.ColumnName), src => src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName) != null && src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).Count > 0 ? src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).ToJsonString() : null)
break;
case "createTime":
case "modifyTime":
			
break;
default:
			@:.Map(dest => dest.@(column.ColumnName), src => src.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)@(column.NetType.Contains("int") ? ".ParseToInt()" : ""))
break;
}
}
}
		@:;
	}
}
@{
	@*实体转详情输出*@
	void GetEntityToInfoOutput()
	{
		@:config.ForType<@(Model.ClassName)Entity, @(Model.ClassName)InfoOutput>()
@foreach (var column in Model.TableField)
{
if (@column.bpmKey != null)
{
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<string>>() : null)
}else
{
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName))
}
break;
case "cascader":
case "organizeSelect":
case "areaSelect":
@if(column.IsMultiple)
{
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src =>  src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<List<string>>>() : null)
}else
{
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<string>>() : null)
}
break;
case "checkbox":
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<string>>() : null)
break;
case "uploadImg":
case "uploadFile":
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<FileControlsModel>>() : new List<FileControlsModel>())
break;
case "createTime":
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => string.Format("{0:yyyy-MM-dd HH:mm:ss}", src.@(column.ColumnName)))
break;
case "modifyTime":
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? string.Format("{0:yyyy-MM-dd HH:mm:ss}", src.@(column.ColumnName)) : null)
break;
default:
			@:.Map(dest => dest.bpm_@(column.TableName)_bpm_@(column.LowerColumnName), src => src.@(column.ColumnName))
break;
}
}
}
		@:;
	}
}