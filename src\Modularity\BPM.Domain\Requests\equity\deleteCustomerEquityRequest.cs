﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.equity;

/// <summary>
/// 删除客户权益卡
/// </summary>
[SuppressSniffer]
public class deleteCustomerEquityRequest
{

    /// <summary>
    /// 三方应用id
    /// </summary>
    public string client_id { get; set; }

    /// <summary>
    ///卡别名，不能自定义，需要从接口youzan.scrm.card.list 中获取。
    /// </summary>
    public string card_alias { get; set; }

    /// <summary>
    /// 权益卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public userEquityInfo user { get; set; }

}