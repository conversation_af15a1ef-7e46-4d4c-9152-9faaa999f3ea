﻿using BPM.ConfigurableOptions;

namespace BPM.Extras.Youzan.Options;

/// <summary>
/// 有赞配置
/// </summary>
public class YouzanOptions : IConfigurableOptions
{
    /// <summary>
    /// 登录路径
    /// </summary>
    public string LoginPath { get; set; }

    /// <summary>
    /// 基础路径
    /// </summary>
    public string BasicPath { get; set; }

    /// <summary>
    /// 客户端id
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 客户端密钥
    /// </summary>
    public string ClientSecret { get; set; }

    /// <summary>
    /// 授权店铺id
    /// </summary>
    public string GrantId { get; set; }

    /// <summary>
    /// 授权方式
    /// </summary>
    public string AuthorizeType { get; set; } = "silent";

    /// <summary>
    /// 是否刷新
    /// </summary>
    public bool Refresh { get; set; } = false;
}

