<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BPM.Extras.Youzan</name>
    </assembly>
    <members>
        <member name="T:BPM.Extras.Youzan.Dto.TokenDto">
            <summary>
            有赞token获取
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Dto.TokenDto.access_token">
            <summary>
            令牌
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Dto.TokenDto.expires">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Dto.TokenDto.scope">
            <summary>
            作用域
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Dto.TokenDto.authority_id">
            <summary>
            授权主体id
            </summary>
        </member>
        <member name="T:BPM.Extras.Youzan.Extensions.YouzanServiceCollectionExtensions">
            <summary>
            有赞服务扩展类
            </summary>
        </member>
        <member name="M:BPM.Extras.Youzan.Extensions.YouzanServiceCollectionExtensions.AddYouzan(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加有赞服务
            </summary>
            <param name="services">服务集合</param>
            <returns></returns>
        </member>
        <member name="M:BPM.Extras.Youzan.Extensions.YouzanServiceCollectionExtensions.ConfigureYouzanOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加有赞配置
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:BPM.Extras.Youzan.Options.YouzanOptions">
            <summary>
            有赞配置
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.LoginPath">
            <summary>
            登录路径
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.BasicPath">
            <summary>
            基础路径
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.ClientId">
            <summary>
            客户端id
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.ClientSecret">
            <summary>
            客户端密钥
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.GrantId">
            <summary>
            授权店铺id
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.AuthorizeType">
            <summary>
            授权方式
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Options.YouzanOptions.Refresh">
            <summary>
            是否刷新
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Request.YouzanParameter.url">
            <summary>
            路径.
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Request.YouzanParameter.method">
            <summary>
            请求方式.
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Request.YouzanParameter.version">
            <summary>
            版本.
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Request.YouzanParameter.grant_id">
            <summary>
             门店id
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Request.YouzanParameter.body">
            <summary>
            参数.
            </summary>
        </member>
        <member name="T:BPM.Extras.Youzan.Result.YouzanResult`1">
            <summary>
            有赞返回对象
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Result.YouzanResult`1.trace_id">
            <summary>
            跟踪id
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Result.YouzanResult`1.code">
            <summary>
            交易代码
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Result.YouzanResult`1.success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Result.YouzanResult`1.data">
            <summary>
            返回对象
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Result.YouzanResult`1.message">
            <summary>
            消息
            </summary>
        </member>
        <member name="T:BPM.Extras.Youzan.Services.Abstractions.IYouzanService">
            <summary>
            有赞服务
            </summary>
        </member>
        <member name="M:BPM.Extras.Youzan.Services.Abstractions.IYouzanService.GetTokenAsync(System.String,System.Boolean)">
            <summary>
            有赞token.
            </summary>
            <param name="grant_id">授权店铺ID</param>
            <param name="forceRefresh">是否强制刷新token</param>
            <returns>访问令牌</returns>
        </member>
        <member name="M:BPM.Extras.Youzan.Services.Abstractions.IYouzanService.GetData(BPM.Extras.Youzan.Request.YouzanParameter)">
            <summary>
            获取数据.
            </summary>
            <returns></returns>
        </member>
        <member name="T:BPM.Extras.Youzan.Services.Implements.YouzanService">
            <summary>
            有赞服务
            </summary>
        </member>
        <member name="F:BPM.Extras.Youzan.Services.Implements.YouzanService._cacheManager">
            <summary>
            缓存管理.
            </summary>
        </member>
        <member name="F:BPM.Extras.Youzan.Services.Implements.YouzanService._youzanOptions">
            <summary>
            有赞配置.
            </summary>
        </member>
        <member name="F:BPM.Extras.Youzan.Services.Implements.YouzanService._logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="P:BPM.Extras.Youzan.Services.Implements.YouzanService.token">
            <summary>
            访问令牌.
            </summary>
        </member>
        <member name="M:BPM.Extras.Youzan.Services.Implements.YouzanService.#ctor(BPM.Common.Manager.ICacheManager,Microsoft.Extensions.Options.IOptions{BPM.Extras.Youzan.Options.YouzanOptions},Microsoft.Extensions.Logging.ILogger{BPM.Extras.Youzan.Services.Implements.YouzanService})">
            <summary>
            初始化一个<see cref="T:BPM.Extras.Youzan.Services.Implements.YouzanService"/>类型的新实例.
            </summary>
            <param name="cacheManager">缓存服务</param>
            <param name="youzanOptions">有赞配置</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:BPM.Extras.Youzan.Services.Implements.YouzanService.GetTokenAsync(System.String,System.Boolean)">
            <summary>
            有赞token.
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Extras.Youzan.Services.Implements.YouzanService.GetData(BPM.Extras.Youzan.Request.YouzanParameter)">
            <summary>
            获取数据.
            </summary>
            <returns></returns>
        </member>
        <member name="M:BPM.Extras.Youzan.Services.Implements.YouzanService.GetDataWithRetry(BPM.Extras.Youzan.Request.YouzanParameter,System.Boolean)">
            <summary>
            获取数据（带重试）
            </summary>
            <param name="param">请求参数</param>
            <param name="isRetry">是否是重试请求</param>
            <returns></returns>
        </member>
    </members>
</doc>
