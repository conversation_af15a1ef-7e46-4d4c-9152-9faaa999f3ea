﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.trade;

/// <summary>
/// 订单扫码收银信息.
/// </summary>
[SuppressSniffer]
public class QrInfo
{
    /// <summary>
    /// 收款码ID.
    /// </summary>
    public int qr_id { get; set; }

    /// <summary>
    /// 收款码名称.
    /// </summary>
    public string qr_name { get; set; }

    /// <summary>
    /// 订单付款ID.
    /// </summary>
    public int qr_pay_id { get; set; }
}
