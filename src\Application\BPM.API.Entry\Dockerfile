FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["framework/BPM/BPM.csproj", "framework/BPM/BPM/"]
COPY ["src/modularity/oauth/BPM.OAuth/BPM.OAuth.csproj", "src/modularity/oauth/BPM.OAuth/"]
COPY ["src/modularity/system/BPM.Systems/BPM.Systems.csproj", "src/modularity/system/BPM.Systems/"]
COPY ["src/modularity/message/BPM.Message/BPM.Message.csproj", "src/modularity/message/BPM.Message/"]
COPY ["src/modularity/taskscheduler/BPM.TaskScheduler/BPM.TaskScheduler.csproj", "src/modularity/taskscheduler/BPM.TaskScheduler/"]
COPY ["src/modularity/workflow/BPM.WorkFlow/BPM.WorkFlow.csproj", "src/modularity/workflow/BPM.WorkFlow/"]
COPY ["src/modularity/visualdev/BPM.VisualDev/BPM.VisualDev.csproj", "src/modularity/visualdev/BPM.VisualDev/"]
COPY ["src/modularity/codegen/BPM.CodeGen/BPM.CodeGen.csproj", "src/modularity/codegen/BPM.CodeGen/"]
COPY ["src/modularity/visualdata/BPM.VisualData/BPM.VisualData.csproj", "src/modularity/visualdata/BPM.VisualData/"]
COPY ["src/modularity/extend/BPM.Extend/BPM.Extend.csproj", "src/modularity/extend/BPM.Extend/"]
COPY ["src/modularity/app/BPM.Apps/BPM.Apps.csproj", "src/modularity/app/BPM.Apps/"]
COPY ["src/modularity/subdev/BPM.SubDev/BPM.SubDev.csproj", "src/modularity/subdev/BPM.SubDev/"]
COPY ["src/application/BPM.API.Entry/BPM.API.Entry.csproj", "src/application/BPM.API.Entry/"]
COPY . .
WORKDIR "/src/src/application/BPM.API.Entry"
RUN dotnet build "BPM.API.Entry.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "BPM.API.Entry.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "BPM.API.Entry.dll"]