﻿using BPM.DependencyInjection;

namespace BPM.Domain.Responses.shop;

/// <summary>
/// 门店商品
/// </summary>
[SuppressSniffer]
public class shopResponse
{
    /// <summary>
    /// 门店集合
    /// </summary>
    public List<Items> items { get; set; }

    /// <summary>
    ///  明细
    /// </summary>
    public class Items
    {
        /// <summary>
        /// 店铺在有赞的id标识
        /// </summary>
        public int kdt_id { get; set; }

        /// <summary>
        /// 区编码
        /// </summary>
        public int county_id { get; set; }

        /// <summary>
        /// 经营类型1:直营，2:加盟
        /// </summary>
        public int join_type { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string address { get; set; }

        /// <summary>
        /// 三方门店编码
        /// </summary>
        public string shop_code { get; set; }

        /// <summary>
        /// 区名称
        /// </summary>
        public string county { get; set; }

        /// <summary>
        ///  区名称
        /// </summary>
        public string shop_display_no { get; set; }

        /// <summary>
        /// 经营关联店铺
        /// </summary>
        public int business_relation_kdt_id { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string shop_name { get; set; }
    }
}
