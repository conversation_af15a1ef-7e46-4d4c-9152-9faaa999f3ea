using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;

namespace BPM.Application;

/// <summary>
/// 本地任务-创建CRM权益.
/// </summary>
[JobDetail("job_crm_create_equity", Description = "创建CRM权益", GroupName = "BuiltIn", Concurrent = true)]
public class CrmCreateEquityJobService : IJob, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// CRM服务.
    /// </summary>
    private readonly CustomerEquityService _customerEquityService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public CrmCreateEquityJobService(IServiceScopeFactory serviceScopeFactory, CustomerEquityService customerEquityService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _customerEquityService = customerEquityService;
    }

    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var result = await _customerEquityService.createCustomerEquity();
        context.Result = result;
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
} 