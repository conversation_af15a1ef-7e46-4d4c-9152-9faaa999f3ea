{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "Template/1-SingleTable/CrInput.cs.m2dymvfz8a.vm", "AssetFile": "Template/1-SingleTable/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2562"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Uic15AKtTyatGUCxtiZE8aoo36EmUhC/v3tQs3IPu/I=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m2dymvfz8a"}, {"Name": "integrity", "Value": "sha256-Uic15AKtTyatGUCxtiZE8aoo36EmUhC/v3tQs3IPu/I="}, {"Name": "label", "Value": "Template/1-SingleTable/CrInput.cs.vm"}]}, {"Route": "Template/1-SingleTable/CrInput.cs.vm", "AssetFile": "Template/1-SingleTable/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2562"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Uic15AKtTyatGUCxtiZE8aoo36EmUhC/v3tQs3IPu/I=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uic15AKtTyatGUCxtiZE8aoo36EmUhC/v3tQs3IPu/I="}]}, {"Route": "Template/1-SingleTable/DetailOutput.cs.gyu9uank5b.vm", "AssetFile": "Template/1-SingleTable/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+Loalh4Yv1dSkjejo6f8SOeZwLg0m/Q3/dx8POmlR2o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gyu9uank5b"}, {"Name": "integrity", "Value": "sha256-+Loalh4Yv1dSkjejo6f8SOeZwLg0m/Q3/dx8POmlR2o="}, {"Name": "label", "Value": "Template/1-SingleTable/DetailOutput.cs.vm"}]}, {"Route": "Template/1-SingleTable/DetailOutput.cs.vm", "AssetFile": "Template/1-SingleTable/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"+Loalh4Yv1dSkjejo6f8SOeZwLg0m/Q3/dx8POmlR2o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Loalh4Yv1dSkjejo6f8SOeZwLg0m/Q3/dx8POmlR2o="}]}, {"Route": "Template/1-SingleTable/Entity.cs.0d3sqtdzss.vm", "AssetFile": "Template/1-SingleTable/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8648"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/IGa+M6NfQGwxUqheKU6oBnwkDxD5jqs0Eg1IHfRSBY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0d3sqtdzss"}, {"Name": "integrity", "Value": "sha256-/IGa+M6NfQGwxUqheKU6oBnwkDxD5jqs0Eg1IHfRSBY="}, {"Name": "label", "Value": "Template/1-SingleTable/Entity.cs.vm"}]}, {"Route": "Template/1-SingleTable/Entity.cs.vm", "AssetFile": "Template/1-SingleTable/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8648"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/IGa+M6NfQGwxUqheKU6oBnwkDxD5jqs0Eg1IHfRSBY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/IGa+M6NfQGwxUqheKU6oBnwkDxD5jqs0Eg1IHfRSBY="}]}, {"Route": "Template/1-SingleTable/InfoOutput.cs.nhad5bsd0u.vm", "AssetFile": "Template/1-SingleTable/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3146"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"W6e1nd2j/HcNnRvgHCFBQB0i3/TkmYtbtMJHhWalEtM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nhad5bsd0u"}, {"Name": "integrity", "Value": "sha256-W6e1nd2j/HcNnRvgHCFBQB0i3/TkmYtbtMJHhWalEtM="}, {"Name": "label", "Value": "Template/1-SingleTable/InfoOutput.cs.vm"}]}, {"Route": "Template/1-SingleTable/InfoOutput.cs.vm", "AssetFile": "Template/1-SingleTable/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3146"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"W6e1nd2j/HcNnRvgHCFBQB0i3/TkmYtbtMJHhWalEtM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-W6e1nd2j/HcNnRvgHCFBQB0i3/TkmYtbtMJHhWalEtM="}]}, {"Route": "Template/1-SingleTable/InlineEditor/InlineEditorOutput.cs.ie5vi0xdv5.vm", "AssetFile": "Template/1-SingleTable/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5059"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Gs/AsXgMhNYqVG/wR3PIyO1oJIPbnLf8K7nmYYnWEmY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ie5vi0xdv5"}, {"Name": "integrity", "Value": "sha256-Gs/AsXgMhNYqVG/wR3PIyO1oJIPbnLf8K7nmYYnWEmY="}, {"Name": "label", "Value": "Template/1-SingleTable/InlineEditor/InlineEditorOutput.cs.vm"}]}, {"Route": "Template/1-SingleTable/InlineEditor/InlineEditorOutput.cs.vm", "AssetFile": "Template/1-SingleTable/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5059"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Gs/AsXgMhNYqVG/wR3PIyO1oJIPbnLf8K7nmYYnWEmY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gs/AsXgMhNYqVG/wR3PIyO1oJIPbnLf8K7nmYYnWEmY="}]}, {"Route": "Template/1-SingleTable/InlineEditor/ListOutput.cs.2bvh2h8hh3.vm", "AssetFile": "Template/1-SingleTable/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5359"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vp5smZnzf1QHWhpoq6RUTPqBnCr1brzx3nNfno6cFbc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2bvh2h8hh3"}, {"Name": "integrity", "Value": "sha256-vp5smZnzf1QHWhpoq6RUTPqBnCr1brzx3nNfno6cFbc="}, {"Name": "label", "Value": "Template/1-SingleTable/InlineEditor/ListOutput.cs.vm"}]}, {"Route": "Template/1-SingleTable/InlineEditor/ListOutput.cs.vm", "AssetFile": "Template/1-SingleTable/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5359"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"vp5smZnzf1QHWhpoq6RUTPqBnCr1brzx3nNfno6cFbc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vp5smZnzf1QHWhpoq6RUTPqBnCr1brzx3nNfno6cFbc="}]}, {"Route": "Template/1-SingleTable/InlineEditor/Service.cs.ncvywrox62.vm", "AssetFile": "Template/1-SingleTable/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "103688"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"yQDWyw7EYIjNSFGkc+gP44DjCoSrH1kr8Z8EQVOcq4M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ncvywrox62"}, {"Name": "integrity", "Value": "sha256-yQDWyw7EYIjNSFGkc+gP44DjCoSrH1kr8Z8EQVOcq4M="}, {"Name": "label", "Value": "Template/1-SingleTable/InlineEditor/Service.cs.vm"}]}, {"Route": "Template/1-SingleTable/InlineEditor/Service.cs.vm", "AssetFile": "Template/1-SingleTable/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "103688"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"yQDWyw7EYIjNSFGkc+gP44DjCoSrH1kr8Z8EQVOcq4M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yQDWyw7EYIjNSFGkc+gP44DjCoSrH1kr8Z8EQVOcq4M="}]}, {"Route": "Template/1-SingleTable/ListOutput.cs.hnlwxlyodi.vm", "AssetFile": "Template/1-SingleTable/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2503"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"nYqP68Xpvg06t81G9c0rBS3geuirZFPWLOILfTtOWk8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hnlwxlyodi"}, {"Name": "integrity", "Value": "sha256-nYqP68Xpvg06t81G9c0rBS3geuirZFPWLOILfTtOWk8="}, {"Name": "label", "Value": "Template/1-SingleTable/ListOutput.cs.vm"}]}, {"Route": "Template/1-SingleTable/ListOutput.cs.vm", "AssetFile": "Template/1-SingleTable/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2503"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"nYqP68Xpvg06t81G9c0rBS3geuirZFPWLOILfTtOWk8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nYqP68Xpvg06t81G9c0rBS3geuirZFPWLOILfTtOWk8="}]}, {"Route": "Template/1-SingleTable/ListQueryInput.cs.5ts5kl0dso.vm", "AssetFile": "Template/1-SingleTable/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"55aGB5rMO4yBn4YYxEvTN9UYHdAuyNBj7cKZp9roDhA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ts5kl0dso"}, {"Name": "integrity", "Value": "sha256-55aGB5rMO4yBn4YYxEvTN9UYHdAuyNBj7cKZp9roDhA="}, {"Name": "label", "Value": "Template/1-SingleTable/ListQueryInput.cs.vm"}]}, {"Route": "Template/1-SingleTable/ListQueryInput.cs.vm", "AssetFile": "Template/1-SingleTable/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1941"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"55aGB5rMO4yBn4YYxEvTN9UYHdAuyNBj7cKZp9roDhA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-55aGB5rMO4yBn4YYxEvTN9UYHdAuyNBj7cKZp9roDhA="}]}, {"Route": "Template/1-SingleTable/Mapper.cs.4v2tvtanqg.vm", "AssetFile": "Template/1-SingleTable/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7110"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wCbMp0WwJep43XaL2ENI9qZgifz+oPt8icF/bCPZsPk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v2tvtanqg"}, {"Name": "integrity", "Value": "sha256-wCbMp0WwJep43XaL2ENI9qZgifz+oPt8icF/bCPZsPk="}, {"Name": "label", "Value": "Template/1-SingleTable/Mapper.cs.vm"}]}, {"Route": "Template/1-SingleTable/Mapper.cs.vm", "AssetFile": "Template/1-SingleTable/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7110"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"wCbMp0WwJep43XaL2ENI9qZgifz+oPt8icF/bCPZsPk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wCbMp0WwJep43XaL2ENI9qZgifz+oPt8icF/bCPZsPk="}]}, {"Route": "Template/1-SingleTable/Service.cs.lss7mj9om6.vm", "AssetFile": "Template/1-SingleTable/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "105840"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hR//SzhrF2ZDeqRvVrCZ4ehZRbqkC+Fn5HWhMV4AFzY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lss7mj9om6"}, {"Name": "integrity", "Value": "sha256-hR//SzhrF2ZDeqRvVrCZ4ehZRbqkC+Fn5HWhMV4AFzY="}, {"Name": "label", "Value": "Template/1-SingleTable/Service.cs.vm"}]}, {"Route": "Template/1-SingleTable/Service.cs.vm", "AssetFile": "Template/1-SingleTable/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105840"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hR//SzhrF2ZDeqRvVrCZ4ehZRbqkC+Fn5HWhMV4AFzY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hR//SzhrF2ZDeqRvVrCZ4ehZRbqkC+Fn5HWhMV4AFzY="}]}, {"Route": "Template/2-MainBelt/CrInput.cs.mnzoc6jbp9.vm", "AssetFile": "Template/2-MainBelt/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3524"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fuEyn9+QRsSd4gcXqDSxVxAtIV3+g8mEFuZF6F3SNDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mnzoc6jbp9"}, {"Name": "integrity", "Value": "sha256-fuEyn9+QRsSd4gcXqDSxVxAtIV3+g8mEFuZF6F3SNDI="}, {"Name": "label", "Value": "Template/2-MainBelt/CrInput.cs.vm"}]}, {"Route": "Template/2-MainBelt/CrInput.cs.vm", "AssetFile": "Template/2-MainBelt/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3524"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fuEyn9+QRsSd4gcXqDSxVxAtIV3+g8mEFuZF6F3SNDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fuEyn9+QRsSd4gcXqDSxVxAtIV3+g8mEFuZF6F3SNDI="}]}, {"Route": "Template/2-MainBelt/DetailOutput.cs.clzqp4ls6f.vm", "AssetFile": "Template/2-MainBelt/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1680"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sM+bnSNCUvZP6fjMfo20K6dbUkiIPxiBNUqPg2N/MbI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "clzqp4ls6f"}, {"Name": "integrity", "Value": "sha256-sM+bnSNCUvZP6fjMfo20K6dbUkiIPxiBNUqPg2N/MbI="}, {"Name": "label", "Value": "Template/2-MainBelt/DetailOutput.cs.vm"}]}, {"Route": "Template/2-MainBelt/DetailOutput.cs.vm", "AssetFile": "Template/2-MainBelt/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1680"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sM+bnSNCUvZP6fjMfo20K6dbUkiIPxiBNUqPg2N/MbI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sM+bnSNCUvZP6fjMfo20K6dbUkiIPxiBNUqPg2N/MbI="}]}, {"Route": "Template/2-MainBelt/Entity.cs.kc4cchq7bz.vm", "AssetFile": "Template/2-MainBelt/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8997"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BHHzO6OcgXBwsdUgP6l0g9SRumyzX8IkN1OtzEpsvZc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kc4cchq7bz"}, {"Name": "integrity", "Value": "sha256-BHHzO6OcgXBwsdUgP6l0g9SRumyzX8IkN1OtzEpsvZc="}, {"Name": "label", "Value": "Template/2-MainBelt/Entity.cs.vm"}]}, {"Route": "Template/2-MainBelt/Entity.cs.vm", "AssetFile": "Template/2-MainBelt/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8997"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BHHzO6OcgXBwsdUgP6l0g9SRumyzX8IkN1OtzEpsvZc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BHHzO6OcgXBwsdUgP6l0g9SRumyzX8IkN1OtzEpsvZc="}]}, {"Route": "Template/2-MainBelt/InfoOutput.cs.s48wwui810.vm", "AssetFile": "Template/2-MainBelt/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3529"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LGlc3ALGCLwIQp0YaH/szoOmUWHQINXpOWImHZvSvUY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s48wwui810"}, {"Name": "integrity", "Value": "sha256-LGlc3ALGCLwIQp0YaH/szoOmUWHQINXpOWImHZvSvUY="}, {"Name": "label", "Value": "Template/2-MainBelt/InfoOutput.cs.vm"}]}, {"Route": "Template/2-MainBelt/InfoOutput.cs.vm", "AssetFile": "Template/2-MainBelt/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3529"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LGlc3ALGCLwIQp0YaH/szoOmUWHQINXpOWImHZvSvUY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LGlc3ALGCLwIQp0YaH/szoOmUWHQINXpOWImHZvSvUY="}]}, {"Route": "Template/2-MainBelt/InlineEditor/InlineEditorOutput.cs.tgxvxg5ftt.vm", "AssetFile": "Template/2-MainBelt/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5445"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0zOPn00NaMWOrCdLvnIPWrZrbSllmAzYs8hlF+OQ3fA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgxvxg5ftt"}, {"Name": "integrity", "Value": "sha256-0zOPn00NaMWOrCdLvnIPWrZrbSllmAzYs8hlF+OQ3fA="}, {"Name": "label", "Value": "Template/2-MainBelt/InlineEditor/InlineEditorOutput.cs.vm"}]}, {"Route": "Template/2-MainBelt/InlineEditor/InlineEditorOutput.cs.vm", "AssetFile": "Template/2-MainBelt/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5445"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0zOPn00NaMWOrCdLvnIPWrZrbSllmAzYs8hlF+OQ3fA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zOPn00NaMWOrCdLvnIPWrZrbSllmAzYs8hlF+OQ3fA="}]}, {"Route": "Template/2-MainBelt/InlineEditor/ListOutput.cs.qgkyz3sjye.vm", "AssetFile": "Template/2-MainBelt/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4043"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"GgtIFAHjHcgTrAdmN2m+qR9aTXep3onVyJGtHkzDLkE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qgkyz3sjye"}, {"Name": "integrity", "Value": "sha256-GgtIFAHjHcgTrAdmN2m+qR9aTXep3onVyJGtHkzDLkE="}, {"Name": "label", "Value": "Template/2-MainBelt/InlineEditor/ListOutput.cs.vm"}]}, {"Route": "Template/2-MainBelt/InlineEditor/ListOutput.cs.vm", "AssetFile": "Template/2-MainBelt/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4043"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"GgtIFAHjHcgTrAdmN2m+qR9aTXep3onVyJGtHkzDLkE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GgtIFAHjHcgTrAdmN2m+qR9aTXep3onVyJGtHkzDLkE="}]}, {"Route": "Template/2-MainBelt/InlineEditor/Service.cs.astscj33er.vm", "AssetFile": "Template/2-MainBelt/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "196418"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"yi1EDGgVKsRwWyeTWFzYSq//W47kZIdp8FGrfk6T/kk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "astscj33er"}, {"Name": "integrity", "Value": "sha256-yi1EDGgVKsRwWyeTWFzYSq//W47kZIdp8FGrfk6T/kk="}, {"Name": "label", "Value": "Template/2-MainBelt/InlineEditor/Service.cs.vm"}]}, {"Route": "Template/2-MainBelt/InlineEditor/Service.cs.vm", "AssetFile": "Template/2-MainBelt/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196418"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"yi1EDGgVKsRwWyeTWFzYSq//W47kZIdp8FGrfk6T/kk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yi1EDGgVKsRwWyeTWFzYSq//W47kZIdp8FGrfk6T/kk="}]}, {"Route": "Template/2-MainBelt/ListOutput.cs.f4oa2nzg6i.vm", "AssetFile": "Template/2-MainBelt/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2926"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"n8ZgYm/523A5gPPVrSswLWC5JN/4qH5aW3Yjx4FF2jY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f4oa2nzg6i"}, {"Name": "integrity", "Value": "sha256-n8ZgYm/523A5gPPVrSswLWC5JN/4qH5aW3Yjx4FF2jY="}, {"Name": "label", "Value": "Template/2-MainBelt/ListOutput.cs.vm"}]}, {"Route": "Template/2-MainBelt/ListOutput.cs.vm", "AssetFile": "Template/2-MainBelt/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2926"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"n8ZgYm/523A5gPPVrSswLWC5JN/4qH5aW3Yjx4FF2jY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n8ZgYm/523A5gPPVrSswLWC5JN/4qH5aW3Yjx4FF2jY="}]}, {"Route": "Template/2-MainBelt/ListQueryInput.cs.0d9tl5ue2d.vm", "AssetFile": "Template/2-MainBelt/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3308"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pJ11oSgzRwZe3IUX/MwKB6znqP9hG7gPeY3guiTAnEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0d9tl5ue2d"}, {"Name": "integrity", "Value": "sha256-pJ11oSgzRwZe3IUX/MwKB6znqP9hG7gPeY3guiTAnEM="}, {"Name": "label", "Value": "Template/2-MainBelt/ListQueryInput.cs.vm"}]}, {"Route": "Template/2-MainBelt/ListQueryInput.cs.vm", "AssetFile": "Template/2-MainBelt/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3308"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"pJ11oSgzRwZe3IUX/MwKB6znqP9hG7gPeY3guiTAnEM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pJ11oSgzRwZe3IUX/MwKB6znqP9hG7gPeY3guiTAnEM="}]}, {"Route": "Template/2-MainBelt/Mapper.cs.cg90oarz59.vm", "AssetFile": "Template/2-MainBelt/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7071"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JOBLFqATboCOiSgasP/ZuRYr7Ku4kHHlGFemr968JT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg90oarz59"}, {"Name": "integrity", "Value": "sha256-JOBLFqATboCOiSgasP/ZuRYr7Ku4kHHlGFemr968JT4="}, {"Name": "label", "Value": "Template/2-MainBelt/Mapper.cs.vm"}]}, {"Route": "Template/2-MainBelt/Mapper.cs.vm", "AssetFile": "Template/2-MainBelt/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7071"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JOBLFqATboCOiSgasP/ZuRYr7Ku4kHHlGFemr968JT4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JOBLFqATboCOiSgasP/ZuRYr7Ku4kHHlGFemr968JT4="}]}, {"Route": "Template/2-MainBelt/Service.cs.vm", "AssetFile": "Template/2-MainBelt/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196641"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ULLSla2WnXS4gBp/olK6jTRcUwPxNF+3dXhs4mnnUtw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ULLSla2WnXS4gBp/olK6jTRcUwPxNF+3dXhs4mnnUtw="}]}, {"Route": "Template/2-MainBelt/Service.cs.wkgr5b5pdq.vm", "AssetFile": "Template/2-MainBelt/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "196641"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ULLSla2WnXS4gBp/olK6jTRcUwPxNF+3dXhs4mnnUtw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wkgr5b5pdq"}, {"Name": "integrity", "Value": "sha256-ULLSla2WnXS4gBp/olK6jTRcUwPxNF+3dXhs4mnnUtw="}, {"Name": "label", "Value": "Template/2-MainBelt/Service.cs.vm"}]}, {"Route": "Template/3-Auxiliary/CrInput.cs.th8808nen4.vm", "AssetFile": "Template/3-Auxiliary/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2569"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/xK8ouNzep2f1cHhIPuNN06z7BX/lKmzIyTVy2ICeH8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "th8808nen4"}, {"Name": "integrity", "Value": "sha256-/xK8ouNzep2f1cHhIPuNN06z7BX/lKmzIyTVy2ICeH8="}, {"Name": "label", "Value": "Template/3-Auxiliary/CrInput.cs.vm"}]}, {"Route": "Template/3-Auxiliary/CrInput.cs.vm", "AssetFile": "Template/3-Auxiliary/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2569"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/xK8ouNzep2f1cHhIPuNN06z7BX/lKmzIyTVy2ICeH8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xK8ouNzep2f1cHhIPuNN06z7BX/lKmzIyTVy2ICeH8="}]}, {"Route": "Template/3-Auxiliary/Entity.cs.pj58yxjpdw.vm", "AssetFile": "Template/3-Auxiliary/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7917"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FcTrQ2epFC1yciskTWeVrrFEH3MIGuPnj3SirXLYwJM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj58yxjpdw"}, {"Name": "integrity", "Value": "sha256-FcTrQ2epFC1yciskTWeVrrFEH3MIGuPnj3SirXLYwJM="}, {"Name": "label", "Value": "Template/3-Auxiliary/Entity.cs.vm"}]}, {"Route": "Template/3-Auxiliary/Entity.cs.vm", "AssetFile": "Template/3-Auxiliary/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7917"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FcTrQ2epFC1yciskTWeVrrFEH3MIGuPnj3SirXLYwJM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FcTrQ2epFC1yciskTWeVrrFEH3MIGuPnj3SirXLYwJM="}]}, {"Route": "Template/3-Auxiliary/InfoOutput.cs.s1j5ak3x9v.vm", "AssetFile": "Template/3-Auxiliary/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2371"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sHnh9pke5wwWk1EN0EjSiXaPsi6NB2eYLEnZM4OALuI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s1j5ak3x9v"}, {"Name": "integrity", "Value": "sha256-sHnh9pke5wwWk1EN0EjSiXaPsi6NB2eYLEnZM4OALuI="}, {"Name": "label", "Value": "Template/3-Auxiliary/InfoOutput.cs.vm"}]}, {"Route": "Template/3-Auxiliary/InfoOutput.cs.vm", "AssetFile": "Template/3-Auxiliary/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2371"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"sHnh9pke5wwWk1EN0EjSiXaPsi6NB2eYLEnZM4OALuI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sHnh9pke5wwWk1EN0EjSiXaPsi6NB2eYLEnZM4OALuI="}]}, {"Route": "Template/3-Auxiliary/Mapper.cs.vl6c8q2gsa.vm", "AssetFile": "Template/3-Auxiliary/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4688"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"c3xg35QBnh0oWPFxo7a8XPJB+UKI5sv4tPxqf8F6i6I=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vl6c8q2gsa"}, {"Name": "integrity", "Value": "sha256-c3xg35QBnh0oWPFxo7a8XPJB+UKI5sv4tPxqf8F6i6I="}, {"Name": "label", "Value": "Template/3-Auxiliary/Mapper.cs.vm"}]}, {"Route": "Template/3-Auxiliary/Mapper.cs.vm", "AssetFile": "Template/3-Auxiliary/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4688"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"c3xg35QBnh0oWPFxo7a8XPJB+UKI5sv4tPxqf8F6i6I=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c3xg35QBnh0oWPFxo7a8XPJB+UKI5sv4tPxqf8F6i6I="}]}, {"Route": "Template/4-MainBeltVice/CrInput.cs.cluuxuq152.vm", "AssetFile": "Template/4-MainBeltVice/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3337"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YM+FXCKpElIbK2GR3fKsHHEtpdDw2PUE07AvSIbCjmM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cluuxuq152"}, {"Name": "integrity", "Value": "sha256-YM+FXCKpElIbK2GR3fKsHHEtpdDw2PUE07AvSIbCjmM="}, {"Name": "label", "Value": "Template/4-MainBeltVice/CrInput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/CrInput.cs.vm", "AssetFile": "Template/4-MainBeltVice/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3337"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YM+FXCKpElIbK2GR3fKsHHEtpdDw2PUE07AvSIbCjmM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YM+FXCKpElIbK2GR3fKsHHEtpdDw2PUE07AvSIbCjmM="}]}, {"Route": "Template/4-MainBeltVice/DetailOutput.cs.5z27hep2t9.vm", "AssetFile": "Template/4-MainBeltVice/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1697"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0cNjMWsUx+EbHvmbSq1GOWBogi6Nla6b25RZepCxVTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5z27hep2t9"}, {"Name": "integrity", "Value": "sha256-0cNjMWsUx+EbHvmbSq1GOWBogi6Nla6b25RZepCxVTQ="}, {"Name": "label", "Value": "Template/4-MainBeltVice/DetailOutput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/DetailOutput.cs.vm", "AssetFile": "Template/4-MainBeltVice/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1697"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"0cNjMWsUx+EbHvmbSq1GOWBogi6Nla6b25RZepCxVTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0cNjMWsUx+EbHvmbSq1GOWBogi6Nla6b25RZepCxVTQ="}]}, {"Route": "Template/4-MainBeltVice/Entity.cs.9lxawumeq1.vm", "AssetFile": "Template/4-MainBeltVice/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9024"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"c/8jXe0DEu/+YfatcOC/cfWUIwztZu+nZA1yNf4M4h0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9lxawumeq1"}, {"Name": "integrity", "Value": "sha256-c/8jXe0DEu/+YfatcOC/cfWUIwztZu+nZA1yNf4M4h0="}, {"Name": "label", "Value": "Template/4-MainBeltVice/Entity.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/Entity.cs.vm", "AssetFile": "Template/4-MainBeltVice/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9024"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"c/8jXe0DEu/+YfatcOC/cfWUIwztZu+nZA1yNf4M4h0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c/8jXe0DEu/+YfatcOC/cfWUIwztZu+nZA1yNf4M4h0="}]}, {"Route": "Template/4-MainBeltVice/InfoOutput.cs.lm6t03u6no.vm", "AssetFile": "Template/4-MainBeltVice/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3344"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M+0qv8lOBJ/0CSK/QEQOLueqaSirGrvmb12LrYJsvM0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lm6t03u6no"}, {"Name": "integrity", "Value": "sha256-M+0qv8lOBJ/0CSK/QEQOLueqaSirGrvmb12LrYJsvM0="}, {"Name": "label", "Value": "Template/4-MainBeltVice/InfoOutput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/InfoOutput.cs.vm", "AssetFile": "Template/4-MainBeltVice/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3344"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"M+0qv8lOBJ/0CSK/QEQOLueqaSirGrvmb12LrYJsvM0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M+0qv8lOBJ/0CSK/QEQOLueqaSirGrvmb12LrYJsvM0="}]}, {"Route": "Template/4-MainBeltVice/InlineEditor/InlineEditorOutput.cs.8ykyr5bkvx.vm", "AssetFile": "Template/4-MainBeltVice/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5121"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"kH3XIkWAdA9Tx5tayhpeAXh3A0FduEcfI+wp1vJ1MRU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8ykyr5bkvx"}, {"Name": "integrity", "Value": "sha256-kH3XIkWAdA9Tx5tayhpeAXh3A0FduEcfI+wp1vJ1MRU="}, {"Name": "label", "Value": "Template/4-MainBeltVice/InlineEditor/InlineEditorOutput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/InlineEditor/InlineEditorOutput.cs.vm", "AssetFile": "Template/4-MainBeltVice/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5121"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"kH3XIkWAdA9Tx5tayhpeAXh3A0FduEcfI+wp1vJ1MRU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kH3XIkWAdA9Tx5tayhpeAXh3A0FduEcfI+wp1vJ1MRU="}]}, {"Route": "Template/4-MainBeltVice/InlineEditor/ListOutput.cs.qnb6agsczz.vm", "AssetFile": "Template/4-MainBeltVice/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5542"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hsquCcYQ0F1spH786i9BCGSBUBFDG9imIAflMIIftyM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qnb6ags<PERSON>z"}, {"Name": "integrity", "Value": "sha256-hsquCcYQ0F1spH786i9BCGSBUBFDG9imIAflMIIftyM="}, {"Name": "label", "Value": "Template/4-MainBeltVice/InlineEditor/ListOutput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/InlineEditor/ListOutput.cs.vm", "AssetFile": "Template/4-MainBeltVice/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5542"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hsquCcYQ0F1spH786i9BCGSBUBFDG9imIAflMIIftyM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hsquCcYQ0F1spH786i9BCGSBUBFDG9imIAflMIIftyM="}]}, {"Route": "Template/4-MainBeltVice/InlineEditor/Service.cs.t7y3lbnbvw.vm", "AssetFile": "Template/4-MainBeltVice/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "126118"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"B78nXefDU32F5dTIEEgdUpbL3+VZ25yKHryXg/P29xI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t7y3lbnbvw"}, {"Name": "integrity", "Value": "sha256-B78nXefDU32F5dTIEEgdUpbL3+VZ25yKHryXg/P29xI="}, {"Name": "label", "Value": "Template/4-MainBeltVice/InlineEditor/Service.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/InlineEditor/Service.cs.vm", "AssetFile": "Template/4-MainBeltVice/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "126118"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"B78nXefDU32F5dTIEEgdUpbL3+VZ25yKHryXg/P29xI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B78nXefDU32F5dTIEEgdUpbL3+VZ25yKHryXg/P29xI="}]}, {"Route": "Template/4-MainBeltVice/ListOutput.cs.pl11j10ymr.vm", "AssetFile": "Template/4-MainBeltVice/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3071"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"oyqHcI2Voyb1nML/1UU4gNpJiK/z+MJ/yLSSEfmsalc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pl11j10ymr"}, {"Name": "integrity", "Value": "sha256-oyqHcI2Voyb1nML/1UU4gNpJiK/z+MJ/yLSSEfmsalc="}, {"Name": "label", "Value": "Template/4-MainBeltVice/ListOutput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/ListOutput.cs.vm", "AssetFile": "Template/4-MainBeltVice/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3071"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"oyqHcI2Voyb1nML/1UU4gNpJiK/z+MJ/yLSSEfmsalc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oyqHcI2Voyb1nML/1UU4gNpJiK/z+MJ/yLSSEfmsalc="}]}, {"Route": "Template/4-MainBeltVice/ListQueryInput.cs.vm", "AssetFile": "Template/4-MainBeltVice/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3116"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"q1agvRMr/sF4pAkfoTsxr2AjMJr5OFSbvPOWzZnvo38=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q1agvRMr/sF4pAkfoTsxr2AjMJr5OFSbvPOWzZnvo38="}]}, {"Route": "Template/4-MainBeltVice/ListQueryInput.cs.za2s8h74w2.vm", "AssetFile": "Template/4-MainBeltVice/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3116"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"q1agvRMr/sF4pAkfoTsxr2AjMJr5OFSbvPOWzZnvo38=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "za2s8h74w2"}, {"Name": "integrity", "Value": "sha256-q1agvRMr/sF4pAkfoTsxr2AjMJr5OFSbvPOWzZnvo38="}, {"Name": "label", "Value": "Template/4-MainBeltVice/ListQueryInput.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/Mapper.cs.vm", "AssetFile": "Template/4-MainBeltVice/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8517"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BUgF6vPYMdpWE6NTEh0GkRuCA2fAJUuKDpiCVj8XDRc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BUgF6vPYMdpWE6NTEh0GkRuCA2fAJUuKDpiCVj8XDRc="}]}, {"Route": "Template/4-MainBeltVice/Mapper.cs.xxkklnb8ct.vm", "AssetFile": "Template/4-MainBeltVice/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8517"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"BUgF6vPYMdpWE6NTEh0GkRuCA2fAJUuKDpiCVj8XDRc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xxkklnb8ct"}, {"Name": "integrity", "Value": "sha256-BUgF6vPYMdpWE6NTEh0GkRuCA2fAJUuKDpiCVj8XDRc="}, {"Name": "label", "Value": "Template/4-MainBeltVice/Mapper.cs.vm"}]}, {"Route": "Template/4-MainBeltVice/Service.cs.vm", "AssetFile": "Template/4-MainBeltVice/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "127933"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"KQY3xdGUqNfs/Be3inxOIxSKiIqcR+/+oX0xP21xqjg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KQY3xdGUqNfs/Be3inxOIxSKiIqcR+/+oX0xP21xqjg="}]}, {"Route": "Template/4-MainBeltVice/Service.cs.vqt19tn45h.vm", "AssetFile": "Template/4-MainBeltVice/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "127933"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"KQY3xdGUqNfs/Be3inxOIxSKiIqcR+/+oX0xP21xqjg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vqt19tn45h"}, {"Name": "integrity", "Value": "sha256-KQY3xdGUqNfs/Be3inxOIxSKiIqcR+/+oX0xP21xqjg="}, {"Name": "label", "Value": "Template/4-MainBeltVice/Service.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/CrInput.cs.ngeet1hsuv.vm", "AssetFile": "Template/5-PrimarySecondary/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3742"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tzEly5OJvhoUn9lRujZkjwCzHk0nNVYL2wpLf2gih78=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ngeet1hsuv"}, {"Name": "integrity", "Value": "sha256-tzEly5OJvhoUn9lRujZkjwCzHk0nNVYL2wpLf2gih78="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/CrInput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/CrInput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3742"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tzEly5OJvhoUn9lRujZkjwCzHk0nNVYL2wpLf2gih78=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzEly5OJvhoUn9lRujZkjwCzHk0nNVYL2wpLf2gih78="}]}, {"Route": "Template/5-PrimarySecondary/DetailOutput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2027"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2/DH4rAeMYZ8+IC/wQLvhsL2hGRzj0g+Vmhp+0fwyCM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2/DH4rAeMYZ8+IC/wQLvhsL2hGRzj0g+Vmhp+0fwyCM="}]}, {"Route": "Template/5-PrimarySecondary/DetailOutput.cs.zijk1wjsq1.vm", "AssetFile": "Template/5-PrimarySecondary/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2027"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2/DH4rAeMYZ8+IC/wQLvhsL2hGRzj0g+Vmhp+0fwyCM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zijk1wjsq1"}, {"Name": "integrity", "Value": "sha256-2/DH4rAeMYZ8+IC/wQLvhsL2hGRzj0g+Vmhp+0fwyCM="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/DetailOutput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/Entity.cs.vm", "AssetFile": "Template/5-PrimarySecondary/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9366"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZWmzxxuaET832ZdPR4DD93cjm8+XbMnk3rrAeMy2LTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZWmzxxuaET832ZdPR4DD93cjm8+XbMnk3rrAeMy2LTY="}]}, {"Route": "Template/5-PrimarySecondary/Entity.cs.xh23aoltcf.vm", "AssetFile": "Template/5-PrimarySecondary/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9366"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZWmzxxuaET832ZdPR4DD93cjm8+XbMnk3rrAeMy2LTY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xh23aoltcf"}, {"Name": "integrity", "Value": "sha256-ZWmzxxuaET832ZdPR4DD93cjm8+XbMnk3rrAeMy2LTY="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/Entity.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/InfoOutput.cs.oh82wh4cr2.vm", "AssetFile": "Template/5-PrimarySecondary/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3708"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hCRLSpk5+X6GnEerhqq6A5uPCFoW4ntIRpnVTg+7610=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oh82wh4cr2"}, {"Name": "integrity", "Value": "sha256-hCRLSpk5+X6GnEerhqq6A5uPCFoW4ntIRpnVTg+7610="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/InfoOutput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/InfoOutput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3708"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hCRLSpk5+X6GnEerhqq6A5uPCFoW4ntIRpnVTg+7610=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hCRLSpk5+X6GnEerhqq6A5uPCFoW4ntIRpnVTg+7610="}]}, {"Route": "Template/5-PrimarySecondary/InlineEditor/InlineEditorOutput.cs.tnl9v4alke.vm", "AssetFile": "Template/5-PrimarySecondary/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5488"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6cT5NWvzsXRWYKGMWTSqtB24kTeNPPkqNM+L4+4IKW4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tnl9v4alke"}, {"Name": "integrity", "Value": "sha256-6cT5NWvzsXRWYKGMWTSqtB24kTeNPPkqNM+L4+4IKW4="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/InlineEditor/InlineEditorOutput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/InlineEditor/InlineEditorOutput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/InlineEditor/InlineEditorOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5488"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6cT5NWvzsXRWYKGMWTSqtB24kTeNPPkqNM+L4+4IKW4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6cT5NWvzsXRWYKGMWTSqtB24kTeNPPkqNM+L4+4IKW4="}]}, {"Route": "Template/5-PrimarySecondary/InlineEditor/ListOutput.cs.53oo4eg8g5.vm", "AssetFile": "Template/5-PrimarySecondary/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5929"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"D/TbcFtoC7W6HGXdKb/0k6I3meY/q5fSJ3lsYnjKjcU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "53oo4eg8g5"}, {"Name": "integrity", "Value": "sha256-D/TbcFtoC7W6HGXdKb/0k6I3meY/q5fSJ3lsYnjKjcU="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/InlineEditor/ListOutput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/InlineEditor/ListOutput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/InlineEditor/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5929"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"D/TbcFtoC7W6HGXdKb/0k6I3meY/q5fSJ3lsYnjKjcU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D/TbcFtoC7W6HGXdKb/0k6I3meY/q5fSJ3lsYnjKjcU="}]}, {"Route": "Template/5-PrimarySecondary/InlineEditor/Service.cs.rpyl9uvhol.vm", "AssetFile": "Template/5-PrimarySecondary/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "215500"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/7PWIBssh2BQ5HV0oDmLbQ6qKS0esPSlus9U/RhsEFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rpyl9uvhol"}, {"Name": "integrity", "Value": "sha256-/7PWIBssh2BQ5HV0oDmLbQ6qKS0esPSlus9U/RhsEFs="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/InlineEditor/Service.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/InlineEditor/Service.cs.vm", "AssetFile": "Template/5-PrimarySecondary/InlineEditor/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "215500"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/7PWIBssh2BQ5HV0oDmLbQ6qKS0esPSlus9U/RhsEFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/7PWIBssh2BQ5HV0oDmLbQ6qKS0esPSlus9U/RhsEFs="}]}, {"Route": "Template/5-PrimarySecondary/ListOutput.cs.fhgmlu58h0.vm", "AssetFile": "Template/5-PrimarySecondary/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3499"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"DUb4yMrkR2G6D3mwo5GMihYWpel5Yg1aGDrva6SAQh4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fhgmlu58h0"}, {"Name": "integrity", "Value": "sha256-DUb4yMrkR2G6D3mwo5GMihYWpel5Yg1aGDrva6SAQh4="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/ListOutput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/ListOutput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3499"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"DUb4yMrkR2G6D3mwo5GMihYWpel5Yg1aGDrva6SAQh4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DUb4yMrkR2G6D3mwo5GMihYWpel5Yg1aGDrva6SAQh4="}]}, {"Route": "Template/5-PrimarySecondary/ListQueryInput.cs.f5ri9xl8j5.vm", "AssetFile": "Template/5-PrimarySecondary/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4485"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"DYknC/hM+Zf6UiDkN1P3Vk7mOIfaNA1ttgZi9Jt8090=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f5ri9xl8j5"}, {"Name": "integrity", "Value": "sha256-DYknC/hM+Zf6UiDkN1P3Vk7mOIfaNA1ttgZi9Jt8090="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/ListQueryInput.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/ListQueryInput.cs.vm", "AssetFile": "Template/5-PrimarySecondary/ListQueryInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4485"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"DYknC/hM+Zf6UiDkN1P3Vk7mOIfaNA1ttgZi9Jt8090=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DYknC/hM+Zf6UiDkN1P3Vk7mOIfaNA1ttgZi9Jt8090="}]}, {"Route": "Template/5-PrimarySecondary/Mapper.cs.iypqofjx4v.vm", "AssetFile": "Template/5-PrimarySecondary/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7293"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FtaRMpKxMHq4EhHt1xtZYQt9F9hEQ+5uGlqLWCtEH5M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iypqofjx4v"}, {"Name": "integrity", "Value": "sha256-FtaRMpKxMHq4EhHt1xtZYQt9F9hEQ+5uGlqLWCtEH5M="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/Mapper.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/Mapper.cs.vm", "AssetFile": "Template/5-PrimarySecondary/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7293"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FtaRMpKxMHq4EhHt1xtZYQt9F9hEQ+5uGlqLWCtEH5M=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FtaRMpKxMHq4EhHt1xtZYQt9F9hEQ+5uGlqLWCtEH5M="}]}, {"Route": "Template/5-PrimarySecondary/Service.cs.shp0j0merz.vm", "AssetFile": "Template/5-PrimarySecondary/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "216615"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EFShAjDJL1BeQPIR9Sm38rIqmN/mA2OjkCYAoxPPfKo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "shp0j0merz"}, {"Name": "integrity", "Value": "sha256-EFShAjDJL1BeQPIR9Sm38rIqmN/mA2OjkCYAoxPPfKo="}, {"Name": "label", "Value": "Template/5-PrimarySecondary/Service.cs.vm"}]}, {"Route": "Template/5-PrimarySecondary/Service.cs.vm", "AssetFile": "Template/5-PrimarySecondary/Service.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "216615"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"EFShAjDJL1BeQPIR9Sm38rIqmN/mA2OjkCYAoxPPfKo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EFShAjDJL1BeQPIR9Sm38rIqmN/mA2OjkCYAoxPPfKo="}]}, {"Route": "Template/Detail.vue.02zmws8n1q.vm", "AssetFile": "Template/Detail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41379"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CFW46qujCqDk8g8onDolKe5xXOpSFoGwl8+/Rcti3wA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "02zmws8n1q"}, {"Name": "integrity", "Value": "sha256-CFW46qujCqDk8g8onDolKe5xXOpSFoGwl8+/Rcti3wA="}, {"Name": "label", "Value": "Template/Detail.vue.vm"}]}, {"Route": "Template/Detail.vue.vm", "AssetFile": "Template/Detail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41379"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"CFW46qujCqDk8g8onDolKe5xXOpSFoGwl8+/Rcti3wA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CFW46qujCqDk8g8onDolKe5xXOpSFoGwl8+/Rcti3wA="}]}, {"Route": "Template/ExportJson.json.dps7l9sqv2.vm", "AssetFile": "Template/ExportJson.json.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ecausx/YKnoG+e5KkvKSuqrzgzvJ153oJlwea2BYW98=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dps7l9sqv2"}, {"Name": "integrity", "Value": "sha256-Ecausx/YKnoG+e5KkvKSuqrzgzvJ153oJlwea2BYW98="}, {"Name": "label", "Value": "Template/ExportJson.json.vm"}]}, {"Route": "Template/ExportJson.json.vm", "AssetFile": "Template/ExportJson.json.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1102"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ecausx/YKnoG+e5KkvKSuqrzgzvJ153oJlwea2BYW98=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ecausx/YKnoG+e5KkvKSuqrzgzvJ153oJlwea2BYW98="}]}, {"Route": "Template/Form.vue.8ofjheuszg.vm", "AssetFile": "Template/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71052"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6KDDjINguNeztaZjvlRD+HMxiI2feOYa58CPSVEa/hU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8ofjheuszg"}, {"Name": "integrity", "Value": "sha256-6KDDjINguNeztaZjvlRD+HMxiI2feOYa58CPSVEa/hU="}, {"Name": "label", "Value": "Template/Form.vue.vm"}]}, {"Route": "Template/Form.vue.vm", "AssetFile": "Template/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71052"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6KDDjINguNeztaZjvlRD+HMxiI2feOYa58CPSVEa/hU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6KDDjINguNeztaZjvlRD+HMxiI2feOYa58CPSVEa/hU="}]}, {"Route": "Template/IService.cs.6z2c4a1zm5.vm", "AssetFile": "Template/IService.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "169"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ElZFZJRjK4Vj60Rm7Kj2z28u8RTMvWPTs7mfNwI+39c=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6z2c4a1zm5"}, {"Name": "integrity", "Value": "sha256-ElZFZJRjK4Vj60Rm7Kj2z28u8RTMvWPTs7mfNwI+39c="}, {"Name": "label", "Value": "Template/IService.cs.vm"}]}, {"Route": "Template/IService.cs.vm", "AssetFile": "Template/IService.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "169"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ElZFZJRjK4Vj60Rm7Kj2z28u8RTMvWPTs7mfNwI+39c=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ElZFZJRjK4Vj60Rm7Kj2z28u8RTMvWPTs7mfNwI+39c="}]}, {"Route": "Template/PureForm/appWorkflowIndex.vue.07kljmtas6.vm", "AssetFile": "Template/PureForm/appWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2285"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hEs43/qQzT/LtjZ4LIJWSFCLJXQ4kkwyxHsNC0nuFn8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "07kljmtas6"}, {"Name": "integrity", "Value": "sha256-hEs43/qQzT/LtjZ4LIJWSFCLJXQ4kkwyxHsNC0nuFn8="}, {"Name": "label", "Value": "Template/PureForm/appWorkflowIndex.vue.vm"}]}, {"Route": "Template/PureForm/appWorkflowIndex.vue.vm", "AssetFile": "Template/PureForm/appWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2285"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hEs43/qQzT/LtjZ4LIJWSFCLJXQ4kkwyxHsNC0nuFn8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hEs43/qQzT/LtjZ4LIJWSFCLJXQ4kkwyxHsNC0nuFn8="}]}, {"Route": "Template/PureForm/index.vue.geqgodagig.vm", "AssetFile": "Template/PureForm/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2151"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WAomPo7PqofMrLKWRVfKuqeItZZlFH5sOUpiNci74Yg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "geqgodagig"}, {"Name": "integrity", "Value": "sha256-WAomPo7PqofMrLKWRVfKuqeItZZlFH5sOUpiNci74Yg="}, {"Name": "label", "Value": "Template/PureForm/index.vue.vm"}]}, {"Route": "Template/PureForm/index.vue.vm", "AssetFile": "Template/PureForm/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2151"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"WAomPo7PqofMrLKWRVfKuqeItZZlFH5sOUpiNci74Yg=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WAomPo7PqofMrLKWRVfKuqeItZZlFH5sOUpiNci74Yg="}]}, {"Route": "Template/SubTable/CrInput.cs.t2g05xu20f.vm", "AssetFile": "Template/SubTable/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1879"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"mwU0xf5OCxvbIYs41mPX2pK2TO3B3Zq9417g6ZDZA/k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t2g05xu20f"}, {"Name": "integrity", "Value": "sha256-mwU0xf5OCxvbIYs41mPX2pK2TO3B3Zq9417g6ZDZA/k="}, {"Name": "label", "Value": "Template/SubTable/CrInput.cs.vm"}]}, {"Route": "Template/SubTable/CrInput.cs.vm", "AssetFile": "Template/SubTable/CrInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1879"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"mwU0xf5OCxvbIYs41mPX2pK2TO3B3Zq9417g6ZDZA/k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mwU0xf5OCxvbIYs41mPX2pK2TO3B3Zq9417g6ZDZA/k="}]}, {"Route": "Template/SubTable/DetailOutput.cs.tlw97kaupu.vm", "AssetFile": "Template/SubTable/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1502"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UWTyPDxBg2M6pq+Z+UnDwOV3eJ/ZlDRD2cZ0BAVaRbA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tlw97kaupu"}, {"Name": "integrity", "Value": "sha256-UWTyPDxBg2M6pq+Z+UnDwOV3eJ/ZlDRD2cZ0BAVaRbA="}, {"Name": "label", "Value": "Template/SubTable/DetailOutput.cs.vm"}]}, {"Route": "Template/SubTable/DetailOutput.cs.vm", "AssetFile": "Template/SubTable/DetailOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1502"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"UWTyPDxBg2M6pq+Z+UnDwOV3eJ/ZlDRD2cZ0BAVaRbA=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UWTyPDxBg2M6pq+Z+UnDwOV3eJ/ZlDRD2cZ0BAVaRbA="}]}, {"Route": "Template/SubTable/Entity.cs.vm", "AssetFile": "Template/SubTable/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7787"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hjryoOoYj+yP0jREDaqZPYVPrVhL0eLMfsQynHBzEPM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjryoOoYj+yP0jREDaqZPYVPrVhL0eLMfsQynHBzEPM="}]}, {"Route": "Template/SubTable/Entity.cs.yil6bm5jrs.vm", "AssetFile": "Template/SubTable/Entity.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7787"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hjryoOoYj+yP0jREDaqZPYVPrVhL0eLMfsQynHBzEPM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yil6bm5jrs"}, {"Name": "integrity", "Value": "sha256-hjryoOoYj+yP0jREDaqZPYVPrVhL0eLMfsQynHBzEPM="}, {"Name": "label", "Value": "Template/SubTable/Entity.cs.vm"}]}, {"Route": "Template/SubTable/InfoOutput.cs.sknqdpd3en.vm", "AssetFile": "Template/SubTable/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2667"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3LpSjg3cP8F23e82LXPwK6jLG8Tjx2h+5o/Ic2TXkrc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sknqdpd3en"}, {"Name": "integrity", "Value": "sha256-3LpSjg3cP8F23e82LXPwK6jLG8Tjx2h+5o/Ic2TXkrc="}, {"Name": "label", "Value": "Template/SubTable/InfoOutput.cs.vm"}]}, {"Route": "Template/SubTable/InfoOutput.cs.vm", "AssetFile": "Template/SubTable/InfoOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2667"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"3LpSjg3cP8F23e82LXPwK6jLG8Tjx2h+5o/Ic2TXkrc=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3LpSjg3cP8F23e82LXPwK6jLG8Tjx2h+5o/Ic2TXkrc="}]}, {"Route": "Template/SubTable/ListOutput.cs.vm", "AssetFile": "Template/SubTable/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1598"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fGpUMvwfB9262HB/lNOpRVKaGL6WTEZq1P3+II4Sizw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fGpUMvwfB9262HB/lNOpRVKaGL6WTEZq1P3+II4Sizw="}]}, {"Route": "Template/SubTable/ListOutput.cs.wd98cym405.vm", "AssetFile": "Template/SubTable/ListOutput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1598"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fGpUMvwfB9262HB/lNOpRVKaGL6WTEZq1P3+II4Sizw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wd98cym405"}, {"Name": "integrity", "Value": "sha256-fGpUMvwfB9262HB/lNOpRVKaGL6WTEZq1P3+II4Sizw="}, {"Name": "label", "Value": "Template/SubTable/ListOutput.cs.vm"}]}, {"Route": "Template/SubTable/Mapper.cs.vm", "AssetFile": "Template/SubTable/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5673"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qNdYR1NRL10dsrjF4SXXOh8iLAev9kM1oESuq75oGV8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qNdYR1NRL10dsrjF4SXXOh8iLAev9kM1oESuq75oGV8="}]}, {"Route": "Template/SubTable/Mapper.cs.wi0wlqfh7u.vm", "AssetFile": "Template/SubTable/Mapper.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5673"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"qNdYR1NRL10dsrjF4SXXOh8iLAev9kM1oESuq75oGV8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wi0wlqfh7u"}, {"Name": "integrity", "Value": "sha256-qNdYR1NRL10dsrjF4SXXOh8iLAev9kM1oESuq75oGV8="}, {"Name": "label", "Value": "Template/SubTable/Mapper.cs.vm"}]}, {"Route": "Template/UpInput.cs.ge1a7ml8z1.vm", "AssetFile": "Template/UpInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "425"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aAuGjUqyD+SNYp0jRzsRI74MYbKlw8yKll7eO5zRckU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ge1a7ml8z1"}, {"Name": "integrity", "Value": "sha256-aAuGjUqyD+SNYp0jRzsRI74MYbKlw8yKll7eO5zRckU="}, {"Name": "label", "Value": "Template/UpInput.cs.vm"}]}, {"Route": "Template/UpInput.cs.vm", "AssetFile": "Template/UpInput.cs.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "425"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aAuGjUqyD+SNYp0jRzsRI74MYbKlw8yKll7eO5zRckU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aAuGjUqyD+SNYp0jRzsRI74MYbKlw8yKll7eO5zRckU="}]}, {"Route": "Template/WorkflowForm.vue.vm", "AssetFile": "Template/WorkflowForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60798"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LMUxrExkX2xSnozlGbqb2Dj32PWNbG6JJw89oxLPdio=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LMUxrExkX2xSnozlGbqb2Dj32PWNbG6JJw89oxLPdio="}]}, {"Route": "Template/WorkflowForm.vue.wbooksbwkt.vm", "AssetFile": "Template/WorkflowForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "60798"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"LMUxrExkX2xSnozlGbqb2Dj32PWNbG6JJw89oxLPdio=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wbooksbwkt"}, {"Name": "integrity", "Value": "sha256-LMUxrExkX2xSnozlGbqb2Dj32PWNbG6JJw89oxLPdio="}, {"Name": "label", "Value": "Template/WorkflowForm.vue.vm"}]}, {"Route": "Template/WorkflowIndex.vue.lbyjc33wov.vm", "AssetFile": "Template/WorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "69243"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fyP1bIeTYNmm9tdRiUah73DFIB9/WZz3vE9ayJNkawY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lbyjc33wov"}, {"Name": "integrity", "Value": "sha256-fyP1bIeTYNmm9tdRiUah73DFIB9/WZz3vE9ayJNkawY="}, {"Name": "label", "Value": "Template/WorkflowIndex.vue.vm"}]}, {"Route": "Template/WorkflowIndex.vue.vm", "AssetFile": "Template/WorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "69243"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fyP1bIeTYNmm9tdRiUah73DFIB9/WZz3vE9ayJNkawY=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fyP1bIeTYNmm9tdRiUah73DFIB9/WZz3vE9ayJNkawY="}]}, {"Route": "Template/appDetail.vue.d61by9m05p.vm", "AssetFile": "Template/appDetail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32985"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tWCzQWTZ2ZUYZpZGNlp0gcN1FKVB9s+noK3hLCsmOQE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d61by9m05p"}, {"Name": "integrity", "Value": "sha256-tWCzQWTZ2ZUYZpZGNlp0gcN1FKVB9s+noK3hLCsmOQE="}, {"Name": "label", "Value": "Template/appDetail.vue.vm"}]}, {"Route": "Template/appDetail.vue.vm", "AssetFile": "Template/appDetail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32985"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tWCzQWTZ2ZUYZpZGNlp0gcN1FKVB9s+noK3hLCsmOQE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tWCzQWTZ2ZUYZpZGNlp0gcN1FKVB9s+noK3hLCsmOQE="}]}, {"Route": "Template/appForm.vue.pn4jy7s3y7.vm", "AssetFile": "Template/appForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "102706"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Vfw9GOWv6QEbQoNllRYuK68y5SQONGdNPeDbEUiSqo8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pn4jy7s3y7"}, {"Name": "integrity", "Value": "sha256-Vfw9GOWv6QEbQoNllRYuK68y5SQONGdNPeDbEUiSqo8="}, {"Name": "label", "Value": "Template/appForm.vue.vm"}]}, {"Route": "Template/appForm.vue.vm", "AssetFile": "Template/appForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "102706"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Vfw9GOWv6QEbQoNllRYuK68y5SQONGdNPeDbEUiSqo8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vfw9GOWv6QEbQoNllRYuK68y5SQONGdNPeDbEUiSqo8="}]}, {"Route": "Template/appIndex.vue.vm", "AssetFile": "Template/appIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26761"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8k6+HB2Fd9c1ojow48ch3KA2ZLT10uAE6K8UomKkXCQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8k6+HB2Fd9c1ojow48ch3KA2ZLT10uAE6K8UomKkXCQ="}]}, {"Route": "Template/appIndex.vue.y09hfe2yy6.vm", "AssetFile": "Template/appIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26761"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"8k6+HB2Fd9c1ojow48ch3KA2ZLT10uAE6K8UomKkXCQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y09hfe2yy6"}, {"Name": "integrity", "Value": "sha256-8k6+HB2Fd9c1ojow48ch3KA2ZLT10uAE6K8UomKkXCQ="}, {"Name": "label", "Value": "Template/appIndex.vue.vm"}]}, {"Route": "Template/appWorkflowForm.vue.8htotf5ldv.vm", "AssetFile": "Template/appWorkflowForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "94016"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"xOt3V1XR6ZW3fbr1OGaogRpARa2ZMZWU0tlMH2pUf9E=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8htotf5ldv"}, {"Name": "integrity", "Value": "sha256-xOt3V1XR6ZW3fbr1OGaogRpARa2ZMZWU0tlMH2pUf9E="}, {"Name": "label", "Value": "Template/appWorkflowForm.vue.vm"}]}, {"Route": "Template/appWorkflowForm.vue.vm", "AssetFile": "Template/appWorkflowForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "94016"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"xOt3V1XR6ZW3fbr1OGaogRpARa2ZMZWU0tlMH2pUf9E=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xOt3V1XR6ZW3fbr1OGaogRpARa2ZMZWU0tlMH2pUf9E="}]}, {"Route": "Template/appWorkflowIndex.vue.c898d1eeip.vm", "AssetFile": "Template/appWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31399"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"RO4hQrdvVh3k6/6XSPdik392TXDt+e3fPXsfgspyNes=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c898d1eeip"}, {"Name": "integrity", "Value": "sha256-RO4hQrdvVh3k6/6XSPdik392TXDt+e3fPXsfgspyNes="}, {"Name": "label", "Value": "Template/appWorkflowIndex.vue.vm"}]}, {"Route": "Template/appWorkflowIndex.vue.vm", "AssetFile": "Template/appWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31399"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"RO4hQrdvVh3k6/6XSPdik392TXDt+e3fPXsfgspyNes=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RO4hQrdvVh3k6/6XSPdik392TXDt+e3fPXsfgspyNes="}]}, {"Route": "Template/columnList.js.vm", "AssetFile": "Template/columnList.js.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "68"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Dpm2r8hDHRE95h63a/330F93Af2Dj3cUB8K7MYLKINk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Dpm2r8hDHRE95h63a/330F93Af2Dj3cUB8K7MYLKINk="}]}, {"Route": "Template/columnList.js.y3k5whyih2.vm", "AssetFile": "Template/columnList.js.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "68"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Dpm2r8hDHRE95h63a/330F93Af2Dj3cUB8K7MYLKINk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y3k5whyih2"}, {"Name": "integrity", "Value": "sha256-Dpm2r8hDHRE95h63a/330F93Af2Dj3cUB8K7MYLKINk="}, {"Name": "label", "Value": "Template/columnList.js.vm"}]}, {"Route": "Template/editorIndex.vue.v52zvl8aht.vm", "AssetFile": "Template/editorIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53951"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"TRcRUq3gqefq7xQdYhVhe69jiCkdx9/rec7ZKnw+EQo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v52zvl8aht"}, {"Name": "integrity", "Value": "sha256-TRcRUq3gqefq7xQdYhVhe69jiCkdx9/rec7ZKnw+EQo="}, {"Name": "label", "Value": "Template/editorIndex.vue.vm"}]}, {"Route": "Template/editorIndex.vue.vm", "AssetFile": "Template/editorIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53951"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"TRcRUq3gqefq7xQdYhVhe69jiCkdx9/rec7ZKnw+EQo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TRcRUq3gqefq7xQdYhVhe69jiCkdx9/rec7ZKnw+EQo="}]}, {"Route": "Template/editorWorkflowIndex.vue.vm", "AssetFile": "Template/editorWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "58244"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"rw777YNR2gSrb1syw+qFwSqQnXtoFtIfJulpSRSVRy8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rw777YNR2gSrb1syw+qFwSqQnXtoFtIfJulpSRSVRy8="}]}, {"Route": "Template/editorWorkflowIndex.vue.xw7twxmqbh.vm", "AssetFile": "Template/editorWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "58244"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"rw777YNR2gSrb1syw+qFwSqQnXtoFtIfJulpSRSVRy8=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xw7twxmqbh"}, {"Name": "integrity", "Value": "sha256-rw777YNR2gSrb1syw+qFwSqQnXtoFtIfJulpSRSVRy8="}, {"Name": "label", "Value": "Template/editorWorkflowIndex.vue.vm"}]}, {"Route": "Template/extraForm.vue.a1cul1oics.vm", "AssetFile": "Template/extraForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22461"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bnHAaTzcpapRV2niF3aBDzNZo8ltYmqrJokRBXxCX5A=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a1cul1oics"}, {"Name": "integrity", "Value": "sha256-bnHAaTzcpapRV2niF3aBDzNZo8ltYmqrJokRBXxCX5A="}, {"Name": "label", "Value": "Template/extraForm.vue.vm"}]}, {"Route": "Template/extraForm.vue.vm", "AssetFile": "Template/extraForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22461"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bnHAaTzcpapRV2niF3aBDzNZo8ltYmqrJokRBXxCX5A=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bnHAaTzcpapRV2niF3aBDzNZo8ltYmqrJokRBXxCX5A="}]}, {"Route": "Template/index.vue.2gyygkspxi.vm", "AssetFile": "Template/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "66662"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4sKa9TbT+4cyjlhFIsgPbxRl14U33qUbkJQ2JfWcgvo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2gyygkspxi"}, {"Name": "integrity", "Value": "sha256-4sKa9TbT+4cyjlhFIsgPbxRl14U33qUbkJQ2JfWcgvo="}, {"Name": "label", "Value": "Template/index.vue.vm"}]}, {"Route": "Template/index.vue.vm", "AssetFile": "Template/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "66662"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"4sKa9TbT+4cyjlhFIsgPbxRl14U33qUbkJQ2JfWcgvo=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sKa9TbT+4cyjlhFIsgPbxRl14U33qUbkJQ2JfWcgvo="}]}, {"Route": "Template/superQueryJson.js.jvbd46h0ua.vm", "AssetFile": "Template/superQueryJson.js.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "78"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"oRYqHSmq4CWN2UGBDVzf3fEUZeHWrM6kK3pU1XFM+hs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jvbd46h0ua"}, {"Name": "integrity", "Value": "sha256-oRYqHSmq4CWN2UGBDVzf3fEUZeHWrM6kK3pU1XFM+hs="}, {"Name": "label", "Value": "Template/superQueryJson.js.vm"}]}, {"Route": "Template/superQueryJson.js.vm", "AssetFile": "Template/superQueryJson.js.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"oRYqHSmq4CWN2UGBDVzf3fEUZeHWrM6kK3pU1XFM+hs=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oRYqHSmq4CWN2UGBDVzf3fEUZeHWrM6kK3pU1XFM+hs="}]}, {"Route": "Template/vue3/Detail.vue.sryyvlx4bm.vm", "AssetFile": "Template/vue3/Detail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26547"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"eBhQ2H2ktFiAx675NxbUA+/8X3URjDn7oW+dngr3oAM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sryyvlx4bm"}, {"Name": "integrity", "Value": "sha256-eBhQ2H2ktFiAx675NxbUA+/8X3URjDn7oW+dngr3oAM="}, {"Name": "label", "Value": "Template/vue3/Detail.vue.vm"}]}, {"Route": "Template/vue3/Detail.vue.vm", "AssetFile": "Template/vue3/Detail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26547"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"eBhQ2H2ktFiAx675NxbUA+/8X3URjDn7oW+dngr3oAM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eBhQ2H2ktFiAx675NxbUA+/8X3URjDn7oW+dngr3oAM="}]}, {"Route": "Template/vue3/Form.vue.hbw17fbfor.vm", "AssetFile": "Template/vue3/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "48145"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"IzNMTGtcg3H7N1D0AwHl4txAk74A7cfKIPeatK4D210=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hbw17fbfor"}, {"Name": "integrity", "Value": "sha256-IzNMTGtcg3H7N1D0AwHl4txAk74A7cfKIPeatK4D210="}, {"Name": "label", "Value": "Template/vue3/Form.vue.vm"}]}, {"Route": "Template/vue3/Form.vue.vm", "AssetFile": "Template/vue3/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "48145"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"IzNMTGtcg3H7N1D0AwHl4txAk74A7cfKIPeatK4D210=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IzNMTGtcg3H7N1D0AwHl4txAk74A7cfKIPeatK4D210="}]}, {"Route": "Template/vue3/InlineEditing/Form.vue.7pchvsie26.vm", "AssetFile": "Template/vue3/InlineEditing/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21124"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"i2mRRiB0heEgxqEG1vq9dufp6PyYGVL/vDSDjH6vJAk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7pchvsie26"}, {"Name": "integrity", "Value": "sha256-i2mRRiB0heEgxqEG1vq9dufp6PyYGVL/vDSDjH6vJAk="}, {"Name": "label", "Value": "Template/vue3/InlineEditing/Form.vue.vm"}]}, {"Route": "Template/vue3/InlineEditing/Form.vue.vm", "AssetFile": "Template/vue3/InlineEditing/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21124"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"i2mRRiB0heEgxqEG1vq9dufp6PyYGVL/vDSDjH6vJAk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i2mRRiB0heEgxqEG1vq9dufp6PyYGVL/vDSDjH6vJAk="}]}, {"Route": "Template/vue3/InlineEditing/index.vue.vm", "AssetFile": "Template/vue3/InlineEditing/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "50604"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7lkFa47S2Wis+r6EiKWtfuW/dQ9UwqxAvXgF5f1H0Us=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7lkFa47S2Wis+r6EiKWtfuW/dQ9UwqxAvXgF5f1H0Us="}]}, {"Route": "Template/vue3/InlineEditing/index.vue.ywj5upxnv9.vm", "AssetFile": "Template/vue3/InlineEditing/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "50604"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"7lkFa47S2Wis+r6EiKWtfuW/dQ9UwqxAvXgF5f1H0Us=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ywj5upxnv9"}, {"Name": "integrity", "Value": "sha256-7lkFa47S2Wis+r6EiKWtfuW/dQ9UwqxAvXgF5f1H0Us="}, {"Name": "label", "Value": "Template/vue3/InlineEditing/index.vue.vm"}]}, {"Route": "Template/vue3/PureForm/Form.vue.9qsssjblrh.vm", "AssetFile": "Template/vue3/PureForm/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "42547"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cThM+q9EvFAvRE1piUZFfjAxh6Z603GB6Ta54Pnw2ck=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9qsssjblrh"}, {"Name": "integrity", "Value": "sha256-cThM+q9EvFAvRE1piUZFfjAxh6Z603GB6Ta54Pnw2ck="}, {"Name": "label", "Value": "Template/vue3/PureForm/Form.vue.vm"}]}, {"Route": "Template/vue3/PureForm/Form.vue.vm", "AssetFile": "Template/vue3/PureForm/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "42547"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"cThM+q9EvFAvRE1piUZFfjAxh6Z603GB6Ta54Pnw2ck=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cThM+q9EvFAvRE1piUZFfjAxh6Z603GB6Ta54Pnw2ck="}]}, {"Route": "Template/vue3/PureForm/index.vue.vm", "AssetFile": "Template/vue3/PureForm/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6Sl2v39g9QqsDqNuk5UGG8hAuF98gbHiY3dh0sPD/i0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6Sl2v39g9QqsDqNuk5UGG8hAuF98gbHiY3dh0sPD/i0="}]}, {"Route": "Template/vue3/PureForm/index.vue.vql860v89r.vm", "AssetFile": "Template/vue3/PureForm/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "476"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"6Sl2v39g9QqsDqNuk5UGG8hAuF98gbHiY3dh0sPD/i0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vql860v89r"}, {"Name": "integrity", "Value": "sha256-6Sl2v39g9QqsDqNuk5UGG8hAuF98gbHiY3dh0sPD/i0="}, {"Name": "label", "Value": "Template/vue3/PureForm/index.vue.vm"}]}, {"Route": "Template/vue3/WorkFlow/ExportJson.json.17r7zva0xs.vm", "AssetFile": "Template/vue3/WorkFlow/ExportJson.json.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1231"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"k/vnfKFPenzhnHfozmKT1wgvdZmSRPXKscDgvV85/fk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "17r7zva0xs"}, {"Name": "integrity", "Value": "sha256-k/vnfKFPenzhnHfozmKT1wgvdZmSRPXKscDgvV85/fk="}, {"Name": "label", "Value": "Template/vue3/WorkFlow/ExportJson.json.vm"}]}, {"Route": "Template/vue3/WorkFlow/ExportJson.json.vm", "AssetFile": "Template/vue3/WorkFlow/ExportJson.json.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1231"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"k/vnfKFPenzhnHfozmKT1wgvdZmSRPXKscDgvV85/fk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k/vnfKFPenzhnHfozmKT1wgvdZmSRPXKscDgvV85/fk="}]}, {"Route": "Template/vue3/WorkFlow/Form.vue.c3oiynsezy.vm", "AssetFile": "Template/vue3/WorkFlow/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39578"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iFcuF/49/I6z9xpdVY2J4cpInI2RtADPTvBZ7Ez2KxU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c3oiyn<PERSON>zy"}, {"Name": "integrity", "Value": "sha256-iFcuF/49/I6z9xpdVY2J4cpInI2RtADPTvBZ7Ez2KxU="}, {"Name": "label", "Value": "Template/vue3/WorkFlow/Form.vue.vm"}]}, {"Route": "Template/vue3/WorkFlow/Form.vue.vm", "AssetFile": "Template/vue3/WorkFlow/Form.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39578"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iFcuF/49/I6z9xpdVY2J4cpInI2RtADPTvBZ7Ez2KxU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iFcuF/49/I6z9xpdVY2J4cpInI2RtADPTvBZ7Ez2KxU="}]}, {"Route": "Template/vue3/WorkFlow/PureForm/index.vue.czbum5nhav.vm", "AssetFile": "Template/vue3/WorkFlow/PureForm/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2190"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"KFJxVOZbzcdIa82Ih8S/67ZPwaP6pqY4TUARAHG1Jj4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "czbum5nhav"}, {"Name": "integrity", "Value": "sha256-KFJxVOZbzcdIa82Ih8S/67ZPwaP6pqY4TUARAHG1Jj4="}, {"Name": "label", "Value": "Template/vue3/WorkFlow/PureForm/index.vue.vm"}]}, {"Route": "Template/vue3/WorkFlow/PureForm/index.vue.vm", "AssetFile": "Template/vue3/WorkFlow/PureForm/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2190"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"KFJxVOZbzcdIa82Ih8S/67ZPwaP6pqY4TUARAHG1Jj4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KFJxVOZbzcdIa82Ih8S/67ZPwaP6pqY4TUARAHG1Jj4="}]}, {"Route": "Template/vue3/api.ts.mqiodrodu9.vm", "AssetFile": "Template/vue3/api.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1245"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"IlItycoiHO3WXUOQX8w1grPLgPeshEzYYzH0dRsovXU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mqiodrodu9"}, {"Name": "integrity", "Value": "sha256-IlItycoiHO3WXUOQX8w1grPLgPeshEzYYzH0dRsovXU="}, {"Name": "label", "Value": "Template/vue3/api.ts.vm"}]}, {"Route": "Template/vue3/api.ts.vm", "AssetFile": "Template/vue3/api.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1245"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"IlItycoiHO3WXUOQX8w1grPLgPeshEzYYzH0dRsovXU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IlItycoiHO3WXUOQX8w1grPLgPeshEzYYzH0dRsovXU="}]}, {"Route": "Template/vue3/appDetail.vue.kxgbtzhu6o.vm", "AssetFile": "Template/vue3/appDetail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31603"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"eGPFvyn+xip1vm/+Xa7o1LqCdQsnLs+fe0Dvj6Dbd4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kxgbtzhu6o"}, {"Name": "integrity", "Value": "sha256-eGPFvyn+xip1vm/+Xa7o1LqCdQsnLs+fe0Dvj6Dbd4w="}, {"Name": "label", "Value": "Template/vue3/appDetail.vue.vm"}]}, {"Route": "Template/vue3/appDetail.vue.vm", "AssetFile": "Template/vue3/appDetail.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31603"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"eGPFvyn+xip1vm/+Xa7o1LqCdQsnLs+fe0Dvj6Dbd4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eGPFvyn+xip1vm/+Xa7o1LqCdQsnLs+fe0Dvj6Dbd4w="}]}, {"Route": "Template/vue3/appForm.vue.27hktv8c31.vm", "AssetFile": "Template/vue3/appForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "96826"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Di364O1/Ri8dPeQQACYyrhsIuau9e3t0vVVXvvMKYaE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "27hktv8c31"}, {"Name": "integrity", "Value": "sha256-Di364O1/Ri8dPeQQACYyrhsIuau9e3t0vVVXvvMKYaE="}, {"Name": "label", "Value": "Template/vue3/appForm.vue.vm"}]}, {"Route": "Template/vue3/appForm.vue.vm", "AssetFile": "Template/vue3/appForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "96826"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Di364O1/Ri8dPeQQACYyrhsIuau9e3t0vVVXvvMKYaE=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Di364O1/Ri8dPeQQACYyrhsIuau9e3t0vVVXvvMKYaE="}]}, {"Route": "Template/vue3/appIndex.vue.k6lxg37xik.vm", "AssetFile": "Template/vue3/appIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26610"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hGqM78/2/Qzv2DvmFodfJCQEuB+GyrBfHMqtJ5jD+RI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k6lxg37xik"}, {"Name": "integrity", "Value": "sha256-hGqM78/2/Qzv2DvmFodfJCQEuB+GyrBfHMqtJ5jD+RI="}, {"Name": "label", "Value": "Template/vue3/appIndex.vue.vm"}]}, {"Route": "Template/vue3/appIndex.vue.vm", "AssetFile": "Template/vue3/appIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26610"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"hGqM78/2/Qzv2DvmFodfJCQEuB+GyrBfHMqtJ5jD+RI=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hGqM78/2/Qzv2DvmFodfJCQEuB+GyrBfHMqtJ5jD+RI="}]}, {"Route": "Template/vue3/appWorkflowForm.vue.059jnk1ce1.vm", "AssetFile": "Template/vue3/appWorkflowForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "89460"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/PNDM0PZxSy3wsxk85ohZELOtjzw1tPhzCAEMWXXhu4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "059jnk1ce1"}, {"Name": "integrity", "Value": "sha256-/PNDM0PZxSy3wsxk85ohZELOtjzw1tPhzCAEMWXXhu4="}, {"Name": "label", "Value": "Template/vue3/appWorkflowForm.vue.vm"}]}, {"Route": "Template/vue3/appWorkflowForm.vue.vm", "AssetFile": "Template/vue3/appWorkflowForm.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89460"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"/PNDM0PZxSy3wsxk85ohZELOtjzw1tPhzCAEMWXXhu4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/PNDM0PZxSy3wsxk85ohZELOtjzw1tPhzCAEMWXXhu4="}]}, {"Route": "Template/vue3/appWorkflowIndex.vue.bsnfi5g5hi.vm", "AssetFile": "Template/vue3/appWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31054"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"K+zOD1pn2t1Da+TAadWkQ3CqV9EnURVAy+S+hoR251o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bsnfi5g5hi"}, {"Name": "integrity", "Value": "sha256-K+zOD1pn2t1Da+TAadWkQ3CqV9EnURVAy+S+hoR251o="}, {"Name": "label", "Value": "Template/vue3/appWorkflowIndex.vue.vm"}]}, {"Route": "Template/vue3/appWorkflowIndex.vue.vm", "AssetFile": "Template/vue3/appWorkflowIndex.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31054"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"K+zOD1pn2t1Da+TAadWkQ3CqV9EnURVAy+S+hoR251o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K+zOD1pn2t1Da+TAadWkQ3CqV9EnURVAy+S+hoR251o="}]}, {"Route": "Template/vue3/columnList.ts.pyefte6hgg.vm", "AssetFile": "Template/vue3/columnList.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"gc7D0R7ggXtqLSVlg0M6kiikakL9Kv2vYJRPvbIFa4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pyefte6hgg"}, {"Name": "integrity", "Value": "sha256-gc7D0R7ggXtqLSVlg0M6kiikakL9Kv2vYJRPvbIFa4o="}, {"Name": "label", "Value": "Template/vue3/columnList.ts.vm"}]}, {"Route": "Template/vue3/columnList.ts.vm", "AssetFile": "Template/vue3/columnList.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"gc7D0R7ggXtqLSVlg0M6kiikakL9Kv2vYJRPvbIFa4o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gc7D0R7ggXtqLSVlg0M6kiikakL9Kv2vYJRPvbIFa4o="}]}, {"Route": "Template/vue3/index.vue.3yhoo7ype2.vm", "AssetFile": "Template/vue3/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44350"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JZviQqf4eRqAZ8jQQ9OJWRCCtrtke6udWAz6NEY65wM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3yhoo7ype2"}, {"Name": "integrity", "Value": "sha256-JZviQqf4eRqAZ8jQQ9OJWRCCtrtke6udWAz6NEY65wM="}, {"Name": "label", "Value": "Template/vue3/index.vue.vm"}]}, {"Route": "Template/vue3/index.vue.vm", "AssetFile": "Template/vue3/index.vue.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44350"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"JZviQqf4eRqAZ8jQQ9OJWRCCtrtke6udWAz6NEY65wM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JZviQqf4eRqAZ8jQQ9OJWRCCtrtke6udWAz6NEY65wM="}]}, {"Route": "Template/vue3/searchList.ts.62845w1yo0.vm", "AssetFile": "Template/vue3/searchList.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Mslc5TLUlhuI8W9/zLhiwTsBp13pCaklAUajSnpiByk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "62845w1yo0"}, {"Name": "integrity", "Value": "sha256-Mslc5TLUlhuI8W9/zLhiwTsBp13pCaklAUajSnpiByk="}, {"Name": "label", "Value": "Template/vue3/searchList.ts.vm"}]}, {"Route": "Template/vue3/searchList.ts.vm", "AssetFile": "Template/vue3/searchList.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Mslc5TLUlhuI8W9/zLhiwTsBp13pCaklAUajSnpiByk=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mslc5TLUlhuI8W9/zLhiwTsBp13pCaklAUajSnpiByk="}]}, {"Route": "Template/vue3/superQueryJson.ts.vm", "AssetFile": "Template/vue3/superQueryJson.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "75"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tcA1RlHaAUGfoF58mf8gnsSeb/RGcbxNLvDYj/0rGgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tcA1RlHaAUGfoF58mf8gnsSeb/RGcbxNLvDYj/0rGgU="}]}, {"Route": "Template/vue3/superQueryJson.ts.vvx7ge9gml.vm", "AssetFile": "Template/vue3/superQueryJson.ts.vm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "75"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"tcA1RlHaAUGfoF58mf8gnsSeb/RGcbxNLvDYj/0rGgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 03:58:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vvx7ge9gml"}, {"Name": "integrity", "Value": "sha256-tcA1RlHaAUGfoF58mf8gnsSeb/RGcbxNLvDYj/0rGgU="}, {"Name": "label", "Value": "Template/vue3/superQueryJson.ts.vm"}]}]}