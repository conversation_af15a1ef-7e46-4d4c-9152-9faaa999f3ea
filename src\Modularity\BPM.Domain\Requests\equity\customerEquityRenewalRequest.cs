﻿using BPM.DependencyInjection;

namespace BPM.Domain.Requests.equity;

/// <summary>
/// 权益卡续期
/// </summary>
[SuppressSniffer]
public class customerEquityRenewalRequest
{
    /// <summary>
    /// 操作人记录（非校验必须是店铺下管理员身份员工 ）
    /// </summary>
    public string operator_yz_open_id { get; set; }

    /// <summary>
    /// 有赞用户id，用户在有赞的唯一id。推荐使用
    /// </summary>
    public string yz_open_id { get; set; }

    /// <summary>
    /// 权益卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 结束时间延期天数 单位：天
    /// </summary>
    public int? extension_end_time { get; set; }

    /// <summary>
    /// 开始时间延期天数 单位：天 （该字段需要加白使用，并且卡要未生效状态。使用前请联系平台）
    /// </summary>
    public int? extension_begin_time { get; set; }

    /// <summary>
    /// 延期天数
    /// </summary>
    public int? extension_date_num { get; set; }

    /// <summary>
    /// 是否需要校验客户存在,不传默认为false,会根据客户是否存在进行补偿创建客户
    /// </summary>
    public bool check_customer { get; set; } = false;
}
