﻿using SqlSugar;

namespace BPM.Domain.Entity.refund;

/// <summary>
/// 订单支付记录
/// </summary>
[SugarTable("REFUND_PAYMENT")]
[Tenant("IPOS-ORDER")]
public class refundPaymentEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public string id { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public string order_no { get; set; }

    /// <summary>
    /// 交易金额
    /// </summary>
    public decimal refund_fee { get; set; }

    /// <summary>
    /// 交易明细id
    /// </summary>
    public string refund_no { get; set; }

    /// <summary>
    /// 交易渠道
    /// </summary>
    public int pay_way { get; set; }
}
