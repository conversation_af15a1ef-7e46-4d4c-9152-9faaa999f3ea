{
  "ProductSync": {
    // 分页相关配置
    "MaxPageSize": 200, // 单页最大数据量，int 类型，默认200，建议不超过500以避免API超时
    "MaxPageNo": 100, // 最大页码限制，int 类型，默认100，防止无限循环查询
    "MaxDataThreshold": 4000, // 数据量阈值，int 类型，默认4000，超过此值将自动分割时间段处理

    // 批处理相关配置
    "BatchSize": 50, // 批处理大小，int 类型，默认50，控制每批处理的商品数量
    "MaxParallelism": 5, // 最大并行度，int 类型，默认5，控制同时处理的线程数量
    "DelayBetweenBatches": 100, // 批次间延迟，int 类型，单位毫秒，默认100，避免API频率限制

    // 重试相关配置
    "MaxRetryAttempts": 3, // 最大重试次数，int 类型，默认3，API调用失败时的重试次数
    "RetryDelaySeconds": 5, // 重试延迟，int 类型，单位秒，默认5，重试间隔时间

    // 缓存相关配置
    "CacheExpirationMinutes": 30, // 缓存过期时间，int 类型，单位分钟，默认30，商品信息缓存时长

    // 时间相关配置
    "DefaultTimeRangeDays": 5, // 默认时间范围，int 类型，单位天，默认5，未指定时间时的查询范围

    // 功能开关配置
    "EnableStoreProductResync": true, // 启用门店商品重新同步，bool 类型，默认true，是否启用门店商品同步功能
    "EnableProductCompletion": true, // 启用商品补全，bool 类型，默认true，是否启用缺失商品资料补全
    "EnableBatchProcessing": true, // 启用批处理，bool 类型，默认true，是否启用批量处理模式
    "EnableParallelProcessing": true, // 启用并行处理，bool 类型，默认true，是否启用多线程并行处理
    "EnableDetailedLogging": false, // 启用详细日志，bool 类型，默认false，是否输出详细的调试日志

    // 冲突检测配置
    "EnableConflictDetection": true, // 启用冲突检测，bool 类型，默认true，是否启用条码冲突检测机制
    "ConflictDetectionWindowMinutes": 5 // 冲突检测时间窗口，int 类型，单位分钟，默认5，在此时间窗口内的条码被认为可能存在冲突
  }
}
