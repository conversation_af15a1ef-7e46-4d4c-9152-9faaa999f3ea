﻿using BPM.DependencyInjection;

namespace BPM.Domain.Dto.customerCard;

/// <summary>
/// 客户会员卡有效期信息.
/// </summary>
[SuppressSniffer]
public class customerCardItemDto
{
    /// <summary>
    /// 当前页码
    /// </summary>
    public int page { get; set; }

    /// <summary>
    /// 每页的最大记录条数
    /// </summary>
    public int page_size { get; set; }

    /// <summary>
    /// 权益卡集合
    /// </summary>
    public List<CardItem> items { get; set; }

    /// <summary>
    /// 会员拥有的权益卡总数
    /// </summary>
    public int total { get; set; }
}

public class CardItem
{
    /// <summary>
    /// 有效期开始时间
    /// </summary>
    public DateTime card_start_time { get; set; }

    /// <summary>
    /// 权益卡号
    /// </summary>
    public string card_no { get; set; }

    /// <summary>
    /// 权益卡别名
    /// </summary>
    public string card_alias { get; set; }

    /// <summary>
    /// 权益卡状态(0-未激活;1-使用中;2-已退款;3-已过期;4-用户已删除;5-商家已禁用;6-管理员删除;7-系统删除;8-未生效)
    /// </summary>
    public int card_state { get; set; }

    /// <summary>
    /// 有效期结束时间
    /// </summary>
    public DateTime card_end_time { get; set; }
}