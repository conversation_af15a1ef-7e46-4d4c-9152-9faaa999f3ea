﻿using BPM.Common.Contracts;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BPM.SubDev.Entitys.Entity;

/// <summary>
/// 测试用例
/// 版 本：V3.2
/// 版 权：BPM信息技术有限公司
/// 作 者：BPM开发平台组
/// 日 期：2021-06-01.
/// </summary>
[SugarTable("SUB_TEST")]
public class TestEntity : CLDEntityBase
{
    /// <summary>
    /// 编码.
    /// </summary>
    [SugarColumn(ColumnName = "F_EN_CODE")]
    public string? EnCode { get; set; }

    /// <summary>
    /// 名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string? FullName { get; set; }
}
