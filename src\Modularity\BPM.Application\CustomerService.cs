﻿using Aop.Api.Domain;
using BPM.Domain.Entity.customer;
using BPM.Domain.Entitys.Dto;
using BPM.Domain.Queries;
using BPM.Domain.Requests.customer;
using BPM.Domain.Responses.customer;
using BPM.EventBus;
using Microsoft.ClearScript.JavaScript;
using System.Text.RegularExpressions;
using BPM.Common.Manager;
using System.Text.Json;
using BPM.Domain.Dto.customer;
using BPM.Domain.Requests.subscriber;
using BPM.Application.Services;
using BPM.Domain.Entity.shop;

namespace BPM.Application;

/// <summary>
/// 客户管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11
/// </summary>
[ApiDescriptionSettings(Tag = "客户管理", Name = "Crm", Order = 600)]
[Route("api/[controller]")]
public class CustomerService : IDynamicApiController, ITransient
{

    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly SqlSugarProvider _repository;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 事件总线.
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    /// 缓存管理器
    /// </summary>
    private readonly ICacheManager _cache;

    /// <summary>
    /// 店铺缓存服务
    /// </summary>
    private readonly ShopCacheService _shopCacheService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context"></param>
    public CustomerService(ISqlSugarClient context, IYouzanService youzanService, IEventPublisher eventPublisher, ICacheManager cache, ShopCacheService shopCacheService)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<customerEntity>();
        _youzanService = youzanService;
        _eventPublisher = eventPublisher;
        _cache = cache;
        _shopCacheService = shopCacheService;
    }

    /// <summary>
    /// 创建客户
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("create")]
    public async Task<dynamic> createCustomer()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();
        var successData = new List<string>();
        while (true)
        {
            var query = new customerQuery();
            query.currentPage = page_index;
            query.pageSize = 50;
            query.tag_status = "add";
            var pageData = await getPageCrateCustomerList(query);
            //共计多少页
            var Total = pageData.pagination.Total;
            if (Total == 0)
                break;
            foreach (var item in pageData.list)
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.scrm.customer.create/3.0.0";
                    param.method = "POST";
                    param.body = item.ToJsonString();
                    var res = await _youzanService.GetData(param);
                    if (res.success)
                    {
                        succeed++;
                        var dit = res.data.ToObject<Dictionary<string, object>>();
                        var accout_id = dit["account_id"].ToString();
                        var yz_open_id = dit["yz_open_id"].ToString();
                        await addSyncTagByPhone(item.mobile, accout_id, yz_open_id);
                        // 记录成功明细
                        successData.Add($"手机:{item.mobile}, account_id:{accout_id}, message:{res.message}");
                        // 添加1秒延迟
                        //await Task.Delay(1000);
                    }
                    else
                    {
                        errorData.Add($"{item.mobile} {res.message}");
                        await updateErrorTagByPhone(item.mobile, res.message);
                        fail++;
                    }
                }
                catch (Exception ex)
                {
                    throw Oops.Oh(ex.Message);
                }
            }
        }
        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.succeed_data = successData;
        resultData.fail_data = errorData;
        return resultData.ToJsonString();
    }

    /// <summary>
    /// 更新客户
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("update")]
    public async Task<dynamic> updateCustomer()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();
        while (true)
        {
            var query = new customerQuery();
            query.currentPage = page_index;
            query.pageSize = 20;
            query.tag_status = "update";
            var pageData = await getPageUpdateCustomerList(query);
            //共计多少页
            var Total = pageData.pagination.Total;
            if (Total == 0)
                break;
            foreach (var item in pageData.list)
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.scrm.customer.update/3.0.0";
                    param.method = "POST";
                    param.body = item.ToJsonString();
                    var res = await _youzanService.GetData(param);
                    if (res.success == true)
                    {
                        succeed++;
                        await modifySyncTagById(item.external_request_id);
                    }
                    else
                    {
                        var errorJson = res.message;
                        errorData.Add(errorJson);
                        // 记录API错误到日志
                        Log.Error("有赞客户更新API错误: {ErrorMessage}, 客户ID: {CustomerId}", errorJson, item.external_request_id);
                        await modifySyncTagById(item.external_request_id, "update-error", errorJson);
                        fail++;
                    }
                }
                catch (Exception ex)
                {
                    // 记录异常到日志
                    Log.Error("客户更新过程中发生异常: {ErrorMessage}, 详细信息: {Exception}, 客户ID: {CustomerId}", ex.Message, ex, item?.external_request_id);
                    errorData.Add(ex.Message);
                    fail++;

                    // 如果是死锁异常，尝试重新执行
                    if (ex.Message.Contains("死锁") || ex.Message.Contains("deadlock"))
                    {
                        try
                        {
                            // 等待一小段时间后重试
                            await Task.Delay(500);
                            Log.Information("正在重试更新客户: {CustomerId}", item?.external_request_id);
                            await modifySyncTagById(item.external_request_id, "update-error", $"死锁后重试: {ex.Message}");
                        }
                        catch (Exception retryEx)
                        {
                            Log.Error("重试更新客户失败: {ErrorMessage}, 客户ID: {CustomerId}", retryEx.Message, item?.external_request_id);
                        }
                    }
                }
            }
        }
        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }

    /// <summary>
    ///  获取会员等级模板
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("createLevelTemplate")]
    public async Task<dynamic> createCustomerLevel()
    {
        var token = await _youzanService.GetTokenAsync();
        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.scrm.level.list/3.0.0";
            param.method = "POST";
            param.body = new createCustomerLevelRequest().ToJsonString();
            var res = await _youzanService.GetData(param);
            if (res.success == true)
            {
                var result = res.data.ToObject<customerLevelResponse>();
                var levelList = new List<memberGradeTemplateEntity>();
                foreach (var item in result.level_details)
                {
                    levelList.Add(new memberGradeTemplateEntity()
                    {
                        id = Guid.NewGuid().ToString(),
                        created_date = DateTime.Now,
                        source_grade_code = item.level_alias,
                        grade_name = item.name,
                        grade_code = item.level_value.ToString(),
                        type = item.level_type,
                        created_user_id = "admin"
                    });
                }
                // 插入会员等级
                await _repository.Insertable<memberGradeTemplateEntity>(levelList).ExecuteCommandAsync();
            }
            else
            {

            }
        }
        catch (Exception ex)
        {

            throw;
        }


        return true;
    }

    /// <summary>
    /// 批量设置会员等级
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("setMemberLevel")]
    public async Task<dynamic> setCustomerLevel()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();
        while (true)
        {
            var query = new customerQuery();
            query.currentPage = page_index;
            query.pageSize = 20;
            query.tag_status = "add";
            var pageData = await getPageMemberGradeList(query);
            //共计多少页
            var Total = pageData.pagination.Total;
            if (Total == 0)
                break;
            foreach (var item in pageData.list)
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.scrm.customer.level.set/3.0.0";
                    param.method = "POST";
                    param.body = item.ToJsonString();
                    var res = await _youzanService.GetData(param);
                    if (res.success)
                    {
                        succeed++;
                        await updateSuccessMemberGradeTagByPhone(item.account_id);
                    }
                    else
                    {
                        var errorJson = res.message;
                        errorData.Add(errorJson);
                        await updateErrorMemberGradeTagByPhone(item.account_id, errorJson);
                        fail++;
                    }
                }
                catch (Exception ex)
                {
                    errorData.Add(ex.Message);
                }
            }
        }
        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }


    /// <summary>
    /// 更新客户OpenId
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("updateOpenId")]
    public async Task<dynamic> updateCustomerOpenId()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        var errorData = new List<string>();
        List<CustomerInfoResponse> list = new List<CustomerInfoResponse>();
        while (true)
        {
            var pageData = await getPageCustomerNotOpenId();
            //共计多少页
            var Total = pageData.Count;
            if (Total == 0)
                break;
            try
            {
                var dict = new Dictionary<string, object>();
                dict.Add("phones", pageData);
                var param = new YouzanParameter();
                param.url = "youzan.scrm.customer.list.phone/1.0.0";
                param.method = "POST";
                param.body = dict.ToJsonString();
                var res = await _youzanService.GetData(param);
                if (res.success)
                {
                    succeed++;
                    var result = res.data.ToObject<List<CustomerInfoResponse>>();

                    // 检查 result 是否为 null 或空列表
                    if (result == null || result.Count == 0)
                    {
                        // 如果API返回成功但数据为空，将所有手机号对应的客户标记为状态2（未找到对应的有赞客户）
                        Log.Warning($"API返回成功但无匹配数据，将处理 {pageData.Count} 个手机号为未找到状态");

                        // 创建一个空列表，每个手机号都会被标记为"2"（未找到）
                        var emptyResultList = new List<CustomerInfoResponse>();
                        await updateCustomerByPhone(emptyResultList, pageData);
                    }
                    else
                    {
                        // 正常处理有返回数据的情况
                        list.AddRange(result);
                        await updateCustomerByPhone(result, pageData);
                    }
                }
                else
                {
                    fail++;
                    errorData.Add($"API调用失败: {res.message}");
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("非法调用"))
                    throw Oops.Oh(ex.Message);

                // 记录其他异常
                Log.Error($"更新客户OpenId异常: {ex.Message}");
                errorData.Add($"异常: {ex.Message}");
                fail++;
            }

            // 添加延迟，避免频繁调用API
            await Task.Delay(200);
        }

        // 调用updateCustomerOpenId2处理未授权的手机号
        var openId2Result = await updateCustomerOpenId2() as resultLogDto;

        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }


    /// <summary>
    /// 更新客户OpenId,处理未授权手机
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous, HttpPost("updateOpenId2")]
    public async Task<dynamic> updateCustomerOpenId2()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        var errorData = new List<string>();
        int processedCount = 0; // 记录处理的手机号数量
        int maxPerBatch = 10; // 每批处理的最大数量，避免单次处理过多

        while (true)
        {
            var pageData = await getPageCustomerNotOpenId2();
            //共计多少页
            var Total = pageData.Count;
            if (Total == 0)
                break;

            // 防止处理过多记录导致长时间运行
            processedCount += Total;
            if (processedCount > 100)
            {
                Log.Warning("本次处理记录超过100条，将在下次任务中继续处理");
                break;
            }

            Log.Information("处理 tag_mall_status='2' 的手机号，当前获取到 {Total} 条记录", pageData.Count);

            try
            {
                var dict = new Dictionary<string, object>();
                dict.Add("phones", pageData);
                var param = new YouzanParameter();
                param.url = "youzan.scrm.customer.list.phone/1.0.0";
                param.method = "POST";
                param.body = dict.ToJsonString();
                var res = await _youzanService.GetData(param);
                if (res.success)
                {
                    succeed++;
                    var result = res.data.ToObject<List<CustomerInfoResponse>>();
                    Log.Information($"API返回成功，找到匹配的有赞客户数量: {result?.Count ?? 0}");

                    // 统一处理逻辑：无论API返回什么结果，确保所有手机号都被处理
                    if (result == null || result.Count == 0)
                    {
                        // 如果API返回空数据，使用批量更新将所有手机号状态更新为3
                        Log.Information($"API返回空数据，将所有手机号状态更新为3");

                        // 对每个手机号单独处理，避免批量更新可能导致的问题
                        foreach (var phone in pageData)
                        {
                            try
                            {
                                await updateCustomerByPhone(phone, null); // 使用null将状态设为3
                                Log.Information($"已将手机号 {phone} 更新为状态3");
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"更新手机号 {phone} 失败: {ex.Message}");
                                errorData.Add($"更新手机号 {phone} 失败: {ex.Message}");
                                // 即使出错，继续处理其他手机号
                            }

                            // 添加小延迟，减轻数据库压力
                            await Task.Delay(50);
                        }
                    }
                    else
                    {
                        // 创建一个字典，方便查找匹配的手机号信息
                        var phoneToInfoMap = new Dictionary<string, CustomerInfoResponse>();
                        foreach (var info in result.Where(r => r.social_info != null))
                        {
                            if (!string.IsNullOrEmpty(info.social_info?.mobile))
                            {
                                phoneToInfoMap[info.social_info.mobile] = info;
                            }
                        }

                        // 处理每个手机号
                        foreach (var phone in pageData)
                        {
                            try
                            {
                                // 检查是否有匹配的客户信息
                                if (phoneToInfoMap.TryGetValue(phone, out var customerInfo))
                                {
                                    // 有匹配的信息，更新为对应的openId
                                    await updateCustomerByPhone(phone, customerInfo.yz_open_id);
                                    Log.Information($"手机号 {phone} 匹配到有赞客户，OpenId: {customerInfo.yz_open_id}");
                                }
                                else
                                {
                                    // 没有匹配的信息，更新为状态3
                                    await updateCustomerByPhone(phone, null);
                                    Log.Information($"手机号 {phone} 在返回结果中未找到匹配，更新为状态3");
                                }
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"更新手机号 {phone} 失败: {ex.Message}");
                                errorData.Add($"更新手机号 {phone} 失败: {ex.Message}");
                                // 即使出错，继续处理其他手机号
                            }

                            // 添加小延迟，减轻数据库压力
                            await Task.Delay(50);
                        }
                    }
                }
                else
                {
                    fail++;
                    var errorMsg = $"API调用失败: {res.message}";
                    Log.Error($"API调用失败: {errorMsg}");
                    errorData.Add(errorMsg);

                    // API调用失败时，仍然更新所有手机号为状态3，避免死循环
                    foreach (var phone in pageData)
                    {
                        try
                        {
                            await updateCustomerByPhone(phone, null);
                            Log.Information($"API失败后将手机号 {phone} 更新为状态3");
                        }
                        catch (Exception ex)
                        {
                            Log.Error($"更新手机号 {phone} 失败: {ex.Message}");
                        }
                        await Task.Delay(50);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"处理异常: {ex.Message}";
                Log.Error($"处理异常: {errorMsg}");
                errorData.Add(errorMsg);
                fail++;

                if (ex.Message.Contains("非法调用"))
                {
                    // 严重错误，需要终止处理
                    Log.Error("检测到API非法调用错误，终止处理");
                    throw Oops.Oh(ex.Message);
                }

                // 发生异常时，尝试更新所有手机号状态，确保不会卡在死循环
                foreach (var phone in pageData)
                {
                    try
                    {
                        await updateCustomerByPhone(phone, null);
                        Log.Information($"异常后将手机号 {phone} 更新为状态3");
                    }
                    catch (Exception updateEx)
                    {
                        Log.Error($"更新手机号 {phone} 状态失败: {updateEx.Message}");
                    }
                    await Task.Delay(50);
                }
            }

            // 批次之间添加更长的延迟
            await Task.Delay(500);
        }

        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }

    /// <summary>
    /// 重新同步会员资料
    /// </summary>
    /// <returns>返回同步结果，包含成功数量、失败数量和错误信息</returns>
    [AllowAnonymous]
    [HttpPost("resync")]
    public async Task<dynamic> resyncCustomerInfo()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int pageIndex = 1;
        var errorData = new List<string>();

        // 首先检查是否有需要同步的数据
        var totalCount = await _repository.Queryable<customerEntity>()
            .Where(x => x.tag_status == "sync")
            .CountAsync();

        if (totalCount == 0)
        {
            Log.Information($"没有需要同步的客户数据");
            return new resultLogDto
            {
                succeed = 0,
                fail = 0,
                fail_data = new List<string>()
            };
        }

        var totalPages = (int)Math.Ceiling(totalCount / 20.0);
        Log.Information($"开始同步会员资料，共 {totalCount} 条数据，共 {totalPages} 页");

        string lastProcessedId = "";  // 用于记录最后处理的ID
        const int batchSize = 20;     // 每批处理的记录数

        while (pageIndex <= totalPages)
        {
            try
            {
                // 使用游标方式获取数据
                var customers = await _repository.Queryable<customerEntity>()
                    .Where(x => x.tag_status == "sync" && x.id.CompareTo(lastProcessedId) > 0)
                    .OrderBy(x => x.id)
                    .Take(batchSize)
                    .ToListAsync();

                if (customers == null || !customers.Any())
                {
                    Log.Warning($"没有更多需要同步的数据，结束同步");
                    break;
                }

                var currentPageCount = customers.Count;
                Log.Information($"正在处理 {currentPageCount} 条记录");

                var batchCustomers = new List<customerEntity>();
                var maxBatchSize = 50; // 每批处理的最大记录数

                foreach (var customer in customers)
                {
                    try
                    {
                        // 记录当前处理的ID
                        lastProcessedId = customer.id;

                        // 准备请求参数
                        var param = new YouzanParameter();
                        param.url = "youzan.scrm.customer.detail.get/1.0.1";
                        param.method = "POST";
                        var request = new Dictionary<string, object>();
                        request.Add("fields", "user_base");

                        var accountInfo = new Dictionary<string, object>();
                        if (string.IsNullOrEmpty(customer.open_id))
                        {
                            accountInfo.Add("account_id", customer.phone);
                            accountInfo.Add("account_type", 2);
                        }
                        else
                        {
                            request.Add("yz_open_id", customer.open_id);
                            accountInfo.Add("account_id", "");
                            accountInfo.Add("account_type", 5);
                        }

                        request.Add("account_info", accountInfo);
                        param.body = request.ToJsonString();

                        // 调用有赞接口获取会员信息
                        var res = await _youzanService.GetData(param);

                        if (res.success && res.data != null)
                        {
                            var customerData = res.data.ToObject<Dictionary<string, object>>();

                            // 更新客户信息
                            customer.modify_date = DateTime.Now;
                            customer.tag_status = "synced";

                            // 基本信息
                            customer.real_name = Regex.Replace(customerData.GetValueOrDefault("name", "")?.ToString() ?? "", @"[^\u4e00-\u9fa5a-zA-Z0-9]", "");
                            customer.nick_name = Regex.Replace(customerData.GetValueOrDefault("latest_nickname", "")?.ToString() ??
                                                            customerData.GetValueOrDefault("wx_nickname", "")?.ToString() ?? "",
                                                            @"[^\u4e00-\u9fa5a-zA-Z0-9]", "");

                            customer.avatar = customerData.GetValueOrDefault("latest_avatar", "")?.ToString() ??
                                            customerData.GetValueOrDefault("wx_avatar", "")?.ToString();
                            customer.phone = customerData.GetValueOrDefault("mobile", "")?.ToString();

                            // 店铺信息
                            var ascriptionKdtId = customerData.GetValueOrDefault("ascription_kdt_id", "")?.ToString();
                            if (!string.IsNullOrEmpty(ascriptionKdtId) && long.TryParse(ascriptionKdtId, out long kdtId))
                            {
                                customer.store_id = await GetStoreIdAsync(kdtId);
                            }

                            // 创建时间
                            var createdAtStr = customerData.GetValueOrDefault("created_at", "")?.ToString();
                            if (!string.IsNullOrEmpty(createdAtStr) && long.TryParse(createdAtStr, out long timestamp) && timestamp > 0)
                            {
                                customer.created_date = DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime;
                            }

                            // 性别
                            var genderStr = customerData.GetValueOrDefault("gender", "")?.ToString();
                            customer.gender = string.IsNullOrEmpty(genderStr) ? null :
                                            int.TryParse(genderStr, out int genderValue) ? genderValue : null;

                            // 生日
                            var birthday = customerData.GetValueOrDefault("birthday", "")?.ToString();
                            if (!string.IsNullOrEmpty(birthday) && birthday != "0" && birthday != "1970-01-01 00:00:00")
                            {
                                if (DateTime.TryParse(birthday, out DateTime birthDate))
                                {
                                    customer.birth_day = birthDate;
                                }

                            }

                            customer.remark = customerData.GetValueOrDefault("remark", "")?.ToString();

                            // 如果有新的openId，也更新
                            var newOpenId = customerData.GetValueOrDefault("yz_open_id", "")?.ToString();
                            if (!string.IsNullOrEmpty(newOpenId))
                            {
                                customer.open_id = newOpenId;
                            }

                            batchCustomers.Add(customer);

                            // 当累积的记录达到批处理大小时执行批量更新
                            if (batchCustomers.Count >= maxBatchSize)
                            {
                                await _repository.Updateable(batchCustomers)
                                    .UpdateColumns(it => new {
                                        it.modify_date,
                                        it.tag_status,
                                        it.real_name,
                                        it.nick_name,
                                        it.avatar,
                                        it.store_id,
                                        it.created_date,
                                        it.gender,
                                        it.birth_day,
                                        it.remark,
                                        it.open_id,
                                        it.phone
                                    })
                                    .ExecuteCommandAsync();

                                batchCustomers.Clear();
                                Log.Information($"已更新 {maxBatchSize} 条记录");
                            }

                            succeed++;
                        }
                        else
                        {
                            var errorMsg = res.success ? "未能获取到会员信息" : res.message;
                            errorData.Add($"{customer.phone} {errorMsg}");
                            await updateErrorTagByPhone(customer.phone, errorMsg);
                            Log.Warning($"客户 {customer.phone} 同步失败: {errorMsg}");
                            fail++;
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("非法调用"))
                            throw Oops.Oh(ex.Message);

                        var errorMsg = ex.Message;
                        errorData.Add($"{customer.phone} {errorMsg}");
                        await updateErrorTagByPhone(customer.phone, errorMsg);
                        fail++;

                        // 如果是API限制相关的错误，增加等待时间
                        if (ex.Message.Contains("频率限制") || ex.Message.Contains("rate limit"))
                        {
                            await Task.Delay(5000); // 等待5秒
                        }
                    }
                }

                // 处理剩余的记录
                if (batchCustomers.Any())
                {
                    await _repository.Updateable(batchCustomers)
                        .UpdateColumns(it => new {
                            it.modify_date,
                            it.tag_status,
                            it.real_name,
                            it.nick_name,
                            it.avatar,
                            it.store_id,
                            it.created_date,
                            it.gender,
                            it.birth_day,
                            it.remark,
                            it.open_id,
                            it.phone
                        })
                        .ExecuteCommandAsync();

                    Log.Information($"已更新剩余 {batchCustomers.Count} 条记录");
                    batchCustomers.Clear();
                }

                pageIndex++;

                // 每页处理完后添加短暂延迟，避免请求过于频繁
                await Task.Delay(2000);

                // 再次检查是否还有未同步的数据
                var remainingCount = await _repository.Queryable<customerEntity>()
                    .Where(x => x.tag_status == "sync")
                    .CountAsync();

                if (remainingCount == 0)
                {
                    Log.Information($"所有数据已同步完成");
                    break;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"同步会员资料时发生错误: {ex.Message}");
                throw Oops.Oh($"同步会员资料失败: {ex.Message}");
            }
        }

        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }

    #region Private Method

    /// <summary>
    /// 获取客户列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<SqlSugarPagedList<createCustomerRequest>> getPageCrateCustomerList(customerQuery query)
    {
        var data = await _repository.Queryable<customerEntity, shopEntity>((a, b) => new JoinQueryInfos(JoinType.Left, a.store_id == b.id))
           .Where((a, b) => a.tag_status == query.tag_status)
           .Select((a, b) => new createCustomerRequest
           {
               mobile = a.phone,
               create_date = a.created_date.ToString("yyyy-MM-dd HH:mm:ss"),
               customer_create = new customer_info()
               {
                   birthday = (a.birth_day == null ||
                             a.birth_day.Value.Date == new DateTime(1999, 1, 1) ||
                             a.birth_day.Value.Date == new DateTime(1900, 1, 1))
                             ? null
                             : a.birth_day.Value.ToString("yyyy-MM-dd"),
                   gender = a.gender,
                   ascription_kdt_id = b.source_no,
                   name = a.real_name,
                   remark = a.remark
               }
           }).ToPagedListAsync(query.currentPage, query.pageSize);
        return data;
    }

    /// <summary>
    /// 获取客户列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<SqlSugarPagedList<updateCustomerRequest>> getPageUpdateCustomerList(customerQuery query)
    {
        var data = await _repository.Queryable<customerEntity, shopEntity>((a, b) => new JoinQueryInfos(JoinType.Left, a.store_id == b.id))
            .Where((a, b) => a.tag_status == query.tag_status)
            .Select((a, b) => new updateCustomerRequest
            {
                create_date = a.created_date.ToString("yyyy-MM-dd HH:mm:ss"),
                external_request_id = a.id,
                // 更新信息
                customer_update = new customerUpdateInfo()
                {
                    birthday = (a.birth_day == null ||
                              a.birth_day.Value.Date == new DateTime(1999, 1, 1) ||
                              a.birth_day.Value.Date == new DateTime(1900, 1, 1))
                              ? null
                              : a.birth_day.Value.ToString("yyyy-MM-dd"),
                    gender = a.gender,
                    ascription_kdt_id = b.source_no,
                    name = a.real_name,
                    remark = a.remark
                },
                // 账户信息
                account = new accountInfo()
                {
                    account_id = string.IsNullOrEmpty(a.open_id) ? a.phone : a.open_id,
                    account_type = string.IsNullOrEmpty(a.open_id) ? "Mobile" : "YzOpenId"
                }
            }).ToPagedListAsync(query.currentPage, query.pageSize);
        return data;
    }

    /// <summary>
    /// 修改客户同步标记
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="tag_status">同步标记</param>
    /// <returns></returns>
    private async Task addSyncTagByPhone(string phone, string request_id, string yz_open_id, string tag_status = "select")
    {
        var customer = await _repository.Queryable<customerEntity>().With(SqlWith.NoLock).FirstAsync(x => x.phone.Equals(phone));
        customer.tag_status = tag_status;
        customer.modify_date = DateTime.Now;
        customer.request_id = request_id;
        customer.open_id = yz_open_id;

        //await _eventPublisher.PublishAsync("Customer:UpdateId", customer.ToJsonString());
        await _repository.Updateable<customerEntity>().SetColumns(it => new customerEntity()
        {
            tag_status = tag_status,
            modify_date = DateTime.Now,
            request_id = request_id,
            open_id = yz_open_id
        }).Where(x => x.phone == phone).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新客户openId
    /// </summary>
    /// <param name="list">有赞返回的客户信息列表</param>
    /// <param name="phones">需要更新的手机号列表</param>
    /// <returns></returns>
    private async Task updateCustomerByPhone(List<CustomerInfoResponse> list, List<string> phones)
    {
        var customer_list = new List<customerEntity>();
        var customer_equity_list = new List<customerEquityEntity>();

        // 生成当前时间戳作为版本号
        long currentVersion = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        // 记录每个手机号的处理状态
        Log.Information($"开始处理 {phones.Count} 个手机号，有 {list?.Count ?? 0} 个匹配的有赞客户信息");

        foreach (var phone in phones)
        {
            // 尝试在列表中找到匹配的客户信息
            var model = list?.FirstOrDefault(x => x.social_info != null && x.social_info.mobile == phone);

            // 如果找不到匹配的客户信息，tag_mall_status 设置为 "2"
            var tagStatus = model != null ? "1" : "2";

            customer_list.Add(new customerEntity
            {
                phone = phone,
                open_id = model?.yz_open_id,
                tag_mall_status = tagStatus,
                modify_date = DateTime.Now,
                yzVersion = currentVersion // 设置版本号
            });

            customer_equity_list.Add(new customerEquityEntity
            {
                phone = phone,
                yz_open_id = model?.yz_open_id,
                tag_mall_status = tagStatus,
                modify_date = DateTime.Now
            });

            Log.Information($"手机号 {phone} 处理结果: OpenId={model?.yz_open_id ?? "NULL"}, 状态={tagStatus}");
        }

        try
        {
            // 分批处理，每批50条，减少批量大小以降低死锁风险
            const int BATCH_SIZE = 50;

            for (int i = 0; i < customer_list.Count; i += BATCH_SIZE)
            {
                var customerBatch = customer_list.Skip(i).Take(BATCH_SIZE).ToList();
                var equityBatch = customer_equity_list.Skip(i).Take(BATCH_SIZE).ToList();

                // 为每个批次设置稍微不同的修改时间，避免时间戳冲突
                var batchTime = DateTime.Now;
                var batchVersion = currentVersion + i; // 为每个批次设置不同的版本号

                foreach (var customer in customerBatch)
                {
                    customer.modify_date = batchTime;
                    customer.yzVersion = batchVersion;
                }
                foreach (var equity in equityBatch)
                {
                    equity.modify_date = batchTime;
                }

                // 使用事务和重试机制
                bool success = false;
                int retryCount = 0;
                const int MAX_RETRIES = 3;

                while (!success && retryCount < MAX_RETRIES)
                {
                    try
                    {
                        var result = await _repository.Ado.UseTranAsync(async () =>
                        {
                            // 更新客户表 - 不再使用modify_date作为条件
                            foreach (var customer in customerBatch)
                            {
                                // 先查询当前记录
                                var currentRecord = await _repository.Queryable<customerEntity>()
                            .Where(it => it.phone == customer.phone)
                            .FirstAsync();

                                if (currentRecord != null)
                                {
                                    // 检查是否需要更新
                                    bool needUpdate = string.IsNullOrEmpty(currentRecord.open_id) ||
                                currentRecord.open_id != customer.open_id ||
                                             currentRecord.tag_mall_status != customer.tag_mall_status ||
                                             currentRecord.yzVersion == null; // 如果版本号为NULL，也需要更新

                                    if (needUpdate)
                                    {
                                        await _repository.Updateable<customerEntity>()
                                    .SetColumns(it => new customerEntity
                                    {
                                        open_id = customer.open_id,
                                        tag_mall_status = customer.tag_mall_status,
                                        modify_date = customer.modify_date,
                                        yzVersion = customer.yzVersion // 更新版本号
                                    })
                                    .Where(it => it.phone == customer.phone)
                    .ExecuteCommandAsync();

                                        Log.Information($"客户已更新: {customer.phone}, OpenId={customer.open_id}, 版本号={customer.yzVersion}");
                                    }
                                    else
                                    {
                                        Log.Information($"客户无需更新(数据相同): {customer.phone}");
                                    }
                                }
                                else
                                {
                                    Log.Warning($"客户不存在: {customer.phone}");
                                }
                            }

                            // 更新权益表 - 不再使用modify_date作为条件
                            foreach (var equity in equityBatch)
                            {
                                // 先查询当前记录
                                var currentRecord = await _repository.Queryable<customerEquityEntity>()
                            .Where(it => it.phone == equity.phone)
                            .FirstAsync();

                                if (currentRecord != null)
                                {
                                    // 只有当yz_open_id为空或与新值不同时才更新
                                    if (string.IsNullOrEmpty(currentRecord.yz_open_id) ||
                                currentRecord.yz_open_id != equity.yz_open_id ||
                                currentRecord.tag_mall_status != equity.tag_mall_status)
                                    {
                                        await _repository.Updateable<customerEquityEntity>()
                                    .SetColumns(it => new customerEquityEntity
                                    {
                                        yz_open_id = equity.yz_open_id,
                                        tag_mall_status = equity.tag_mall_status,
                                        modify_date = equity.modify_date
                                    })
                                    .Where(it => it.phone == equity.phone)
                    .ExecuteCommandAsync();

                                        Log.Information($"权益已更新: {equity.phone}, OpenId={equity.yz_open_id}");
                                    }
                                    else
                                    {
                                        Log.Information($"权益无需更新(数据相同): {equity.phone}");
                                    }
                                }
                                else
                                {
                                    Log.Warning($"权益不存在: {equity.phone}");
                                }
                            }

                            return true;
                        });

                        if (result.IsSuccess)
                        {
                            success = true;
                            Log.Information($"批次 {i / BATCH_SIZE + 1} 更新成功，处理了 {customerBatch.Count} 条记录");
                        }
                        else
                        {
                            Log.Error($"批次 {i / BATCH_SIZE + 1} 更新失败: {result.ErrorMessage}, 重试次数: {retryCount + 1}/{MAX_RETRIES}");
                            retryCount++;

                            // 如果是死锁错误，等待更长时间
                            if (result.ErrorMessage.Contains("死锁") || result.ErrorMessage.Contains("deadlock"))
                            {
                                await Task.Delay(1000 * retryCount);
                            }
                            else
                            {
                                await Task.Delay(500 * retryCount);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"批次 {i / BATCH_SIZE + 1} 处理异常: {ex.Message}, 重试次数: {retryCount + 1}/{MAX_RETRIES}");
                        retryCount++;
                        await Task.Delay(500 * retryCount);
                    }
                }

                if (!success)
                {
                    Log.Error($"批次 {i / BATCH_SIZE + 1} 处理失败，已达到最大重试次数");
                }

                // 批次之间增加间隔，减少数据库压力
                await Task.Delay(200);
            }
        }
        catch (Exception ex)
        {
            // 记录错误日志
            Log.Error($"更新客户信息时发生错误: {ex.Message}, 堆栈: {ex.StackTrace}");
            throw new Exception($"更新客户信息时发生错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 更新客户openId
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="yz_open_id">有赞openId</param>
    /// <returns></returns>
    private async Task updateCustomerByPhone(string phone, string yz_open_id)
    {
        try
        {
            // 生成当前时间戳作为版本号
            long currentVersion = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            // 确定状态值
            string tagMallStatus = string.IsNullOrEmpty(yz_open_id) ? "3" : "2";

            Log.Information($"开始更新客户信息: 手机号={phone}, OpenId={yz_open_id ?? "NULL"}, 状态={tagMallStatus}, 版本号={currentVersion}");

            // 开启事务
            var result = await _repository.Ado.UseTranAsync(async () =>
            {
                // 更新客户表
                await _repository.Updateable<customerEntity>()
                    .SetColumns(it => new customerEntity
                    {
                        open_id = yz_open_id,
                        tag_mall_status = tagMallStatus,
                        modify_date = DateTime.Now,
                        yzVersion = currentVersion // 设置版本号
                    })
                    .Where(x => x.phone == phone)
                    .ExecuteCommandAsync();

                // 更新权益表
                await _repository.Updateable<customerEquityEntity>()
                    .SetColumns(it => new customerEquityEntity
                    {
                        yz_open_id = yz_open_id,
                        tag_mall_status = tagMallStatus,
                        modify_date = DateTime.Now
                    })
                    .Where(x => x.phone == phone)
                    .ExecuteCommandAsync();

                return true;
            });

            if (!result.IsSuccess)
            {
                Log.Error($"更新客户事务失败: {result.ErrorMessage}, 手机号: {phone}");
                throw new Exception($"更新失败: {result.ErrorMessage}");
            }

            Log.Information($"单个客户更新成功: {phone}, OpenId={yz_open_id ?? "NULL"}, 状态={tagMallStatus}, 版本号={currentVersion}");
        }
        catch (Exception ex)
        {
            Log.Error($"更新客户信息时发生错误: {ex.Message}, 手机号: {phone}");
            throw new Exception($"更新客户信息时发生错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 修改客户同步标记
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="tag_body">同步标记</param>
    /// <returns></returns>
    private async Task updateErrorTagByPhone(string phone, string tag_body)
    {
        await _repository.Updateable<customerEntity>().SetColumns(it => new customerEntity()
        {
            tag_status = "add-error",
            modify_date = DateTime.Now,
            tag_body = tag_body,
        }).Where(x => x.phone == phone).ExecuteCommandAsync();
    }


    /// <summary>
    /// 修改客户同步标记
    /// </summary>
    /// <param name="customer_id">客户ID</param>
    /// <param name="tag_status">同步标记</param>
    /// <param name="tag_body">标记内容</param>
    /// <returns></returns>
    private async Task modifySyncTagById(string customer_id, string tag_status = "select", string tag_body = "")
    {
        // 添加重试机制
        int maxRetries = 3;
        int retryCount = 0;
        bool success = false;

        while (!success && retryCount < maxRetries)
        {
            try
            {
                // 使用事务保护
                var result = await _repository.Ado.UseTranAsync(async () =>
                {
                    await _repository.Updateable<customerEntity>().SetColumns(it => new customerEntity()
                    {
                        tag_status = tag_status,
                        modify_date = DateTime.Now,
                        tag_body = tag_body,
                    }).Where(x => x.id == customer_id).ExecuteCommandAsync();

                    return true;
                });

                if (result.IsSuccess)
                {
                    success = true;
                }
                else
                {
                    // 记录事务失败
                    Log.Error($"更新客户同步标记事务失败: 客户ID={customer_id}, 错误={result.ErrorMessage}, 重试次数={retryCount + 1}/{maxRetries}");
                    retryCount++;
                    await Task.Delay(500 * retryCount); // 指数退避
                }
            }
            catch (Exception ex)
            {
                // 记录异常
                Log.Error($"更新客户同步标记异常: 客户ID={customer_id}, 错误={ex.Message}, 重试次数={retryCount + 1}/{maxRetries}");
                retryCount++;

                // 如果是死锁异常，等待更长时间再重试
                if (ex.Message.Contains("死锁") || ex.Message.Contains("deadlock"))
                {
                    await Task.Delay(1000 * retryCount); // 死锁情况下等待更长时间
                }
                else
                {
                    await Task.Delay(500 * retryCount);
                }
            }
        }

        if (!success)
        {
            Log.Error($"更新客户同步标记失败，已达到最大重试次数: {maxRetries}, 客户ID={customer_id}");
        }
    }


    /// <summary>
    /// 获取客户列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<List<string>> getPageCustomerNotOpenId()
    {
        return await _repository.Queryable<customerEntity>().Where(x => x.open_id == null && x.phone != null && x.tag_mall_status == "1")
              .Select(p => p.phone).Take(50).ToListAsync();

    }

    /// <summary>
    /// 获取客户列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<List<string>> getPageCustomerNotOpenId2()
    {
        // 修改为获取多条记录，并按照修改时间排序，避免总是获取相同的记录
        return await _repository.Queryable<customerEntity>()
              .Where(x => x.open_id == null && x.phone != null && x.tag_mall_status == "2")
              .OrderBy(x => x.modify_date) // 按修改时间排序，先处理旧数据
              .Select(p => p.phone)
              .Take(10) // 增加每次处理的数量
              .ToListAsync();
    }


    #region 会员等级
    /// <summary>
    /// 获取会员等级同步列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns></returns>
    private async Task<SqlSugarPagedList<setCustomerLevelRequest_v3>> getPageMemberGradeList(customerQuery query)
    {
        var data = await _repository.Queryable<memberGradeEntity>()
            .Where(a => a.tag_status == query.tag_status)
            .Select(a => new setCustomerLevelRequest_v3
            {
                account_id = a.phone,
                level_alias = a.yz_grade_id

            }).ToPagedListAsync(query.currentPage, query.pageSize);
        return data;
    }

    /// <summary>
    /// 修改客户同步标记
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="tag_status">同步标记</param>
    /// <returns></returns>
    private async Task updateSuccessMemberGradeTagByPhone(string phone, string tag_status = "select")
    {
        await _repository.Updateable<memberGradeEntity>().SetColumns(it => new memberGradeEntity()
        {
            tag_status = tag_status,
            modify_date = DateTime.Now,
        }).Where(x => x.phone == phone).ExecuteCommandAsync();
    }
    /// <summary>
    /// 修改会员等级同步标记
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="tag_body">同步标记</param>
    /// <returns></returns>
    private async Task updateErrorMemberGradeTagByPhone(string phone, string tag_body)
    {
        await _repository.Updateable<memberGradeEntity>().SetColumns(it => new memberGradeEntity()
        {
            tag_status = "error",
            modify_date = DateTime.Now,
            tag_body = tag_body
        }).Where(x => x.phone == phone).ExecuteCommandAsync();
    }
    #endregion

    /// <summary>
    /// 获取店铺ID
    /// </summary>
    private async Task<string> GetStoreIdAsync(long kdtId)
    {
        try
        {
            var shop = await _shopCacheService.GetStoreIdFromCache(kdtId);
            if (shop != null)
            {
                Log.Information($"从缓存获取店铺ID成功: kdtId={kdtId}, storeId={shop}");
                return shop;
            }

            Log.Warning($"店铺不存在: kdtId={kdtId}, 将使用kdtId作为storeId");
            return kdtId.ToString();
        }
        catch (Exception ex)
        {
            Log.Error($"获取店铺ID失败: kdtId={kdtId}, 错误={ex.Message}");
            return kdtId.ToString();
        }
    }

    #endregion
}
