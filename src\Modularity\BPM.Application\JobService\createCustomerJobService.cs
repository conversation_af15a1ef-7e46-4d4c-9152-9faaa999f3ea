﻿using BPM.Domain.Entitys.Dto;
using BPM.Schedule;
using Microsoft.Extensions.DependencyInjection;

namespace BPM.Application;

/// <summary>
/// 本地任务-创建客户.
/// </summary>
[JobDetail("job_create_customer", Description = "创建客户", GroupName = "BuiltIn", Concurrent = true)]
public class createCustomerJobService : <PERSON><PERSON><PERSON>, IDisposable
{
    /// <summary>
    /// 服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// crm服务.
    /// </summary>
    private readonly CustomerService _customerService;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public createCustomerJobService(IServiceScopeFactory serviceScopeFactory, CustomerService customerService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _customerService = customerService;
    }


    /// <summary>
    /// 执行任务.
    /// </summary>
    /// <param name="context">上下文.</param>
    /// <param name="stoppingToken">是否取消.</param>
    /// <returns></returns>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        resultLogDto result = await _customerService.createCustomer();
        context.Result = string.Format("执行成功,成功:" + result.succeed + "行");
    }

    /// <summary>
    /// 回收.
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
}
