﻿using BPM.Domain.Entity.customer;
using BPM.Domain.Entitys.Dto;
using BPM.Domain.Queries;
using BPM.Domain.Requests.equity;
using BPM.Domain.Responses.customer;
using BPM.EventBus;
using BPM.Domain.Dto.customerCard;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using BPM.Domain.Requests.voucher;
using System.Text.Json.Serialization;

namespace BPM.Application;

/// <summary>
/// 会员管理
/// 版 本：V3.6
/// 版 权：BPM信息技术有限公司
/// 作 者：Aarons
/// 日 期：2024-09-11
/// </summary>
[ApiDescriptionSettings(Tag = "客户管理", Name = "Crm", Order = 600)]
[Route("api/[controller]")]
public class CustomerEquityService : IDynamicApiController, ITransient
{
    /// <summary>
    ///  服务提供
    /// </summary>
    private readonly SqlSugarProvider _repository;
    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;
    /// <summary>
    /// 事件总线.
    /// </summary>
    private readonly IEventPublisher _eventPublisher;
    private readonly ILogger<CustomerEquityService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="youzanService">有赞服务接口</param>
    /// <param name="eventPublisher">事件发布服务</param>
    /// <param name="logger">日志服务</param>
    public CustomerEquityService(ISqlSugarClient context, IYouzanService youzanService, IEventPublisher eventPublisher, ILogger<CustomerEquityService> logger)
    {
        _repository = context.AsTenant().GetConnectionWithAttr<customerEntity>();
        _youzanService = youzanService;
        _eventPublisher = eventPublisher;
        _logger = logger;
    }

    /// <summary>
    /// 获取权益卡列表模板
    /// </summary>
    /// <returns>操作结果，true表示成功，false表示失败</returns>
    [AllowAnonymous, HttpPost("createEquityTemplate")]
    public async Task<dynamic> getCustomerEquity()
    {
        var token = await _youzanService.GetTokenAsync();
        try
        {
            var param = new YouzanParameter();
            param.url = "youzan.scrm.card.list/3.0.0";
            param.method = "POST";
            param.body = new getCustomerEquityRequest().ToJsonString();
            var res = await _youzanService.GetData(param);
            if (res.success == true)
            {
                var result = res.data.ToObject<customerEquityResponse>();
                var list = result.items.ToObject<List<EquityEntity>>();
                await crateEquity(list);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权益卡列表模板时发生错误");
            throw;
        }

        return true;
    }

    /// <summary>
    /// 创建客户权益卡
    /// </summary>
    /// <returns>处理结果，包含成功和失败的统计信息</returns>
    [AllowAnonymous, HttpPost("createEquity")]
    public async Task<dynamic> createCustomerEquity()
    {
        try
        {
            _logger.LogInformation("开始执行客户权益卡批量创建");

            var token = await _youzanService.GetTokenAsync();
            var succeed = 0;
            var fail = 0;
            var errorData = new ConcurrentBag<string>();
            var successData = new ConcurrentBag<string>();

            // 减小批量处理的最大记录数，避免过大并发
            const int batchSize = 10;

            while (true)
            {
                try
                {
                    _logger.LogInformation($"正在获取{batchSize}条待处理的权益卡记录");

                    // 获取固定数量的记录，使用Take而不是分页
                    var recordsToProcess = await _repository.Queryable<customerEquityEntity>()
                        .Where(a => a.tag_status == "add")
                        .OrderBy(a => a.created_date)  // 添加排序以确保处理顺序稳定
                        .Take(batchSize)
                        .Select(a => new createCustomerEquityRequest
                        {
                            card_alias = a.equity_card_alias_id,
                            user = new userEquityInfo()
                            {
                                account_id = a.phone,
                                account_type = 2
                            }
                        })
                        .ToListAsync();

                    int currentBatchCount = recordsToProcess.Count;
                    _logger.LogInformation($"查询结果: 本批次记录数={currentBatchCount}");

                    if (currentBatchCount == 0)
                    {
                        _logger.LogInformation("没有更多记录需要处理，任务完成");
                        break;
                    }

                    _logger.LogInformation($"开始处理本批次的{currentBatchCount}条记录");

                    // 改为串行处理每个权益卡，避免事务冲突
                    foreach (var item in recordsToProcess)
                    {
                        _logger.LogInformation($"处理用户[{item.user.account_id}]的权益卡");
                        try
                        {
                            var result = await ProcessCustomerEquityAsync(token, item);

                            if (result.Success)
                            {
                                succeed++;
                                var successMessage = $"手机:{item.user.account_id}, 卡号:{result.CardNo}, message:{result.Message}";
                                successData.Add(successMessage);
                                _logger.LogInformation($"用户[{item.user.account_id}]的权益卡处理成功，当前成功总数: {succeed}");
                            }
                            else
                            {
                                fail++;
                                if (!string.IsNullOrEmpty(result.ErrorMessage))
                                {
                                    var errorMsg = $"手机:{item.user.account_id}, 卡别名:{item.card_alias}, 错误:{result.ErrorMessage}";
                                    errorData.Add(errorMsg);
                                    _logger.LogError($"用户[{item.user.account_id}]的权益卡处理失败: {result.ErrorMessage}，当前失败总数: {fail}");
                                }
                            }
                        }
                        catch (Exception itemEx)
                        {
                            fail++;
                            var errorMsg = $"手机:{item.user.account_id}, 卡别名:{item.card_alias}, 错误:{itemEx.Message}";
                            errorData.Add(errorMsg);
                            _logger.LogError(itemEx, errorMsg);
                        }

                        // 增加延迟，降低系统压力，减少事务冲突
                        await Task.Delay(500);
                    }

                    // 每个批次处理完后增加额外延迟，让数据库和网络请求有更多恢复时间
                    await Task.Delay(1000);
                }
                catch (Exception batchEx)
                {
                    _logger.LogError(batchEx, $"处理批次数据时发生异常");
                    // 不增加失败计数，继续尝试下一批
                    await Task.Delay(2000); // 发生异常后增加额外延迟
                }
            }

            var resultData = new resultLogDto();
            resultData.succeed = succeed;
            resultData.fail = fail;
            resultData.succeed_data = successData.ToList();
            resultData.fail_data = errorData.ToList();

            _logger.LogInformation($"客户权益卡批量创建完成，成功: {succeed}, 失败: {fail}");
            return resultData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "客户权益卡批量创建过程中发生全局异常");
            var resultData = new resultLogDto();
            resultData.succeed = 0;
            resultData.fail = 1;
            resultData.fail_data = new List<string> { $"全局异常: {ex.Message}" };
            return resultData;
        }
    }

    /// <summary>
    /// 处理单个客户权益卡
    /// </summary>
    /// <param name="token">有赞接口访问令牌</param>
    /// <param name="item">客户权益卡请求信息</param>
    /// <returns>处理结果，包含成功/失败状态和错误信息</returns>
    private async Task<(bool Success, string ErrorMessage, string CardNo, string Message)> ProcessCustomerEquityAsync(string token, createCustomerEquityRequest item)
    {
        try
        {
            _logger.LogInformation($"开始处理用户[{item.user.account_id}]的权益卡申请 - 卡别名:{item.card_alias}");

            // 1. 查询该用户是否已有权益卡，并判断是否过期
            var checkParam = new YouzanParameter();
            checkParam.url = "youzan.scrm.customer.card.list/4.0.0";
            checkParam.method = "POST";
            var checkDict = new Dictionary<string, object>();
            checkDict.Add("params", new { user = new { account_type = item.user.account_type, account_id = item.user.account_id }, page_no = 1, state = true });
            checkParam.body = checkDict.ToJsonString();
            _logger.LogInformation($"查询用户[{item.user.account_id}]的权益卡列表 - 请求内容: {checkParam.body}");
            var checkRes = await _youzanService.GetData(checkParam);

            bool needDelete = false;
            string expiredCardNo = "";
            string expiredCardAlias = "";

            if (checkRes.success && checkRes.data != null)
            {
                var cardListData = checkRes.data.ToObject<customerCardItemDto>();
                if (cardListData.total > 0 && cardListData.items.Count > 0)
                {
                    var existingCard = cardListData.items.FirstOrDefault(c => c.card_alias == item.card_alias);
                    if (existingCard != null)
                    {
                        // 规范化日期处理
                        DateTime normalizedStartTime = NormalizeDateTimeValue(existingCard.card_start_time);
                        DateTime normalizedEndTime = NormalizeDateTimeValue(existingCard.card_end_time);

                        var cardStateDesc = existingCard.card_state switch
                        {
                            0 => "未激活",
                            1 => "使用中",
                            2 => "已退款",
                            3 => "已过期",
                            4 => "用户已删除",
                            5 => "商家已禁用",
                            6 => "管理员删除",
                            7 => "系统删除",
                            8 => "未生效",
                            _ => "未知状态"
                        };
                        var cardInfo = $"用户[{item.user.account_id}]已有权益卡信息 - 卡号:{existingCard.card_no}, 卡别名:{existingCard.card_alias}, 状态:{existingCard.card_state}({cardStateDesc}), 领卡日期:{normalizedStartTime:yyyy-MM-dd HH:mm:ss}, 有效期至:{normalizedEndTime:yyyy-MM-dd HH:mm:ss}";
                        _logger.LogInformation(cardInfo);

                        if (existingCard.card_state == 3)
                        {
                            var expiredInfo = $"用户[{item.user.account_id}]的权益卡已过期，准备删除 - 卡号:{existingCard.card_no}, 卡别名:{existingCard.card_alias}, 领卡日期:{normalizedStartTime:yyyy-MM-dd HH:mm:ss}, 有效期至:{normalizedEndTime:yyyy-MM-dd HH:mm:ss}";
                            _logger.LogInformation(expiredInfo);

                            needDelete = true;
                            expiredCardNo = existingCard.card_no;
                            expiredCardAlias = existingCard.card_alias;
                        }
                        else
                        {
                            var validInfo = $"用户[{item.user.account_id}]的权益卡未过期，无需重新发卡 - 卡号:{existingCard.card_no}, 卡别名:{existingCard.card_alias}, 状态:{existingCard.card_state}({cardStateDesc}), 领卡日期:{normalizedStartTime:yyyy-MM-dd HH:mm:ss}, 有效期至:{normalizedEndTime:yyyy-MM-dd HH:mm:ss}";
                            _logger.LogInformation(validInfo);

                            try
                            {
                                await modifySyncTagByPhone(item.user.account_id, item.card_alias, existingCard.card_no, "select", 1, "");
                                return (true, string.Empty, existingCard.card_no, "successful");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"更新用户[{item.user.account_id}]的有效卡标记时发生异常，但不影响业务逻辑 - 卡号:{existingCard.card_no}, 卡别名:{existingCard.card_alias}");
                                return (true, string.Empty, existingCard.card_no, "successful");
                            }
                        }
                    }
                    else
                    {
                        var noCardInfo = $"用户[{item.user.account_id}]没有找到匹配的权益卡(别名:{item.card_alias})，准备发放新卡";
                        _logger.LogInformation(noCardInfo);
                    }
                }
                else
                {
                    var emptyCardInfo = $"用户[{item.user.account_id}]没有任何权益卡记录，准备发放新卡";
                    _logger.LogInformation(emptyCardInfo);
                }
            }
            else
            {
                var queryFailInfo = $"查询用户[{item.user.account_id}]权益卡失败: {checkRes.message}";
                _logger.LogError(queryFailInfo);

                // 对特定错误进行特殊处理，直接标记为add-error并返回
                if (checkRes.message != null &&
                   (checkRes.message.Contains("invalid params") ||
                    checkRes.message.Contains("参数错误")))
                {
                    _logger.LogWarning($"检测到有赞API参数错误，只标记为add-error，不继续发卡: 用户[{item.user.account_id}], 错误: {checkRes.message}");

                    try
                    {
                        await modifySyncTagByPhone(item.user.account_id, item.card_alias, "", "add-error", 0, $"查询失败: {checkRes.message}");
                        _logger.LogInformation($"已将用户[{item.user.account_id}]标记为add-error状态");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"标记用户[{item.user.account_id}]为add-error状态时发生异常");
                    }

                    return (false, $"查询用户权益卡失败，已标记为错误状态: {checkRes.message}", "", "");
                }
                else
                {
                    // 其他错误情况，直接返回失败
                    try
                    {
                        await modifySyncTagByPhone(item.user.account_id, item.card_alias, "", "query-error", 0, queryFailInfo);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"标记用户[{item.user.account_id}]查询错误时发生异常");
                    }
                    return (false, queryFailInfo, "", "");
                }
            }

            // 2. 如果有过期卡，先删除
            if (needDelete)
            {
                var deleteParam = new YouzanParameter();
                deleteParam.url = "youzan.scrm.customer.card.delete/4.0.0";
                deleteParam.method = "POST";
                var deleteDict = new Dictionary<string, object>();
                var deleteRequest = new deleteCustomerEquityRequest
                {
                    card_alias = expiredCardAlias,
                    card_no = expiredCardNo,
                    user = item.user
                };
                deleteDict.Add("params", deleteRequest);
                deleteParam.body = deleteDict.ToJsonString();
                var deleteRes = await _youzanService.GetData(deleteParam);

                if (!deleteRes.success)
                {
                    var errorJson = deleteRes.message;
                    var deleteFailInfo = $"删除用户[{item.user.account_id}]过期权益卡失败 - 卡号:{expiredCardNo}, 卡别名:{expiredCardAlias}, 错误:{errorJson}";
                    _logger.LogError(deleteFailInfo);

                    try
                    {
                        await modifySyncTagByPhone(item.user.account_id, item.card_alias, "", "delete-error", 0, errorJson);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"更新用户[{item.user.account_id}]删除错误标记时发生异常 - 卡别名:{item.card_alias}");
                        // 继续流程，因为主要问题是删除卡失败，而不是更新标记失败
                    }

                    return (false, $"删除过期卡失败: {errorJson}", "", "");
                }
                else
                {
                    var deleteSuccessInfo = $"成功删除用户[{item.user.account_id}]过期权益卡 - 卡号:{expiredCardNo}, 卡别名:{expiredCardAlias}";
                    _logger.LogInformation(deleteSuccessInfo);
                }
            }

            // 3. 发放新卡
            var grantInfo = $"准备为用户[{item.user.account_id}]发放新权益卡 - 卡别名:{item.card_alias}";
            _logger.LogInformation(grantInfo);

            var param = new YouzanParameter();
            param.url = "youzan.scrm.customer.card.grant/4.0.0";
            param.method = "POST";
            var dict = new Dictionary<string, object>();
            dict.Add("params", item);
            param.body = dict.ToJsonString();
            _logger.LogInformation($"用户[{item.user.account_id}]发卡请求内容: {param.body}");
            var res = await _youzanService.GetData(param);

            if (res.success == true)
            {
                var result = res.data.ToObject<createCustomerEquityResponse>();
                var grantSuccessInfo = $"成功为用户[{item.user.account_id}]发放新权益卡 - 卡号:{result.card_no}, 卡别名:{item.card_alias}";
                _logger.LogInformation(grantSuccessInfo);

                bool syncTagSuccess = false;
                string syncTagError = "";

                try
                {
                    // 更新同步标记前添加短暂延迟，确保外部系统已处理完成
                    await Task.Delay(200);

                    _logger.LogInformation($"开始更新用户[{item.user.account_id}]的同步标记 - 卡号:{result.card_no}, 卡别名:{item.card_alias}");
                    await modifySyncTagByPhone(item.user.account_id, item.card_alias, result.card_no, "select", 1, "");
                    _logger.LogInformation($"成功更新用户[{item.user.account_id}]的同步标记 - 卡号:{result.card_no}, 卡别名:{item.card_alias}");
                    syncTagSuccess = true;
                }
                catch (Exception ex)
                {
                    // 即使更新标记失败，发卡操作已经成功了
                    syncTagError = ex.Message;
                    _logger.LogError(ex, $"更新用户[{item.user.account_id}]同步标记时发生异常 - 卡号:{result.card_no}, 卡别名:{item.card_alias}");
                    // 不返回失败，因为发卡已经成功
                }

                // 添加1秒延迟
                await Task.Delay(1000);

                if (!syncTagSuccess)
                {
                    _logger.LogWarning($"用户[{item.user.account_id}]发卡成功但标记更新失败，但不影响结果: {syncTagError}");
                }

                return (true, string.Empty, result.card_no, res.message);
            }
            else
            {
                var errorJson = res.message;
                var grantFailInfo = $"为用户[{item.user.account_id}]发放新权益卡失败 - 卡别名:{item.card_alias}, 错误:{errorJson}";
                _logger.LogError(grantFailInfo);

                bool syncTagSuccess = false;
                string syncTagError = "";

                try
                {
                    await modifySyncTagByPhone(item.user.account_id, item.card_alias, "", "add-error", 0, errorJson);
                    syncTagSuccess = true;
                }
                catch (Exception ex)
                {
                    syncTagError = ex.Message;
                    _logger.LogError(ex, $"更新用户[{item.user.account_id}]错误标记时发生异常 - 卡别名:{item.card_alias}");
                    // 继续返回原始错误
                }

                if (!syncTagSuccess)
                {
                    _logger.LogWarning($"用户[{item.user.account_id}]标记更新失败，错误: {syncTagError}，但不影响最终结果");
                }

                return (false, errorJson, "", "");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"处理用户[{item.user.account_id}]权益卡时发生异常: {ex.Message}");
            if (ex.InnerException != null)
            {
                _logger.LogError($"用户[{item.user.account_id}]处理时发生内部异常: {ex.InnerException.Message}");
            }

            // 检查是否是SqlTransaction异常，如果是则提供更具体的信息
            if (ex.Message.Contains("SqlTransaction") || (ex.InnerException != null && ex.InnerException.Message.Contains("SqlTransaction")))
            {
                _logger.LogError($"用户[{item.user.account_id}]处理时检测到事务异常，很可能是并发或连接池问题导致的事务无法使用，需要检查连接池配置");
            }

            // 尝试标记错误状态
            try
            {
                await modifySyncTagByPhone(item.user.account_id, item.card_alias, "", "process-error", 0, ex.Message);
            }
            catch
            {
                // 忽略标记过程中的错误
            }

            return (false, ex.Message, "", "");
        }
    }

    /// <summary>
    /// 自定义DateTime JSON转换器
    /// </summary>
    private class DateTimeJsonConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string dateStr = reader.GetString();
            if (DateTime.TryParse(dateStr, out DateTime result))
            {
                // 检查时间是否已经包含了8小时的偏移
                if (result.Hour == 8 && result.Minute == 0 && result.Second == 0)
                {
                    // 如果时间是8:00:00，说明可能被自动加了8小时，需要减回去
                    return result.AddHours(-8);
                }
                return result;
            }
            return DateTime.MinValue;
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }

    /// <summary>
    /// 规范化日期时间值，处理超出范围的情况
    /// </summary>
    private DateTime NormalizeDateTimeValue(DateTime dateTime)
    {
        try
        {
            // SQL Server datetime 类型的最大值是 9999-12-31 23:59:59.997
            DateTime maxSqlDateTime = new DateTime(9999, 12, 31, 23, 59, 59, 997);

            // 如果日期超出范围，使用最大值
            if (dateTime > maxSqlDateTime)
            {
                _logger.LogWarning($"检测到超出范围的日期时间值 {dateTime:yyyy-MM-dd HH:mm:ss.fff}，将使用最大允许值 {maxSqlDateTime:yyyy-MM-dd HH:mm:ss.fff}");
                return maxSqlDateTime;
            }

            return dateTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"处理日期时间值时发生异常: {dateTime:yyyy-MM-dd HH:mm:ss.fff}");
            // 发生异常时返回一个安全的默认值
            return new DateTime(2099, 12, 31, 23, 59, 59);
        }
    }

    /// <summary>
    /// 转换时间为UTC时间（从中国时区）
    /// </summary>
    private DateTime ConvertToLocalTime(DateTime dateTime)
    {
        try
        {
            if (dateTime.Kind == DateTimeKind.Unspecified)
            {
                return dateTime.AddHours(-8); // 从中国时区转换到UTC时间
            }
            return dateTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"转换时间到UTC时区时发生异常: {dateTime:yyyy-MM-dd HH:mm:ss.fff}");
            return dateTime; // 发生异常时返回原始时间
        }
    }

    /// <summary>
    /// 删除客户权益卡
    /// </summary>
    /// <returns>处理结果，包含成功和失败的统计信息</returns>
    [AllowAnonymous, HttpPost("deleteEquity")]
    public async Task<dynamic> deleteCustomerEquity()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();
        while (true)
        {
            var query = new customerQuery();
            query.currentPage = page_index;
            query.pageSize = 20;
            query.tag_status = "delete";
            var pageData = await getPageDeleteCustomerEquityList(query);
            //共计多少页
            var Total = pageData.pagination.Total;
            if (Total == 0)
                break;
            foreach (var item in pageData.list)
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.scrm.customer.card.delete/4.0.0";
                    param.method = "POST";
                    var dict = new Dictionary<string, object>();
                    dict.Add("params", item);
                    param.body = dict.ToJsonString();
                    var res = await _youzanService.GetData(param);
                    if (res.success == true)
                    {
                        succeed++;
                        await deleteSyncTagByPhone(item.user.account_id, item.card_no);
                    }
                    else
                    {
                        var errorJson = res.message;
                        errorData.Add(errorJson);
                        await deleteSyncTagByPhone(item.user.account_id, item.card_no, "delete-error", errorJson);
                        fail++;
                    }
                }
                catch (Exception ex)
                {
                    errorData.Add(ex.Message);
                }
            }
        }
        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.fail_data = errorData;
        return resultData;
    }

    /// <summary>
    /// 权益卡续期
    /// </summary>
    /// <returns>处理结果，包含成功和失败的统计信息</returns>
    [AllowAnonymous, HttpPost("equityRenewal")]
    public async Task<dynamic> equityRenewal()
    {
        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        int page_index = 1;
        var errorData = new List<string>();
        var successData = new List<string>();
        while (true)
        {
            var query = new customerQuery();
            query.currentPage = page_index;
            query.pageSize = 20;
            query.tag_status = "add";
            var pageData = await getPageCustomerEquityRenewalList(query);
            //共计多少页
            var Total = pageData.pagination.Total;
            if (Total == 0)
                break;
            foreach (var item in pageData.list)
            {
                try
                {
                    var param = new YouzanParameter();
                    param.url = "youzan.scrm.card.add.data/1.0.0";
                    param.method = "POST";
                    param.body = item.ToJsonString();
                    var res = await _youzanService.GetData(param);
                    if (res.success == true)
                    {
                        await modifyEquityRenewalTag(item.yz_open_id, item.card_no);
                        succeed++;
                        // 记录成功明细
                        successData.Add($"卡号:{item.card_no}, 续期天数:{item.extension_date_num}, message:{res.message}");
                    }
                    else
                    {
                        var errorJson = res.message;
                        errorData.Add($"卡号:{item.card_no}, 续期天数:{item.extension_date_num}, 错误:{errorJson}");
                        await modifyEquityRenewalTag(item.yz_open_id, item.card_no, "add-error", errorJson);
                        fail++;
                    }
                }
                catch (Exception ex)
                {
                    errorData.Add($"卡号:{item.card_no}, 续期天数:{item.extension_date_num}, 错误:{ex.Message}");
                    fail++;
                }
            }
        }
        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.succeed_data = successData;
        resultData.fail_data = errorData;
        return resultData;
    }

    /// <summary>
    /// 删除客户优惠券
    /// </summary>
    /// <returns>处理结果，包含成功和失败的统计信息</returns>
    [AllowAnonymous, HttpPost("deleteVoucher")]
    public async Task<dynamic> deleteCustomerVoucher()
    {
        try
        {
            _logger.LogInformation("开始执行客户优惠券批量删除");

            var token = await _youzanService.GetTokenAsync();
            int succeed = 0;
            int fail = 0;
            int processedCount = 0;
            var errorData = new ConcurrentBag<string>();

            // 一次性查询所有符合条件的记录
            _logger.LogInformation("正在查询所有符合条件的优惠券记录");

            var allRecordsToProcess = await _repository.Queryable<customerEquityEntity>()
                        .Where(a => a.created_date > DateTime.Now.AddDays(-5) && a.equity_card_no != null && a.equity_card_no != "")
                    .OrderBy(a => a.created_date)
                        .Select(a => new {
                            a.phone,
                            a.yz_open_id,
                            a.equity_card_no,
                            a.equity_card_alias_id
                        })
                        .ToListAsync();

            int totalCount = allRecordsToProcess.Count;
            _logger.LogInformation($"查询结果: 共找到{totalCount}条需要处理的记录");

            if (totalCount == 0)
            {
                _logger.LogInformation("没有符合条件的记录需要处理，任务完成");
                return new resultLogDto { succeed = 0, fail = 0, fail_data = new List<string>() };
            }

            // 设置并发批处理的大小
            const int batchSize = 10;
            var semaphore = new SemaphoreSlim(5); // 限制最大并发数为5

            // 分批次处理所有记录
            for (int i = 0; i < totalCount; i += batchSize)
            {
                var currentBatch = allRecordsToProcess.Skip(i).Take(batchSize).ToList();
                int currentBatchCount = currentBatch.Count;
                _logger.LogInformation($"开始处理第{i / batchSize + 1}批，本批次记录数={currentBatchCount}，总进度：{processedCount}/{totalCount}");

                // 并行处理当前批次中的所有记录
                await Task.WhenAll(currentBatch.Select(async item =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        string customerId = item.phone;
                        if (string.IsNullOrEmpty(item.yz_open_id))
                        {
                            _logger.LogWarning($"用户[手机:{customerId}]的yz_open_id为空，无法删除优惠券，跳过处理");
                            Interlocked.Increment(ref processedCount);
                            return;
                        }

                        _logger.LogInformation($"处理用户[手机:{customerId}, 有赞ID:{item.yz_open_id}]的优惠券删除");
                        try
                        {
                            var result = await ProcessVoucherDeleteAsync(item.yz_open_id, customerId);

                            if (result.Success)
                            {
                                Interlocked.Increment(ref succeed);
                                _logger.LogInformation($"用户[手机:{customerId}, 有赞ID:{item.yz_open_id}]的优惠券删除成功");
                            }
                            else
                            {
                                Interlocked.Increment(ref fail);
                                if (!string.IsNullOrEmpty(result.ErrorMessage))
                                {
                                    errorData.Add(result.ErrorMessage);
                                    _logger.LogError($"用户[手机:{customerId}, 有赞ID:{item.yz_open_id}]的优惠券删除失败: {result.ErrorMessage}");
                                }
                            }
                        }
                        catch (Exception itemEx)
                        {
                            Interlocked.Increment(ref fail);
                            var errorMsg = $"处理用户[手机:{customerId}, 有赞ID:{item.yz_open_id}]优惠券删除时出现未处理异常: {itemEx.Message}";
                            errorData.Add(errorMsg);
                            _logger.LogError(itemEx, errorMsg);
                        }

                        Interlocked.Increment(ref processedCount);
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));

                _logger.LogInformation($"第{i / batchSize + 1}批处理完成，总进度：{processedCount}/{totalCount}，成功：{succeed}，失败：{fail}");
            }

            var resultData = new resultLogDto
            {
                succeed = succeed,
                fail = fail,
                fail_data = errorData.ToList()
            };

            _logger.LogInformation($"客户优惠券批量删除完成，总记录数：{totalCount}，成功: {succeed}, 失败: {fail}");
            return resultData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "客户优惠券批量删除过程中发生全局异常");
            return new resultLogDto
            {
                succeed = 0,
                fail = 1,
                fail_data = new List<string> { $"全局异常: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// 处理单个客户优惠券删除
    /// </summary>
    /// <param name="yzOpenId">用户有赞Open ID</param>
    /// <param name="phone">用户手机号</param>
    /// <returns>处理结果，包含成功/失败状态和错误信息</returns>
    private async Task<(bool Success, string ErrorMessage)> ProcessVoucherDeleteAsync(string yzOpenId, string phone = null)
    {
        try
        {
            string userIdentifier = string.IsNullOrEmpty(phone) ? $"有赞ID:{yzOpenId}" : $"手机:{phone}, 有赞ID:{yzOpenId}";
            _logger.LogInformation($"开始处理用户[{userIdentifier}]的优惠券删除");

            // 准备请求参数
            var param = new YouzanParameter();
            param.url = "youzan.ump.batch.delete.voucher/1.0.0";
            param.method = "POST";

            var request = new deleteVoucherRequest
            {
                yz_open_id = yzOpenId,
                operator_yz_open_id = "C4VfYkPl626878234184654848" // 使用固定的管理员ID
            };

            var requestDict = new Dictionary<string, object>();
            requestDict.Add("request", request);
            param.body = requestDict.ToJsonString();

            _logger.LogInformation($"删除用户[{userIdentifier}]优惠券请求内容: {param.body}");
            var res = await _youzanService.GetData(param);

            if (res.success)
            {
                var successInfo = $"成功删除用户[{userIdentifier}]的优惠券";
                _logger.LogInformation(successInfo);
                return (true, string.Empty);
            }
            else
            {
                var errorJson = res.message;
                var errorInfo = $"删除用户[{userIdentifier}]的优惠券失败: {errorJson}";
                _logger.LogError(errorInfo);
                return (false, errorJson);
            }
        }
        catch (Exception ex)
        {
            string userIdentifier = string.IsNullOrEmpty(phone) ? $"有赞ID:{yzOpenId}" : $"手机:{phone}, 有赞ID:{yzOpenId}";
            _logger.LogError(ex, $"处理用户[{userIdentifier}]优惠券删除时发生异常: {ex.Message}");
            return (false, ex.Message);
        }
    }

    /// <summary>
    /// 重新下载权益卡信息
    /// </summary>
    /// <returns>处理结果，包含成功和失败的统计信息</returns>
    [AllowAnonymous, HttpPost("resyncEquityInfo")]
    public async Task<dynamic> resyncEquityInfo()
    {
        try
        {
            _logger.LogInformation("开始执行权益卡信息重新同步");

            var token = await _youzanService.GetTokenAsync();
            var succeed = 0;
            var fail = 0;
            var errorData = new ConcurrentBag<string>();

            // 减小批量处理的最大记录数，避免过大并发
            const int batchSize = 10;
            // 记录处理失败的记录
            var failedRecords = new ConcurrentDictionary<string, (string Phone, string CardAliasId)>();

            while (true)
            {
                try
                {
                    _logger.LogInformation($"正在获取{batchSize}条待处理的权益卡记录");

                    // 获取固定数量的记录，排除已知失败的记录
                    var recordsToProcess = await _repository.Queryable<customerEquityEntity>()
                        .Where(a => a.tag_status == "sync")
                        .OrderBy(a => a.created_date)
                        .Take(batchSize)
                        .ToListAsync();

                    int currentBatchCount = recordsToProcess.Count;
                    _logger.LogInformation($"查询结果: 本批次记录数={currentBatchCount}");

                    if (currentBatchCount == 0)
                    {
                        _logger.LogInformation("没有更多记录需要处理，任务完成");
                        break;
                    }

                    _logger.LogInformation($"开始处理本批次的{currentBatchCount}条记录");

                    // 创建批量更新列表
                    var updateBatch = new List<(customerEquityEntity Entity, customerCardItemDto CardInfo)>();

                    // 首先收集所有需要更新的数据
                    foreach (var item in recordsToProcess)
                    {
                        string recordKey = $"{item.phone}_{item.equity_card_alias_id}";

                        // 检查是否是已知的失败记录
                        if (failedRecords.ContainsKey(recordKey))
                        {
                            _logger.LogWarning($"跳过已知失败的记录: 用户[{item.phone}], 卡别名[{item.equity_card_alias_id}]");
                            continue;
                        }

                        _logger.LogInformation($"处理用户[{item.phone}]的权益卡信息同步");
                        try
                        {
                            var checkParam = new YouzanParameter();
                            checkParam.url = "youzan.scrm.customer.card.list/4.0.0";
                            checkParam.method = "POST";
                            var checkDict = new Dictionary<string, object>();
                            checkDict.Add("params", new {
                                user = new {
                                    account_type = 2,
                                    account_id = item.phone
                                },
                                page_no = 1,
                                state = true
                            });
                            checkParam.body = checkDict.ToJsonString();
                            var checkRes = await _youzanService.GetData(checkParam);

                            if (checkRes.success && checkRes.data != null)
                            {
                                // 使用自定义设置进行反序列化
                                var options = new JsonSerializerOptions
                                {
                                    Converters = { new DateTimeJsonConverter() }
                                };

                                _logger.LogInformation($"API原始响应数据: {checkRes.data}");

                                var cardListData = JsonSerializer.Deserialize<customerCardItemDto>(checkRes.data.ToString(), options);

                                if (cardListData.total > 0 && cardListData.items.Count > 0)
                                {
                                    var existingCard = cardListData.items.FirstOrDefault(c => c.card_alias == item.equity_card_alias_id);
                                    if (existingCard != null)
                                    {
                                        // 规范化日期
                                        existingCard.card_start_time = NormalizeDateTimeValue(existingCard.card_start_time);
                                        existingCard.card_end_time = NormalizeDateTimeValue(existingCard.card_end_time);

                                        cardListData.items[cardListData.items.IndexOf(existingCard)] = existingCard;
                                        updateBatch.Add((item, cardListData));
                                    }
                                    else
                                    {
                                        var noCardError = $"未找到用户[{item.phone}]对应的权益卡(别名:{item.equity_card_alias_id})";
                                        errorData.Add(noCardError);
                                        _logger.LogWarning(noCardError);

                                        await UpdateErrorStatus(item, "sync-error", noCardError);
                                        failedRecords.TryAdd(recordKey, (item.phone, item.equity_card_alias_id));
                                        fail++;
                                    }
                                }
                                else
                                {
                                    var noCardsError = $"用户[{item.phone}]没有任何权益卡记录";
                                    errorData.Add(noCardsError);
                                    _logger.LogWarning(noCardsError);

                                    await UpdateErrorStatus(item, "sync-error", noCardsError);
                                    failedRecords.TryAdd(recordKey, (item.phone, item.equity_card_alias_id));
                                    fail++;
                                }
                            }
                            else
                            {
                                var apiError = $"查询用户[{item.phone}]权益卡失败: {checkRes.message}";
                                errorData.Add(apiError);
                                _logger.LogError(apiError);

                                await UpdateErrorStatus(item, "sync-error", apiError);
                                failedRecords.TryAdd(recordKey, (item.phone, item.equity_card_alias_id));
                                fail++;
                            }
                        }
                        catch (Exception itemEx)
                        {
                            fail++;
                            var errorMsg = $"处理用户[{item.phone}]权益卡信息同步时出现未处理异常: {itemEx.Message}";
                            errorData.Add(errorMsg);
                            _logger.LogError(itemEx, errorMsg);

                            await UpdateErrorStatus(item, "sync-error", errorMsg);
                            failedRecords.TryAdd(recordKey, (item.phone, item.equity_card_alias_id));
                        }
                    }

                    // 如果有需要更新的数据，执行批量更新
                    if (updateBatch.Any())
                    {
                        try
                        {
                            // 将批量数据分组，每组10条
                            var batchGroups = updateBatch.Chunk(10);

                            foreach (var group in batchGroups)
                            {
                                try
                                {
                                    var updates = group.Select(x =>
                                    {
                                        var card = x.CardInfo.items.First(c => c.card_alias == x.Entity.equity_card_alias_id);

                                        return new customerEquityEntity
                                        {
                                            phone = x.Entity.phone,
                                            equity_card_alias_id = x.Entity.equity_card_alias_id,
                                            equity_card_no = card.card_no,
                                            start_time = card.card_start_time,
                                            end_time = card.card_end_time,
                                            status = card.card_state switch
                                            {
                                                0 => 0,  // 未激活 -> 禁用
                                                1 => 1,  // 使用中 -> 启用
                                                2 => -1, // 已退款 -> 删除
                                                3 => -1, // 已过期 -> 删除
                                                4 => -1, // 用户已删除 -> 删除
                                                5 => 0,  // 商家已禁用 -> 禁用
                                                6 => -1, // 管理员删除 -> 删除
                                                7 => -1, // 系统删除 -> 删除
                                                8 => 0,  // 未生效 -> 禁用
                                                _ => 0   // 未知状态 -> 禁用
                                            },
                                            tag_status = "select",
                                            modify_date = DateTime.Now
                                        };
                                    }).ToList();

                                    // 执行批量更新
                                    var updateResult = await _repository.Updateable<customerEquityEntity>(updates)
                                        .UpdateColumns(it => new {
                                            it.equity_card_no,
                                            it.start_time,
                                            it.end_time,
                                            it.status,
                                            it.tag_status,
                                            it.modify_date
                                        })
                                        .WhereColumns(it => new { it.phone, it.equity_card_alias_id })
                                        .ExecuteCommandAsync();

                                    succeed += updateResult;

                                    _logger.LogInformation($"批量更新完成，本批次成功更新{updateResult}条记录");
                                }
                                catch (Exception groupEx)
                                {
                                    // 处理每组更新的错误
                                    foreach (var item in group)
                                    {
                                        string recordKey = $"{item.Entity.phone}_{item.Entity.equity_card_alias_id}";
                                        var errorMsg = $"更新用户[{item.Entity.phone}]权益卡数据时发生错误: {groupEx.Message}";
                                        errorData.Add(errorMsg);
                                        _logger.LogError(groupEx, errorMsg);

                                        await UpdateErrorStatus(item.Entity, "db-error", errorMsg);
                                        failedRecords.TryAdd(recordKey, (item.Entity.phone, item.Entity.equity_card_alias_id));
                                        fail++;
                                    }
                                }
                            }
                        }
                        catch (Exception batchEx)
                        {
                            _logger.LogError(batchEx, "执行批量更新时发生错误");
                            // 将整个批次标记为失败
                            foreach (var item in updateBatch)
                            {
                                string recordKey = $"{item.Entity.phone}_{item.Entity.equity_card_alias_id}";
                                var errorMsg = $"批量更新过程中发生错误: {batchEx.Message}";
                                errorData.Add(errorMsg);

                                await UpdateErrorStatus(item.Entity, "batch-error", errorMsg);
                                failedRecords.TryAdd(recordKey, (item.Entity.phone, item.Entity.equity_card_alias_id));
                                fail++;
                            }
                        }
                    }
                }
                catch (Exception batchEx)
                {
                    _logger.LogError(batchEx, $"处理批次数据时发生异常");
                    await Task.Delay(1000); // 发生异常后增加额外延迟
                }
            }

            var resultData = new resultLogDto();
            resultData.succeed = succeed;
            resultData.fail = fail;
            resultData.fail_data = errorData.ToList();

            _logger.LogInformation($"权益卡信息重新同步完成，成功: {succeed}, 失败: {fail}, 失败记录数: {failedRecords.Count}");
            return resultData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "权益卡信息重新同步过程中发生全局异常");
            var resultData = new resultLogDto();
            resultData.succeed = 0;
            resultData.fail = 1;
            resultData.fail_data = new List<string> { $"全局异常: {ex.Message}" };
            return resultData;
        }
    }

    /// <summary>
    /// 更新客户权益卡同步标记
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="equity_card_alias_id">权益卡别名ID</param>
    /// <param name="equity_card_no">权益卡号</param>
    /// <param name="tag_status">标记状态</param>
    /// <param name="status">状态</param>
    /// <param name="tag_body">标记内容</param>
    private async Task modifySyncTagByPhone(string phone, string equity_card_alias_id, string equity_card_no, string tag_status = "select", int status = 1, string tag_body = "")
    {
        // 添加更详细的日志
        _logger.LogInformation($"开始处理同步标记: 用户[{phone}], 卡别名: {equity_card_alias_id}, 卡号: {equity_card_no}, 状态: {status}, 标记: {tag_status}");

        // 添加重试机制
        int maxRetries = 3;
        int retryCount = 0;
        bool success = false;

        while (!success && retryCount < maxRetries)
        {
            try
            {
                retryCount++;
                _logger.LogInformation($"尝试更新同步标记 (尝试 {retryCount}/{maxRetries}): 用户[{phone}], 卡别名: {equity_card_alias_id}");

                // 改为直接执行数据库操作，不使用事务
                try
                {
                    // 先查询记录是否存在
                    var exists = await _repository.Queryable<customerEquityEntity>().With(SqlWith.NoLock).AnyAsync(x => x.phone == phone && x.equity_card_alias_id == equity_card_alias_id);
                    if (exists)
                    {
                        _logger.LogInformation($"找到记录，准备更新: 用户[{phone}], 卡别名: {equity_card_alias_id}");
                        // 直接执行更新，不使用事务
                        var updateResult = await _repository.Updateable<customerEquityEntity>().SetColumns(it => new customerEquityEntity()
                        {
                            tag_status = tag_status,
                            equity_card_no = equity_card_no,
                            modify_date = DateTime.Now,
                            status = status,
                            tag_body = tag_body
                        }).Where(x => x.phone == phone && x.equity_card_alias_id == equity_card_alias_id)
                        .ExecuteCommandAsync();
                        _logger.LogInformation($"成功更新记录: 用户[{phone}], 卡别名: {equity_card_alias_id}, 受影响行数: {updateResult}");
                        success = true;
                    }
                    else
                    {
                        _logger.LogWarning($"未找到记录，跳过更新: 用户[{phone}], 卡别名: {equity_card_alias_id}");
                        success = true; // 如果记录不存在，也视为成功
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"数据库操作异常: 用户[{phone}], 卡别名: {equity_card_alias_id}");
                    if (retryCount < maxRetries)
                    {
                        await Task.Delay(500 * retryCount); // 指数退避
                        continue;
                    }
                    throw;
                }
                // 不明白这样写的原因,先屏蔽
                //if (success)
                if (false)
                {
                    _logger.LogInformation($"数据库更新成功，准备发布事件: 用户[{phone}], 卡别名: {equity_card_alias_id}");

                    try
                    {
                        // 数据库操作成功后，稍微延迟一下再发布事件，确保操作完全完成
                        await Task.Delay(300);

                        // 在事务外发布事件，确保使用有效的JSON格式
                        var eventData = new Dictionary<string, object>
                        {
                            ["phone"] = phone,
                            ["equity_card_alias_id"] = equity_card_alias_id,
                            ["equity_card_no"] = equity_card_no,
                            ["tag_status"] = tag_status,
                            ["status"] = status,
                            ["tag_body"] = tag_body,
                            ["modify_date"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        };

                        string eventPayload = JsonSerializer.Serialize(eventData);
                        _logger.LogInformation($"发布事件内容: {eventPayload}");

                        await _eventPublisher.PublishAsync("CustomerEquity:UpdateTag", eventPayload);

                        _logger.LogInformation($"事件发布成功: 用户[{phone}], 卡别名: {equity_card_alias_id}");
                    }
                    catch (Exception ex)
                    {
                        // 事件发布失败不影响数据库操作的成功状态
                        _logger.LogError(ex, $"事件发布异常: 用户[{phone}], 卡别名: {equity_card_alias_id}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新同步标记异常(尝试 {retryCount}/{maxRetries}): 用户[{phone}], 卡别名: {equity_card_alias_id}");
                if (retryCount < maxRetries)
                {
                    await Task.Delay(500 * retryCount); // 指数退避
                }
                else
                {
                    throw; // 最后一次重试仍然失败，则抛出异常
                }
            }
        }

        if (!success)
        {
            _logger.LogError($"在{maxRetries}次尝试后，更新同步标记最终失败: 用户[{phone}], 卡别名: {equity_card_alias_id}");
        }
    }

    /// <summary>
    /// 更新客户权益卡续期标记
    /// </summary>
    /// <param name="yz_open_id">有赞开放ID</param>
    /// <param name="equity_card_no">权益卡号</param>
    /// <param name="tag_status">标记状态</param>
    /// <param name="tag_body">标记内容</param>
    private async Task modifyEquityRenewalTag(string yz_open_id, string equity_card_no, string tag_status = "select", string tag_body = "")
    {
        _logger.LogInformation($"开始处理续期标记: 用户ID: {yz_open_id}, 卡号: {equity_card_no}, 标记: {tag_status}");

        // 添加重试机制
        int maxRetries = 3;
        int retryCount = 0;
        bool success = false;

        while (!success && retryCount < maxRetries)
        {
            try
            {
                retryCount++;

                _logger.LogInformation($"尝试更新续期标记 (尝试 {retryCount}/{maxRetries}): 用户ID: {yz_open_id}, 卡号: {equity_card_no}");

                // 改为直接执行数据库操作，不使用事务
                try
                {
                    // 先查询记录是否存在
                    var exists = await _repository.Queryable<customerEquityRenewalEntity>()
                        .AnyAsync(x => x.yz_open_id == yz_open_id && x.equity_card_no == equity_card_no);

                    if (exists)
                    {
                        _logger.LogInformation($"找到续期记录，准备更新: 用户ID: {yz_open_id}, 卡号: {equity_card_no}");

                        // 直接执行更新，不使用事务
                        var updateResult = await _repository.Updateable<customerEquityRenewalEntity>()
                            .SetColumns(it => new customerEquityRenewalEntity()
                            {
                                tag_status = tag_status,
                                equity_card_no = equity_card_no,
                                modify_date = DateTime.Now,
                                tag_body = tag_body
                            })
                            .Where(x => x.yz_open_id == yz_open_id && x.equity_card_no == equity_card_no)
                            .ExecuteCommandAsync();

                        _logger.LogInformation($"成功更新续期记录: 用户ID: {yz_open_id}, 卡号: {equity_card_no}, 受影响行数: {updateResult}");
                        success = true;
                    }
                    else
                    {
                        _logger.LogWarning($"未找到续期记录，跳过更新: 用户ID: {yz_open_id}, 卡号: {equity_card_no}");
                        success = true; // 如果记录不存在，也视为成功
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"续期标记数据库操作异常: 用户ID: {yz_open_id}, 卡号: {equity_card_no}");
                    if (retryCount < maxRetries)
                    {
                        await Task.Delay(500 * retryCount); // 指数退避
                        continue;
                    }
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新续期标记异常(尝试 {retryCount}/{maxRetries}): 用户ID: {yz_open_id}, 卡号: {equity_card_no}");
                if (retryCount < maxRetries)
                {
                    await Task.Delay(500 * retryCount); // 指数退避
                }
                else
                {
                    throw; // 最后一次重试仍然失败，则抛出异常
                }
            }
        }

        if (!success)
        {
            _logger.LogError($"在{maxRetries}次尝试后，更新续期标记最终失败: 用户ID: {yz_open_id}, 卡号: {equity_card_no}");
        }
    }

    /// <summary>
    /// 删除客户权益卡同步标记
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="equity_card_no">权益卡号</param>
    /// <param name="tag_status">标记状态</param>
    /// <param name="tag_body">标记内容</param>
    private async Task deleteSyncTagByPhone(string phone, string equity_card_no, string tag_status = "select", string tag_body = "")
    {
        _logger.LogInformation($"开始处理删除标记: 用户[{phone}], 卡号: {equity_card_no}, 标记: {tag_status}");

        // 添加重试机制
        int maxRetries = 3;
        int retryCount = 0;
        bool success = false;

        while (!success && retryCount < maxRetries)
        {
            try
            {
                retryCount++;

                _logger.LogInformation($"尝试更新删除标记 (尝试 {retryCount}/{maxRetries}): 用户[{phone}], 卡号: {equity_card_no}");

                // 改为直接执行数据库操作，不使用事务
                try
                {
                    // 先查询记录是否存在
                    var exists = await _repository.Queryable<customerEquityEntity>()
                        .AnyAsync(x => x.phone == phone && x.equity_card_no == equity_card_no);

                    if (exists)
                    {
                        _logger.LogInformation($"找到记录，准备更新删除标记: 用户[{phone}], 卡号: {equity_card_no}");

                        // 直接执行更新，不使用事务
                        var updateResult = await _repository.Updateable<customerEquityEntity>()
                            .SetColumns(it => new customerEquityEntity()
                            {
                                tag_status = tag_status,
                                modify_date = ConvertToLocalTime(DateTime.Now),  // 使用本地时间
                                status = -1,
                                tag_body = tag_body
                            })
                            .Where(x => x.phone == phone && x.equity_card_no == equity_card_no)
                            .ExecuteCommandAsync();

                        _logger.LogInformation($"成功更新删除标记: 用户[{phone}], 卡号: {equity_card_no}, 受影响行数: {updateResult}");
                        success = true;
                    }
                    else
                    {
                        _logger.LogWarning($"未找到记录，跳过删除标记更新: 用户[{phone}], 卡号: {equity_card_no}");
                        success = true; // 如果记录不存在，也视为成功
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"删除标记数据库操作异常: 用户[{phone}], 卡号: {equity_card_no}");
                    if (retryCount < maxRetries)
                    {
                        await Task.Delay(500 * retryCount); // 指数退避
                        continue;
                    }
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新删除标记异常(尝试 {retryCount}/{maxRetries}): 用户[{phone}], 卡号: {equity_card_no}");
                if (retryCount < maxRetries)
                {
                    await Task.Delay(500 * retryCount); // 指数退避
                }
                else
                {
                    throw; // 最后一次重试仍然失败，则抛出异常
                }
            }
        }

        if (!success)
        {
            _logger.LogError($"在{maxRetries}次尝试后，更新删除标记最终失败: 用户[{phone}], 卡号: {equity_card_no}");
        }
    }

    /// <summary>
    /// 创建或更新权益卡模板
    /// </summary>
    /// <param name="list">权益卡模板列表</param>
    private async Task crateEquity(List<EquityEntity> list)
    {
        var insertList = new List<EquityEntity>();
        var updateList = new List<EquityEntity>();
        foreach (var item in list)
        {
            if (!_repository.Queryable<EquityEntity>().Where(x => x.card_alias == item.card_alias).Any())
            {
                item.id = Guid.NewGuid().ToString();
                insertList.Add(item);
            }
            else
            {
                var old_equity = await _repository.Queryable<EquityEntity>().Where(x => x.card_alias == item.card_alias).SingleAsync();
                old_equity.name = item.name;
                old_equity.card_alias = item.card_alias;
                old_equity.card_id = item.card_id;
                old_equity.card_url = item.card_url;
                old_equity.is_available = item.is_available;
                old_equity.grant_type = item.grant_type;
                updateList.Add(old_equity);
            }
        }
        // 插入权益卡模板
        if (insertList.Count > 0)
            await _repository.Insertable<EquityEntity>(insertList).ExecuteCommandAsync();
        // 更新权益卡模板
        if (updateList.Count > 0)
            await _repository.Updateable<EquityEntity>(updateList).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新错误状态
    /// </summary>
    private async Task UpdateErrorStatus(customerEquityEntity item, string tagStatus, string errorMessage)
    {
        try
        {
            await _repository.Updateable<customerEquityEntity>()
                .SetColumns(it => new customerEquityEntity
                {
                    tag_status = tagStatus,
                    tag_body = errorMessage,
                    modify_date = DateTime.Now
                })
                .Where(x => x.phone == item.phone && x.equity_card_alias_id == item.equity_card_alias_id)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"更新用户[{item.phone}]错误状态时发生异常");
        }
    }

    #region Private Method

    /// <summary>
    /// 获取待创建的客户权益卡列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <returns>分页后的客户权益卡列表</returns>
    private async Task<SqlSugarPagedList<createCustomerEquityRequest>> getPageCreateCustomerEquityList(customerQuery query)
    {
        var data = await _repository.Queryable<customerEquityEntity>()
            .Where(a => a.tag_status == query.tag_status)
            .Select(a => new createCustomerEquityRequest
            {
                card_alias = a.equity_card_alias_id,
                // 账户信息
                user = new userEquityInfo()
                {
                    account_id = a.phone,
                    account_type = 2
                }
            }).ToPagedListAsync(query.currentPage, query.pageSize);
        return data;
    }

    /// <summary>
    /// 获取待删除的客户权益卡列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <returns>分页后的客户权益卡列表</returns>
    private async Task<SqlSugarPagedList<deleteCustomerEquityRequest>> getPageDeleteCustomerEquityList(customerQuery query)
    {
        var data = await _repository.Queryable<customerEquityEntity>()
            .Where(a => a.tag_status == query.tag_status)
            .Select(a => new deleteCustomerEquityRequest
            {
                card_alias = a.equity_card_alias_id,
                card_no = a.equity_card_no,
                // 账户信息
                user = new userEquityInfo()
                {
                    account_id = a.phone,
                    account_type = 2
                }
            }).ToPagedListAsync(query.currentPage, query.pageSize);
        return data;
    }

    /// <summary>
    /// 获取待续期的客户权益卡列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns>分页后的客户权益卡列表</returns>
    private async Task<SqlSugarPagedList<customerEquityRenewalRequest>> getPageCustomerEquityRenewalList(customerQuery query)
    {
        var data = await _repository.Queryable<customerEquityRenewalEntity>()
            .Where(a => a.tag_status == query.tag_status)
            .Select(a => new customerEquityRenewalRequest
            {
                card_no = a.equity_card_no,
                extension_date_num = a.extension_end_time ?? 0,
                operator_yz_open_id = a.operator_yz_open_id,
                yz_open_id = a.yz_open_id
            }).ToPagedListAsync(query.currentPage, query.pageSize);
        return data;
    }

    /// <summary>
    /// 指定卡号权益卡续期
    /// </summary>
    /// <param name="cardNo">权益卡号</param>
    /// <returns>处理结果，包含成功和失败的统计信息</returns>
    [AllowAnonymous, HttpPost("equityRenewalByCardNo")]
    public async Task<dynamic> equityRenewalByCardNo([FromQuery] string cardNo)
    {
        if (string.IsNullOrEmpty(cardNo))
        {
            throw Oops.Oh("卡号不能为空");
        }

        var token = await _youzanService.GetTokenAsync();
        var succeed = 0;
        var fail = 0;
        var errorData = new List<string>();
        var successData = new List<string>();

        try
        {
            // 查询指定卡号的续期记录，只查询需要的字段
            var renewalInfo = await _repository.Queryable<customerEquityRenewalEntity>()
                .Where(x => x.equity_card_no == cardNo && x.tag_status == "add")
                .Select(x => new customerEquityRenewalRequest
                {
                    card_no = x.equity_card_no,
                    extension_date_num = x.extension_end_time ?? 0,
                    operator_yz_open_id = x.operator_yz_open_id,
                    yz_open_id = x.yz_open_id
                })
                .FirstAsync();

            if (renewalInfo == null)
            {
                throw Oops.Oh($"未找到卡号[{cardNo}]的续期记录或该记录不是待处理状态");
            }

            var param = new YouzanParameter();
            param.url = "youzan.scrm.card.add.data/1.0.0";
            param.method = "POST";
            param.body = renewalInfo.ToJsonString();
            var res = await _youzanService.GetData(param);

            if (res.success == true)
            {
                await modifyEquityRenewalTag(renewalInfo.yz_open_id, renewalInfo.card_no);
                succeed++;
                // 记录成功明细
                successData.Add($"卡号:{renewalInfo.card_no}, 续期天数:{renewalInfo.extension_date_num}, message:{res.message}");
            }
            else
            {
                var errorJson = res.message;
                errorData.Add($"卡号:{renewalInfo.card_no}, 续期天数:{renewalInfo.extension_date_num}, 错误:{errorJson}");
                await modifyEquityRenewalTag(renewalInfo.yz_open_id, renewalInfo.card_no, "add-error", errorJson);
                fail++;
            }
        }
        catch (Exception ex)
        {
            if (ex.Message.Contains("未找到卡号"))
            {
                throw;
            }
            errorData.Add($"卡号:{cardNo}, 错误:{ex.Message}");
            fail++;
            _logger.LogError(ex, $"处理卡号[{cardNo}]续期时发生异常");
        }

        var resultData = new resultLogDto();
        resultData.succeed = succeed;
        resultData.fail = fail;
        resultData.succeed_data = successData;
        resultData.fail_data = errorData;
        return resultData;
    }

    #endregion
}
