﻿using BPM.Common.Core.Manager;
using BPM.Common.Models;
using BPM.Engine.Entity.Model;
@if(Model.IsExport)
{
@:using BPM.ClayObject;
}
@if(Model.IsImportData || Model.IsExport)
{
@:using BPM.Common.Models.NPOI;
}
@if(Model.Type != 3)
{
@:using BPM.Common.CodeGen.ExportImport;
}
@if(Model.IsImportData)
{
@:using BPM.Common.Core.Manager.Files;
@:using BPM.Common.Dtos;
}
@if(Model.Type != 3)
{
@:using BPM.Common.CodeGen.DataParsing;
}
using BPM.Common.Manager;
using BPM.Common.Const;
using BPM.Common.Enums;
using BPM.Common.Extension;
using BPM.Common.Filter;
using BPM.Common.Security;
using BPM.DatabaseAccessor;
using BPM.DependencyInjection;
using BPM.DynamicApiController;
using BPM.FriendlyException;
using BPM.Systems.Entitys.System;
using BPM.Systems.Entitys.Permission;
using BPM.Systems.Interfaces.System;
using BPM.Common.Dtos.Datainterface;
@if(Model.Type != 3 && (Model.IsImportData || Model.ParsBpmKeyConstList.Count > 0))
{
@:using BPM.VisualDev.Engine;
}
@if(Model.IsImportData)
{
@:using BPM.VisualDev.Engine.Core;
@:using Microsoft.AspNetCore.Http;
}
using BPM.@(Model.NameSpace).Entitys.Dto.@(Model.ClassName);
@foreach(var table in Model.AuxiliayTableRelations)
{
@*循环出副表的命名空间*@
@:using BPM.@(Model.NameSpace).Entitys.Dto.@(table.ClassName);
}
using BPM.@(Model.NameSpace).Entitys;
using BPM.@(Model.NameSpace).Interfaces;
@if(Model.EnableFlow)
{
@:using BPM.WorkFlow.Entitys.Entity;
}
using Mapster;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using BPM.Common.Models.Authorize;

namespace BPM.@(Model.NameSpace);

/// <summary>
/// 业务实现：@(Model.BusName).
/// </summary>
[ApiDescriptionSettings(Tag = "@(Model.Type == 3 ? Model.NameSpace + "Form" : Model.NameSpace)", Name = "@Model.ClassName", Order = 200)]
[Route("api/@(Model.Type == 3 ? Model.NameSpace+ "/Form" : Model.NameSpace)/[controller]")]
public class @(Model.ClassName)Service : I@(Model.ClassName)Service, IDynamicApiController, ITransient
{
    /// <summary>
    /// 服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<@(Model.ClassName)Entity> _repository;
@if(Model.IsBillRule)
{
@:
    @:/// <summary>
    @:/// 单据规则服务.
    @:/// </summary>
    @:private readonly IBillRullService _billRullService;
}
@if(Model.DbLinkId != "0" || Model.IsImportData)
{
@:
    @:/// <summary>
    @:/// 数据库管理.
    @:/// </summary>
    @:private readonly IDataBaseManager _dataBaseManager;
}

    /// <summary>
    /// 数据接口服务.
    /// </summary>
    private readonly IDataInterfaceService _dataInterfaceService;
    
    /// <summary>
    /// 缓存管理.
    /// </summary>
    private readonly ICacheManager _cacheManager;
    
@if(Model.Type != 3)
{
@:
    @:/// <summary>
    @:/// 通用数据解析.
    @:/// </summary>
    @:private readonly ControlParsing _controlParsing;
}

    /// <summary>
    /// 用户管理.
    /// </summary>
    private readonly IUserManager _userManager;
@if(Model.IsImportData)
{
    @:/// <summary>
    @:/// 代码生成导出数据帮助类.
    @:/// </summary>
    @:private readonly ExportImportDataHelper _exportImportDataHelper;
@:
    @:/// <summary>
    @:/// 文件服务.
    @:/// </summary>
    @:private readonly IFileManager _fileManager;
}
@if(Model.DbLinkId != "0")
{
@:
    @:/// <summary>
    @:/// 客户端.
    @:/// </summary>
    @:private static SqlSugarScope? _sqlSugarClient;
}
@if(Model.IsImportData || Model.IsExport)
{
@:
    @:/// <summary>
    @:/// 导出字段.
    @:/// </summary>
    @:private readonly List<ParamsModel> paramList = "[@(Model.ExportField)]".ToList<ParamsModel>();
}
@if(Model.IsImportData)
{
@:
    @:/// <summary>
    @:/// 导入字段.
    @:/// </summary>
    @:private readonly string[] uploaderKey = new string[] @(Model.ImportColumnField);
}
@if(Model.DataRuleJson != null && Model.DataRuleJson != "[]")
{
@:
    @:/// <summary>
    @:/// 配置数据过滤.
    @:/// </summary>
    @:private readonly List<CodeGenDataRuleModuleResourceModel> dataRuleList = "@(Model.DataRuleJson)".ToObject<List<CodeGenDataRuleModuleResourceModel>>();
@foreach(var table in Model.AuxiliayTableRelations)
{
    @:private readonly List<IConditionalModel> @(table.LowerClassName)DataRule = new List<IConditionalModel>();
}
}

    /// <summary>
    /// 初始化一个<see cref="@(Model.ClassName)Service"/>类型的新实例.
    /// </summary>
    public @(Model.ClassName)Service(
        ISqlSugarRepository<@(Model.ClassName)Entity> repository,
@if(Model.IsBillRule)
{
        @:IBillRullService billRullService,
}
        IDataInterfaceService dataInterfaceService,
@if(Model.DbLinkId != "0" || Model.IsImportData)
{
        @:IDataBaseManager dataBaseManager,
}
@if(Model.DbLinkId != "0"){
        @:ISqlSugarClient context,
}
@if(Model.IsImportData)
{
        @:ExportImportDataHelper exportImportDataHelper,
        @:IFileManager fileManager,
}
        ICacheManager cacheManager,
@if(Model.Type != 3)
{
        @:ControlParsing controlParsing,
}
        IUserManager userManager)
    {
        _repository = repository;
@if(Model.IsBillRule)
{
        @:_billRullService = billRullService;
}
@if(Model.DbLinkId != "0" || Model.IsImportData)
{
        @:_dataBaseManager = dataBaseManager;
}
@if(Model.DbLinkId != "0")
{
        @:_sqlSugarClient = (SqlSugarScope)context;
}
@if(Model.IsImportData)
{
        @:_exportImportDataHelper = exportImportDataHelper;
        @:_fileManager = fileManager;
}
        _dataInterfaceService = dataInterfaceService;
        _cacheManager = cacheManager;
@if(Model.Type != 3)
{
        @:_controlParsing = controlParsing;
}
        _userManager = userManager;
@if(Model.DataRuleJson != null && Model.DataRuleJson != "[]")
{
        @:dataRuleList = dataRuleList.Where(x => x.UserOrigin.Equals(_userManager.UserOrigin)).ToList();
        @:dataRuleList.ForEach(x => x.conditionalModel = _repository.AsSugarClient().Utilities.JsonToConditionalModels(x.conditionalModelJson));
@foreach(var table in Model.AuxiliayTableRelations)
{
        @:@(table.LowerClassName)DataRule = dataRuleList.FirstOrDefault(x => x.TableName.Equals("@(table.OriginalTableName)"))?.conditionalModel;
}
}
    }
@foreach(var item in Model.Function)
{
@switch(item.FullName)
{
@*信息方法*@
case "info":
@:
    @:/// <summary>
    @:/// 获取@(Model.BusName).
    @:/// </summary>
    @:/// <param name="id">主键值.</param>
    @:/// <returns></returns>
    @:[HttpGet("{id}")]
    @:public async Task<dynamic> GetInfo(@(Model.PrimaryKeyPolicy == 1 ? "string" : Model.EnableFlow ? "string" : "long") id)
    @:{
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
        @:var output = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()")
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.Includes(x => x.@(table.ClassName))
}
            @:.FirstAsync(it => it.@(Model.EnableFlow && Model.PrimaryKeyPolicy == 2 ? "FlowTaskId" : Model.PrimaryKey).Equals(id));
@:
@*获取出副表数据*@
@foreach(var table in Model.AuxiliayTableRelations)
{
        @:var auxiliay@(table.ClassName) = output?.@(table.ClassName).Adapt<@(table.ClassName)InfoOutput>();
}
@:
        @:var data = output.Adapt<@(Model.ClassName)InfoOutput>();
@:
        @:if(output != null)
        @:{
@*合并*@
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:if (output.@(table.ClassName) != null)
                @:data = ConcurrentDictionaryExtensions.AssignmentObject<@(Model.ClassName)InfoOutput, @(table.ClassName)InfoOutput>(data, auxiliay@(table.ClassName)).Adapt<@(Model.ClassName)InfoOutput>();
}
@foreach(var column in Model.TableField)
{
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@switch(column.bpmKey)
{
case "modifyUser":
case "createUser":
@:
            @:// @column.ColumnComment
            @:data.@(parameterName) = await _repository.AsSugarClient().Queryable<UserEntity>().Where(it => it.Id.Equals(data.@(parameterName))).Select(it => SqlFunc.MergeString(it.RealName, "/", it.Account)).FirstAsync();
break;
case "currOrganize":
@:
            @:// @column.ColumnComment
            @:if(data.@(parameterName) != null)
            @:{
                @:var @(parameterName + @column.upperBpmKey) = data.@(parameterName).ToObject<List<string>>().LastOrDefault();
                @:data.@(parameterName) = await _repository.AsSugarClient().Queryable<OrganizeEntity>().Where(it => @(parameterName + @column.upperBpmKey).Equals(it.Id)).Select(it => it.FullName).FirstAsync();
            @:}
            @:if (data.@(parameterName).IsNullOrEmpty()) data.@(parameterName) = " ";
break;
case "currPosition":
@:
            @:// @column.ColumnComment
            @:data.@(parameterName) = await _repository.AsSugarClient().Queryable<PositionEntity>().Where(it => it.Id.Equals(data.@(parameterName))).Select(it => it.FullName).FirstAsync();
            @:if (data.@(parameterName).IsNullOrEmpty()) data.@(parameterName) = " ";
break;
}
}
        @:}
        @:else
        @:{
            @:data = new @(Model.ClassName)InfoOutput();
        @:}
        @:return data;
    @:}
break;
@*流程保存*@
case "save":
@:
    @:/// <summary>
    @:/// 保存.
    @:/// </summary>
    @:/// <param name="id">主键值.</param>
    @:/// <param name="input">表单数据.</param>
    @:/// <returns></returns>
    @:[HttpPost("{id}")]
    @:public async Task Save(string id, [FromBody] @(Model.ClassName)CrInput input)
    @:{
@*跨库*@
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
        @:var entity = input.Adapt<@(Model.ClassName)Entity>();
        @:entity.@(Model.PrimaryKeyPolicy == 1 ? Model.PrimaryKey : "FlowTaskId") = id;
        @:if (await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()." : "_repository.Is")AnyAsync(x => x.@(Model.PrimaryKeyPolicy == 1 ? Model.PrimaryKey : "FlowTaskId").Equals(id)@(Model.PrimaryKeyPolicy == 2 ? " || x." + Model.PrimaryKey + ".Equals(id)" : "")))
        @:{
@if(Model.PrimaryKeyPolicy  == 2)
{
            @:entity.@(Model.PrimaryKey) = (await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()." : "_repository.Get")FirstAsync(it=> it.FlowTaskId.Equals(entity.FlowTaskId)@(Model.PrimaryKeyPolicy == 2 ? " || it." + Model.PrimaryKey + ".Equals(entity.FlowTaskId)" : ""))).@(Model.PrimaryKey);
}
@{ GetAndModifyDataMethodTemplate(); }
        @:}
        @:else
        @:{
@{ GetTheNewDataMethodTemplate(); }
        @:}
    @:}
break;
@*新增*@
case "add":
@:
    @:/// <summary>
    @:/// 新建@(Model.BusName).
    @:/// </summary>
    @:/// <param name="input">参数.</param>
    @:/// <returns></returns>
    @:[HttpPost("")]
    @:[UnitOfWork]
    @:public async Task Create([FromBody] @(Model.ClassName)CrInput input)
    @:{
        @:input = CodeGenHelper.SetEmptyStringNull(input);
@*跨库*@
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
        @:var entity = input.Adapt<@(Model.ClassName)Entity>();
@if(Model.PrimaryKeyPolicy == 1)
{
        @:entity.@(Model.PrimaryKey) = SnowflakeIdHelper.NextId();
}
@{ GetTheNewDataMethodTemplate(); }
    @:}
break;
@*编辑*@
case "edit":
@:
    @:/// <summary>
    @:/// 更新@(Model.BusName).
    @:/// </summary>
    @:/// <param name="id">主键值.</param>
    @:/// <param name="input">参数.</param>
    @:/// <returns></returns>
    @:[HttpPut("{id}")]
    @:[UnitOfWork]
    @:public async Task Update(@(Model.PrimaryKeyPolicy == 1 ? "string" : "long") id, [FromBody] @(Model.ClassName)UpInput input)
    @:{
        @:input = CodeGenHelper.SetEmptyStringNull(input);
@*跨库*@
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
        @:var entity = input.Adapt<@(Model.ClassName)Entity>();
@{ GetAndModifyDataMethodTemplate(); }
    @:}
break;
@*分页列表*@
case "page":
@:
    @:/// <summary>
    @:/// 获取@(Model.BusName)列表.
    @:/// </summary>
    @:/// <param name="input">请求参数.</param>
    @:/// <returns></returns>
@if(@item.IsInterface)
{
    @:[HttpPost("List")]
}
    @:@(item.IsInterface ? "public" : "private") async Task<dynamic> GetList(@(item.IsInterface ? "[FromBody] " : "")@(Model.ClassName)ListQueryInput input)
    @:{
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
@if(Model.UseDataPermission)
{
        @:var authorizeWhere = new List<IConditionalModel>();
@foreach(var table in Model.AuxiliayTableRelations)
{
        @:var @(table.LowerClassName)AuthorizeWhere = new List<IConditionalModel>();
}
@:
        @:// 数据权限过滤
        @:if (_userManager.User.IsAdministrator == 0)
        @:{
            @:var allAuthorizeWhere = await _userManager.GetCodeGenAuthorizeModuleResource<@(Model.ClassName)ListOutput>(input.menuId, "@(Model.OriginalPrimaryKey)", @(Model.PrimaryKeyPolicy), _userManager.UserOrigin.Equals("pc") ? true : false);
            @:authorizeWhere = allAuthorizeWhere.Find(it => it.FieldRule == 0 || it.TableName.Equals("@(Model.OriginalMainTableName)"))?.conditionalModel;
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:@(table.LowerClassName)AuthorizeWhere = allAuthorizeWhere.Find(it => it.TableName.Equals("@(table.OriginalTableName)"))?.conditionalModel;
}
        @:}
@:
}
@if(Model.IsSearchMultiple || Model.HasSuperQuery)
{
        @:var entityInfo = @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").EntityMaintenance.GetEntityInfo<@(Model.ClassName)Entity>();
}
@if(Model.PcDefaultSortConfig)
{
        @:if (_userManager.UserOrigin.Equals("pc"))
        @:{
            @:input.sidx = CodeGenHelper.CodeGenDefaultSort(input.sidx, string.Empty, entityInfo, 0);
            @:input.sort = string.Empty;
        @:}
}
@if(Model.AppDefaultSortConfig)
{
        @:if (!_userManager.UserOrigin.Equals("pc"))
        @:{
            @:input.sidx = CodeGenHelper.CodeGenDefaultSort(input.sidx, string.Empty, entityInfo, 0);
            @:input.sort = string.Empty;
        @:}
}
@if(Model.HasSuperQuery)
{
        @:var superQuery = SuperQueryHelper.GetSuperQueryInput(input.superQueryJson, string.Empty, entityInfo, 0);
        @:List<IConditionalModel> mainConditionalModel = SuperQueryHelper.GetSuperQueryJson(superQuery);
@foreach(var table in Model.AuxiliayTableRelations)
{
@:
        @:entityInfo = @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").EntityMaintenance.GetEntityInfo<@(table.ClassName)Entity>();
        @:superQuery = SuperQueryHelper.GetSuperQueryInput(input.superQueryJson, entityInfo.DbTableName, entityInfo, 2);
        @:List<IConditionalModel> @(table.LowerClassName)ConditionalModel = SuperQueryHelper.GetSuperQueryJson(superQuery);
}
}
@{ GetListQueryFieldTemplate(); }
        @:var data = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()")
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.Includes(it => it.@(table.ClassName))
}
@{ GetTheListQueryCriteriaTemplate();}
@if(Model.HasSuperQuery)
{
            @:.Where(mainConditionalModel)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.WhereIF(@(table.LowerClassName)ConditionalModel?.Count > 0, it => SqlFunc.Exists(it.@(table.ClassName).@(table.PrimaryKey), @(table.LowerClassName)ConditionalModel))
}
}
@if(Model.UseDataPermission)
{
            @:.Where(authorizeWhere)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.WhereIF(@(table.LowerClassName)AuthorizeWhere?.Count > 0, it => SqlFunc.Exists(it.@(table.ClassName).@(table.PrimaryKey), @(table.LowerClassName)AuthorizeWhere))
}
}
@if(Model.DataRuleJson != null && Model.DataRuleJson != "[]")
{
            @:.Where(dataRuleList.FirstOrDefault(x => x.FieldRule.Equals(0))?.conditionalModel)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.WhereIF(@(table.LowerClassName)DataRule !=null , it => SqlFunc.Exists(it.@(table.ClassName).@(table.TableField), @(table.LowerClassName)DataRule))
}
}
@if(Model.IsLogicalDelete)
{
            @:.Where(it => it.DeleteMark == null)
}
            @:.OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.@(Model.PrimaryKey)).OrderByIF(!string.IsNullOrEmpty(input.sidx), input.sidx + " " + input.sort)
            @:.Select(it => new @(Model.ClassName)ListOutput
            @:{
@{ GetTheListDisplayFieldTemplate();}
            @:})
            @:.ToPagedListAsync(input.currentPage, input.pageSize);
@if(Model.IsConversion)
{
@:
        @:await _repository.AsSugarClient().ThenMapperAsync(data.list, async item =>
        @:{
            @:var linkageParameters = new List<DataInterfaceParameter>();
@{ GetListDataConversionTemplate(); }
        @:});
@:
}
@if(Model.ParsBpmKeyConstList.Count > 0)
{
@:
        @:var resData = data.list.ToObject<List<Dictionary<string, object>>>(CommonConst.options);
        @:List<FieldsModel> fieldList = new List<FieldsModel>();
        @:fieldList.AddRange(ExportImportDataHelper.GetDataConversionTemplateParsing<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity()));
@:
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsControlParsing)
{
        @:fieldList.AddRange(ExportImportDataHelper.GetDataConversionTemplateParsing<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), "@(table.OriginalTableName)"));
@:
}
}
@foreach (var bpmKeyConst in Model.ParsBpmKeyConstList)
{
        @:resData = await _controlParsing.GetParsDataList(resData, "@(bpmKeyConst[1])", "@(bpmKeyConst[0])", _userManager.TenantId, fieldList);
}
@:
        @:data.list = resData.ToObject<List<@(Model.ClassName)ListOutput>>(CommonConst.options);
}
@if(Model.TableType == 3)
{
        @:return _userManager.UserOrigin.Equals("pc") ? CodeGenHelper.GetGroupList(data.list.ToJsonStringOld().ToObjectOld<List<Dictionary<string, object>>>(), "@(Model.GroupField)", "@(Model.GroupShowField)") : PageResult<@(Model.ClassName)ListOutput>.SqlSugarPageResult(data);
}else{
        @:return PageResult<@(Model.ClassName)ListOutput>.SqlSugarPageResult(data);
}
    @:}
break;
case "noPage":
@:
    @:/// <summary>
    @:/// 获取@(Model.BusName)无分页列表.
    @:/// </summary>
    @:/// <param name="input">请求参数.</param>
    @:/// <returns></returns>
@if(@item.IsInterface)
{
    @:[HttpPost("@(Model.IsTreeTable ? "Tree" : "")List")]
}
    @:@(item.IsInterface ? "public" : "private") async Task<dynamic> GetNoPagingList(@(item.IsInterface ? "[FromBody] " : "")@(Model.ClassName)ListQueryInput input)
    @:{
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
@if(Model.UseDataPermission)
{
        @:var authorizeWhere = new List<IConditionalModel>();
@foreach(var table in Model.AuxiliayTableRelations)
{
        @:var @(table.LowerClassName)AuthorizeWhere = new List<IConditionalModel>();
}
@:
        @:// 数据权限过滤
        @:if (_userManager.User.IsAdministrator == 0)
        @:{
            @:var allAuthorizeWhere = await _userManager.GetCodeGenAuthorizeModuleResource<@(Model.ClassName)ListOutput>(input.menuId, "@(Model.OriginalPrimaryKey)", @(Model.PrimaryKeyPolicy), _userManager.UserOrigin.Equals("pc") ? true : false);
            @:authorizeWhere = allAuthorizeWhere.Find(it => it.FieldRule == 0 || it.TableName.Equals("@(Model.OriginalMainTableName)"))?.conditionalModel;
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:@(table.LowerClassName)AuthorizeWhere = allAuthorizeWhere.Find(it => it.TableName.Equals("@(table.OriginalTableName)"))?.conditionalModel;
}
        @:}
@:
}
@if(Model.IsSearchMultiple || Model.HasSuperQuery)
{
        @:var entityInfo = @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").EntityMaintenance.GetEntityInfo<@(Model.ClassName)Entity>();
}
@if(Model.PcDefaultSortConfig)
{
        @:if (_userManager.UserOrigin.Equals("pc"))
        @:{
            @:input.sidx = CodeGenHelper.CodeGenDefaultSort(input.sidx, string.Empty, entityInfo, 0);
            @:input.sort = string.Empty;
        @:}
}
@if(Model.AppDefaultSortConfig)
{
        @:if (!_userManager.UserOrigin.Equals("pc"))
        @:{
            @:input.sidx = CodeGenHelper.CodeGenDefaultSort(input.sidx, string.Empty, entityInfo, 0);
            @:input.sort = string.Empty;
        @:}
}
@if(Model.HasSuperQuery)
{
        @:var superQuery = SuperQueryHelper.GetSuperQueryInput(input.superQueryJson, string.Empty, entityInfo, 0);
        @:List<IConditionalModel> mainConditionalModel = SuperQueryHelper.GetSuperQueryJson(superQuery);
@foreach(var table in Model.AuxiliayTableRelations)
{
@:
        @:entityInfo = @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").EntityMaintenance.GetEntityInfo<@(table.ClassName)Entity>();
        @:superQuery = SuperQueryHelper.GetSuperQueryInput(input.superQueryJson, entityInfo.DbTableName, entityInfo, 2);
        @:List<IConditionalModel> @(table.LowerClassName)ConditionalModel = SuperQueryHelper.GetSuperQueryJson(superQuery);
}
}
@{ GetListQueryFieldTemplate(); }
        @:var data = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()")
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.Includes(it => it.@(table.ClassName))
}
@{ GetTheListQueryCriteriaTemplate();}
@if(Model.HasSuperQuery)
{
            @:.Where(mainConditionalModel)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.WhereIF(@(table.LowerClassName)ConditionalModel?.Count > 0, it => SqlFunc.Exists(it.@(table.ClassName).@(table.PrimaryKey), @(table.LowerClassName)ConditionalModel))
}
}
@if(Model.UseDataPermission)
{
            @:.Where(authorizeWhere)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.WhereIF(@(table.LowerClassName)AuthorizeWhere?.Count > 0, it => SqlFunc.Exists(it.@(table.ClassName).@(table.PrimaryKey), @(table.LowerClassName)AuthorizeWhere))
}
}
@if(Model.DataRuleJson != null && Model.DataRuleJson != "[]")
{
            @:.Where(dataRuleList.FirstOrDefault(x => x.FieldRule.Equals(0))?.conditionalModel)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.WhereIF(@(table.LowerClassName)DataRule != null, it => SqlFunc.Exists(it.@(table.ClassName).@(table.TableField), @(table.LowerClassName)DataRule))
}
}
@if(Model.IsLogicalDelete)
{
            @:.Where(it => it.DeleteMark == null)
}
            @:.OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.@(Model.PrimaryKey)).OrderByIF(!string.IsNullOrEmpty(input.sidx), input.sidx + " " + input.sort)
            @:.Select(it => new @(Model.ClassName)ListOutput
            @:{
@{ GetTheListDisplayFieldTemplate();}
            @:})
            @:.ToListAsync();
@if(Model.IsConversion)
{
@:
        @:await _repository.AsSugarClient().ThenMapperAsync(data, async item =>
        @:{
            @:var linkageParameters = new List<DataInterfaceParameter>();
@{ GetListDataConversionTemplate(); }
        @:});
@:
}
@if(Model.IsTreeTable || Model.ParsBpmKeyConstList.Count > 0)
{
@:
        @:var resData = data.ToObject<List<Dictionary<string, object>>>(CommonConst.options);
}
@if(Model.ParsBpmKeyConstList.Count > 0)
{
        @:List<FieldsModel> fieldList = new List<FieldsModel>();
        @:fieldList.AddRange(ExportImportDataHelper.GetDataConversionTemplateParsing<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity()));
@:
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsControlParsing)
{
        @:fieldList.AddRange(ExportImportDataHelper.GetDataConversionTemplateParsing<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), "@(table.OriginalTableName)"));
@:
}
}
@foreach (var bpmKeyConst in Model.ParsBpmKeyConstList)
{
        @:resData = await _controlParsing.GetParsDataList(resData, "@(bpmKeyConst[1])", "@(bpmKeyConst[0])", _userManager.TenantId, fieldList);
}
@:
}
@if(Model.IsTreeTable)
{
        @:if (_userManager.UserOrigin.Equals("pc"))
        @:{
            @:resData = CodeGenHelper.GetTreeList(resData, "@(Model.ParentField)", "@(Model.TreeShowField)");
        @:}
@:
}
@if(Model.IsTreeTable || Model.ParsBpmKeyConstList.Count > 0)
{
        @:data = resData.ToObject<List<@(Model.ClassName)ListOutput>>(CommonConst.options);
}
@if(Model.TableType == 3)
{        
        @:return _userManager.UserOrigin.Equals("pc") ? CodeGenHelper.GetGroupList(data.ToJsonStringOld().ToObjectOld<List<Dictionary<string, object>>>(), "@(Model.GroupField)", "@(Model.GroupShowField)") : data;
}else{
        @:return data;
}
    @:}
break;
@*删除*@
case "remove":
@:
    @:/// <summary>
    @:/// 删除@(Model.BusName).
    @:/// </summary>
    @:/// <returns></returns>
    @:[HttpDelete("{id}")]
    @:[UnitOfWork]
    @:public async Task Delete(@(Model.PrimaryKeyPolicy == 1 ? "string" : "long") id)
    @:{
@*跨库*@
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
        @:if(!await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()").AnyAsync(it => it.@(Model.PrimaryKey) == id@(Model.IsLogicalDelete ? "  && it.DeleteMark == null" : "")))
            @:throw Oops.Oh(ErrorCode.COM1005);
@:
@if(Model.IsLogicalDelete)
{
        @:try
        @:{
            @:var entity = await _repository.AsQueryable().FirstAsync(it => it.@(Model.PrimaryKey) == id && it.DeleteMark == null);
@:
            @:await _repository.AsDeleteable().Where(it => it.@(Model.PrimaryKey) == id).IsLogic().ExecuteCommandAsync("F_Delete_Mark",1, "F_DELETE_TIME", "F_DELETE_USER_ID", _userManager.UserId);        
        @:}
        @:catch (Exception)
        @:{
            @:throw Oops.Oh(ErrorCode.COM1002);
        @:}
}else{
        @:var isOk = await @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").DeleteNav<@(Model.ClassName)Entity>(it => it.@(Model.PrimaryKey) == id)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.Include(it => it.@(table.ClassName))
}
            @:.ExecuteCommandAsync();
        @:if (!isOk)
            @:throw Oops.Oh(ErrorCode.COM1002);
}
    @:}
break;
@*批量删除*@
case "batchRemove":
@:
    @:/// <summary>
    @:/// 批量删除@(Model.BusName).
    @:/// </summary>
    @:/// <param name="ids">主键数组.</param>
    @:/// <returns></returns>
    @:[HttpPost("batchRemove")]
    @:[UnitOfWork]
    @:public async Task BatchRemove([FromBody] List<@(Model.PrimaryKeyPolicy == 1 ? "string" : "long")> ids)
    @:{
@*跨库*@
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
@if(Model.EnableFlow)
{
        @:var idList = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()").Where(it => ids.Contains(it.@(Model.PrimaryKey))@(Model.IsLogicalDelete ? "  && it.DeleteMark == null" : "")).Select(it => it.@(Model.PrimaryKeyPolicy  == 1 ? Model.PrimaryKey : "FlowTaskId")).ToListAsync();
@:
        @:// 取差集
        @:idList = idList.Except(await GetAllowDeleteFlowTaskList(idList)).ToList();
@:
        @:var entitys = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()").In(it => it.@(Model.PrimaryKeyPolicy  == 1 ? @Model.PrimaryKey : "FlowTaskId"), idList)@(Model.IsLogicalDelete ? ".Where(it => it.DeleteMark == null)" : "").ToListAsync();
}else{
        @:var entitys = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()").In(it => it.@(Model.PrimaryKey), ids)@(Model.IsLogicalDelete ? ".Where(it => it.DeleteMark == null)" : "").ToListAsync();
}
@:
        @:if (entitys.Count > 0)
        @:{
@if(Model.IsLogicalDelete)
{
            @:try
            @:{
                @:await _repository.AsDeleteable().Where(it => @(Model.EnableFlow ? "entitys.Select(s => s." + Model.PrimaryKey + ").ToList()" : "ids").Contains(it.Id)).IsLogic().ExecuteCommandAsync("F_Delete_Mark",1, "F_DELETE_TIME", "F_DELETE_USER_ID", _userManager.UserId);
}else{
            @:@(!Model.EnableFlow ? "var isOk = " : "")await @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").DeleteNav<@(Model.ClassName)Entity>(it => @(Model.EnableFlow ? "entitys.Select(s => s." + Model.PrimaryKey + ").ToList()" : "ids").Contains(it.@(Model.PrimaryKey)))
@foreach(var table in Model.AuxiliayTableRelations)
{
                @:.Include(it => it.@(table.ClassName))
}
                @:.ExecuteCommandAsync();
@if(!Model.EnableFlow)
{
            @:if (!isOk)
                @:throw Oops.Oh(ErrorCode.COM1002);
}
}
@if(Model.EnableFlow)
{
@:
            @:await _repository.AsSugarClient().Updateable<FlowTaskEntity>().SetColumns(it => new FlowTaskEntity()
            @:{
                @:DeleteMark = 1,
                @:DeleteUserId = _userManager.UserId,
                @:DeleteTime = SqlFunc.GetDate()
            @:}).Where(it => idList.Contains(it.Id)).ExecuteCommandAsync();
}
if(Model.IsLogicalDelete)
{
            @:}
            @:catch (Exception)
            @:{
                @:throw Oops.Oh(ErrorCode.COM1002);
            @:}
}
        @:}
    @:}
@if(Model.EnableFlow)
{
@:
    @:/// <summary>
    @:/// 获取不允许删除任务列表.
    @:/// </summary>
    @:/// <param name="ids">id数组</param>
    @:/// <returns></returns>
    @:private async Task<List<string>> GetAllowDeleteFlowTaskList(List<string> ids)
    @:{
        @:return await _repository.AsSugarClient().Queryable<FlowTaskEntity>().Where(it => ids.Contains(it.ProcessId)).Where(it=> it.Status != 4 && it.Status != 7).Select(f => f.ProcessId).ToListAsync();
    @:}
}
break;
@*导出*@
case "download":
@:
    @:/// <summary>
    @:/// 导出@(Model.BusName).
    @:/// </summary>
    @:/// <param name="input">请求参数.</param>
    @:/// <returns></returns>
    @:[HttpPost("Actions/Export")]
    @:public async Task<dynamic> Export([FromBody] @(Model.ClassName)ListQueryInput input)
    @:{
@if(Model.TableType == 3)
{
        @:var exportData = new List<Dictionary<string, object>>();
        @:if (input.dataType == 0)
            @:exportData = await GetList(input);
        @:else
            @:exportData = await GetNoPagingList(input);
}else{
        @:var exportData = new List<@(Model.ClassName)ListOutput>();
        @:if (input.dataType == 0)
            @:exportData = Clay.Object(await GetList(input)).Solidify<PageResult<@(Model.ClassName)ListOutput>>().list;
        @:else
            @:exportData = await GetNoPagingList(input);
}
        @:var excelName = string.Format("{0}_{1:yyyyMMddHHmmss}", "@(Model.FullName)", DateTime.Now);
        @:_cacheManager.Set(excelName + ".xls", string.Empty);
        @:return ExportImportDataHelper.GetDataExport(excelName, input.selectKey, _userManager.UserId, exportData.ToJsonString().ToObjectOld<List<Dictionary<string, object>>>(), paramList, @((Model.TableType == 3).ToString().ToLower()));
    @:}
break;
@*导入*@
case "upload":
@:
    @:/// <summary>
    @:/// 下载模板.
    @:/// </summary>
    @:/// <returns></returns>
    @:[HttpGet("TemplateDownload")]
    @:public async Task<dynamic> TemplateDownload()
    @:{
        @:List<Dictionary<string, object>>? dataList = new List<Dictionary<string, object>>();
@:
        @:// 赋予默认值
        @:var dicItem = ExportImportDataHelper.GetTemplateHeader<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity(), 1);
@:
@foreach(var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:var @(table.LowerClassName) = ExportImportDataHelper.GetTemplateHeader<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), 2, "@(table.OriginalTableName)");
}
}
        @:dicItem.Add("id", "id");
@{var tableName = 0;}
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:var table@(table.TableNo) = @(tableName == 0 ? "dicItem" : "table" + tableName).Concat(@(table.LowerClassName)).ToDictionary(k => k.Key, v => v.Value);
tableName++;
if(tableName > 0)
{
tableName = table.TableNo;
}
}
}
        @:dataList.Add(@(tableName > 0 ? "table"+ tableName : "dicItem"));
@:
        @:var excelName = string.Format("{0} 导入模板_{1}", "@(Model.FullName)", SnowflakeIdHelper.NextId());
        @:_cacheManager.Set(excelName + ".xls", string.Empty);
        @:return ExportImportDataHelper.GetTemplateExport(excelName, string.Join(",", uploaderKey), _userManager.UserId, dataList, null);
    @:}
@:
    @:/// <summary>
    @:/// Excel导入.
    @:/// </summary>
    @:/// <param name="file"></param>
    @:/// <returns></returns>
    @:[HttpPost("Uploader")]
    @:public async Task<dynamic> Uploader(IFormFile file)
    @:{
        @:var _filePath = _fileManager.GetPathByType(string.Empty);
        @:var _fileName = DateTime.Now.ToString("yyyyMMdd") + "_" + SnowflakeIdHelper.NextId() + Path.GetExtension(file.FileName);
        @:var stream = file.OpenReadStream();
        @:await _fileManager.UploadFileByType(stream, _filePath, _fileName);
        @:return new { name = _fileName, url = string.Format("/api/File/Image/{0}/{1}", string.Empty, _fileName) };
    @:}
@:
    @:/// <summary>
    @:/// 导入预览.
    @:/// </summary>
    @:/// <returns></returns>
    @:[HttpGet("ImportPreview")]
    @:public async Task<dynamic> ImportPreview(string fileName)
    @:{
        @:List<FieldsModel> fieldList = new List<FieldsModel>();
        @:fieldList.AddRange(ExportImportDataHelper.GetTemplateParsing<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity()));
@:
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:fieldList.AddRange(ExportImportDataHelper.GetTemplateParsing<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), "@(table.OriginalTableName)"));
@:
}
}
        @:var entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(Model.ClassName)Entity>();
        @:List<DbTableRelationModel> tables = new List<DbTableRelationModel>() { ExportImportDataHelper.GetTableRelation(entityInfo, "1") };
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(table.ClassName)Entity>();
        @:tables.Add(ExportImportDataHelper.GetTableRelation(entityInfo, "0", "@(table.OriginalTableField)", "@(table.RelationTable)", "@(table.OriginalRelationField)"));
@:
}
}
@if(Model.DbLinkId != "0")
{
        @:DbLinkEntity link = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
}else{
        @:DbLinkEntity link = _dataBaseManager.GetTenantDbLink(_userManager.TenantId, _userManager.TenantDbName); // 当前数据库连接
}
        @:var tInfo = new TemplateParsingBase(link, fieldList, tables, "@(Model.OriginalPrimaryKey)", @(Model.WebType), @(Model.PrimaryKeyPolicy), uploaderKey.ToList(), "@(Model.ImportDataType)"@(Model.EnableFlow ? ", 1, \"" + Model.FormId + "\"" : ""));
        @:return await _exportImportDataHelper.GetImportPreviewData(tInfo, fileName);
    @:}
@:
    @:/// <summary>
    @:/// 导入数据.
    @:/// </summary>
    @:/// <param name="input"></param>
    @:/// <returns></returns>
    @:[HttpPost("ImportData")]
    @:[UnitOfWork]
    @:public async Task<dynamic> ImportData([FromBody] DataImportInput input)
    @:{
        @:List<FieldsModel> fieldList = new List<FieldsModel>();
        @:fieldList.AddRange(ExportImportDataHelper.GetTemplateParsing<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity()));
@:
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:fieldList.AddRange(ExportImportDataHelper.GetTemplateParsing<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), "@(table.OriginalTableName)"));
@:
}
}
        @:var entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(Model.ClassName)Entity>();
        @:List<DbTableRelationModel> tables = new List<DbTableRelationModel>() { ExportImportDataHelper.GetTableRelation(entityInfo, "1") };
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(table.ClassName)Entity>();
        @:tables.Add(ExportImportDataHelper.GetTableRelation(entityInfo, "0", "@(table.OriginalTableField)", "@(table.RelationTable)", "@(table.OriginalRelationField)"));
@:
}
}
@if(Model.DbLinkId != "0")
{
        @:DbLinkEntity link = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
}else{
        @:DbLinkEntity link = _dataBaseManager.GetTenantDbLink(_userManager.TenantId, _userManager.TenantDbName); // 当前数据库连接
}
        @:var tInfo = new TemplateParsingBase(link, fieldList, tables, "@(Model.OriginalPrimaryKey)", @(Model.WebType), @(Model.PrimaryKeyPolicy), uploaderKey.ToList(), "@(Model.ImportDataType)"@(Model.EnableFlow ? ", 1, \"" + Model.FormId + "\"" : ""));
@:
        @:object[]? res = await _exportImportDataHelper.ImportMenuData(tInfo, input @(Model.EnableFlow ? ", \"" + Model.FormId + "\"" : ""));
        @:var addlist = res.First() as List<Dictionary<string, object>>;
        @:var errorlist = res.Last() as List<Dictionary<string, object>>;
        @:var result = new DataImportOutput()
        @:{
            @:snum = addlist.Count,
            @:fnum = errorlist.Count,
            @:failResult = errorlist,
            @:resultType = errorlist.Count < 1 ? 0 : 1
        @:};
@:
        @:return result;
    @:}
@:
    @:/// <summary>
    @:/// 导入数据的错误报告.
    @:/// </summary>
    @:/// <param name="list"></param>
    @:/// <returns></returns>
    @:[HttpPost("ImportExceptionData")]
    @:[UnitOfWork]
    @:public async Task<dynamic> ExportExceptionData([FromBody] DataImportInput list)
    @:{
        @:List<FieldsModel> fieldList = new List<FieldsModel>();
        @:fieldList.AddRange(ExportImportDataHelper.GetTemplateParsing<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity()));
@:
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:fieldList.AddRange(ExportImportDataHelper.GetTemplateParsing<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), "@(table.OriginalTableName)"));
@:
}
}
        @:var entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(Model.ClassName)Entity>();
        @:List<DbTableRelationModel> tables = new List<DbTableRelationModel>() { ExportImportDataHelper.GetTableRelation(entityInfo, "1") };
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsImportData)
{
        @:entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(table.ClassName)Entity>();
        @:tables.Add(ExportImportDataHelper.GetTableRelation(entityInfo, "0", "@(table.OriginalTableField)", "@(table.RelationTable)", "@(table.OriginalRelationField)"));
@:
}
}
@if(Model.DbLinkId != "0")
{
        @:DbLinkEntity link = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
}else{
        @:DbLinkEntity link = _dataBaseManager.GetTenantDbLink(_userManager.TenantId, _userManager.TenantDbName); // 当前数据库连接
}
        @:var tInfo = new TemplateParsingBase(link, fieldList, tables, "@(Model.OriginalPrimaryKey)", @(Model.WebType), @(Model.PrimaryKeyPolicy), uploaderKey.ToList(), "@(Model.ImportDataType)");
        @:tInfo.FullName = "@(Model.FullName)";
@:
        @:// 错误数据
        @:tInfo.selectKey.Add("errorsInfo");
        @:tInfo.AllFieldsModel.Add(new FieldsModel() { __vModel__ = "errorsInfo", __config__ = new ConfigModel() { label = "异常原因" } });
        @:for (var i = 0; i < list.list.Count(); i++) list.list[i].Add("id", i);
@:
        @:var result = ExportImportDataHelper.GetCreateFirstColumnsHeader(tInfo.selectKey, list.list, paramList);
        @:var firstColumns = result.Item1;
        @:var resultList = result.Item2;
        @:_cacheManager.Set(string.Format("{0} 导入错误报告.xls", tInfo.FullName), string.Empty);
        @:return firstColumns.Any()
            @:? await _exportImportDataHelper.ExcelCreateModel(tInfo, resultList, string.Format("{0} 导入错误报告", tInfo.FullName), firstColumns)
            @:: await _exportImportDataHelper.ExcelCreateModel(tInfo, resultList, string.Format("{0} 导入错误报告", tInfo.FullName));
    @:}
break;
@*详情*@
case "detail":
@:
    @:/// <summary>
    @:/// @(Model.BusName)详情.
    @:/// </summary>
    @:/// <param name="id">主键值.</param>
    @:/// <returns></returns>
    @:[HttpGet("Detail/{id}")]
    @:[UnifySerializerSetting("special")]
    @:public async Task<dynamic> GetDetails(@(Model.PrimaryKeyPolicy == 1 ? "string" : "long") id)
    @:{
@if(Model.DbLinkId != "0")
{
        @:var dbLink = await _repository.AsSugarClient().Queryable<DbLinkEntity>().FirstAsync(it => it.Id.Equals("@(Model.DbLinkId)"));
        @:_sqlSugarClient = _dataBaseManager.ChangeDataBase(dbLink);
@:
}
        @:var data = await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()" : "_repository.AsQueryable()")
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.Includes(x => x.@(table.ClassName))
}
            @:.Where(it => it.@(Model.PrimaryKey) == id)
            @:.ToListAsync(it => new @(Model.ClassName)DetailOutput
            @:{
@*循环关联字段*@
@foreach (var column in Model.RelationsField)
{
                @:@(column.RelationColumnName) = <EMAIL>,
}
@*循环展示字段*@
@foreach (var column in Model.TableField){
@{var parameterName = string.Empty;}
@{var parameterValue = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
@{parameterValue = "it." + @column.ClassName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
@{parameterValue = "it";}
break;
}
@if (column.PrimaryKey){
                @:@(column.LowerColumnName) = <EMAIL>,
}else if(column.bpmKey != null){
@switch(column.bpmKey)
{
case "uploadFile":
case "uploadImg":
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
break;
case "slider":
                @:@(parameterName) = SqlFunc.ToInt32(@(parameterValue).@(column.ColumnName)),
break;
case "switch":
                @:@(parameterName) = SqlFunc.IIF(@(parameterValue).@(column.ColumnName) == 1, "@(column.ActiveTxt)", "@(column.InactiveTxt)"),
break;
case "datePicker":
                @:@(parameterName) = @(parameterValue).@(column.ColumnName).Value.ToString("@(column.Format)"),
break;
case "createTime":
case "modifyTime":
                @:@(parameterName) = @(parameterValue).@(column.ColumnName).Value.ToString("yyyy-MM-dd HH:mm:ss"),
break;
case "modifyUser":
case "createUser":
                @:@(parameterName) = SqlFunc.Subqueryable<UserEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_USER\")" : "").Where(u => u.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(u => SqlFunc.MergeString(u.RealName, "/", u.Account)),
break;
case "currPosition":
                @:@(parameterName) = SqlFunc.Subqueryable<PositionEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_POSITION\")" : "").Where(p => p.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(p => p.FullName),
break;
case "userSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<UserEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_USER\")" : "").Where(u => u.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(u => u.RealName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
}
break;
case "posSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<PositionEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_POSITION\")" : "").Where(p => p.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(p => p.FullName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
}
break;
case "depSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<OrganizeEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_ORGANIZE\")" : "").Where(o => o.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(o => o.FullName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
}
break;
case "roleSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<RoleEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_ROLE\")" : "").Where(r => r.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(r => r.FullName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
}
break;
case "groupSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<GroupEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_GROUP\")" : "").Where(g => g.Id.Equals(@(parameterValue).@(column.ColumnName))).Select(g => g.FullName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
}
break;
case "radio":
@switch(column.ControlsDataType)
{
case "dictionary":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<DictionaryDataEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_DICTIONARYDATA\")" : "").Where(dic => dic.@(column.Value == "id" ? "Id" : "EnCode").Equals(@(parameterValue).@(column.ColumnName)) && dic.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(dic => dic.FullName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName)@(column.DataType == "int" ? ".ToString()" : ""),
}
break;
default:
                @:@(parameterName) = @(parameterValue).@(column.ColumnName)@(column.DataType == "int" ? ".ToString()" : ""),
break;
}
break;
case "select":
case "treeSelect":
@switch(column.ControlsDataType)
{
case "dictionary":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<DictionaryDataEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_DICTIONARYDATA\")" : "").Where(dic => dic.@(column.Value == "id" ? "Id" : "EnCode").Equals(@(parameterValue).@(column.ColumnName)) && dic.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(dic => dic.FullName),
}else{
                @:@(parameterName) = @(parameterValue).@(column.ColumnName),
}
break;
default:
                @:@(parameterName) = @(parameterValue).@(column.ColumnName)@(column.NetType == "int" ? ".ToString()" : ""),
break;
}
break;
default:
                @:@(parameterName) = @(parameterValue).@(column.ColumnName)@(column.NetType == "int" ? ".ToString()" : ""),
break;
}
}
}
            @:});
@if(Model.IsDetailConversion)
{
@:
        @:await _repository.AsSugarClient().ThenMapperAsync(data, async item =>
        @:{
            @:var linkageParameters = new List<DataInterfaceParameter>();
@foreach (var column in Model.TableField)
{
@{var dataCount = column.StaticData != null ? column.StaticData.Count : 0;}
@*参数名*@
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@if(column.IsDetailConversion)
{
@switch(column.bpmKey)
{
case "uploadFile":
case "uploadImg":
            @:if(item.@(parameterName) != null)
            @:{
                @:item.@(parameterName) = item.@(parameterName).ToString().ToObject<List<FileControlsModel>>();
            @:}
            @:else
            @:{
                @:item.@(parameterName) = new List<FileControlsModel>();
            @:}
@:
break;
case "select":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => item.@(parameterName).Equals(it.id))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(s => s.fullName));
}
            @:}
break;
case "dictionary":
@if(column.IsMultiple)
{
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
            @:}
}
break;
case "dynamic":
            @:if(item.@(parameterName) != null)
            @:{
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
                @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", ""@(column.IsLinkage ? ", linkageParameters" : ""));
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
}
@:
break;
case "checkbox":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
            @:}
break;
case "dictionary":
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
            @:}
break;
case "dynamic":
            @:if(item.@(parameterName) != null)
            @:{
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
                @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", ""@(column.IsLinkage ? ", linkageParameters" : ""));
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
            @:}
break;
}
@:
break;
case "radio":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => item.@(parameterName).Equals(it.id))?.fullName;
            @:}
break;
case "dynamic":
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName);
            @:}
break;
}
@:
break;
case "cascader":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
        @if(column.ShowAllLevels){
                @:item.@(parameterName) = string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
        }else{
                @:item.@(parameterName) = @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName).FirstOrDefault();
        }
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
        @if(column.ShowAllLevels){
                    @:@(parameterName)Excessive.Add(string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName)));
        }else{
                    @:@(parameterName)Excessive.Add(@(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName).FirstOrDefault());
        }
                @:}
@:
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
break;
case "dictionary":
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
        @if(column.ShowAllLevels){
                @:item.@(parameterName) = string.Join("@(column.Separator)", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
        }else{
                @:item.@(parameterName) = await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).FirstOrDefault();
        }
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
        @if(column.ShowAllLevels){
                    @:@(parameterName)Excessive.Add(string.Join("@(column.Separator)", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(parameterName).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync()));
        }else{
                    @:@(parameterName)Excessive.Add(await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(parameterName).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).FirstOrDefault());
        }
                @:}
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
break;
case "dynamic":
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
        @if(column.ShowAllLevels){
                @:item.@(parameterName) = string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
        }else{
                @:item.@(parameterName) = @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName).FirstOrDefault();
        }
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
        @if(column.ShowAllLevels){
                    @:@(parameterName)Excessive.Add(string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName)));
        }else{
                    @:@(parameterName)Excessive.Add(@(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName).FirstOrDefault());
        }
                @:}
@:
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
break;
}
@:
break;
case "organizeSelect":
            @:// @column.ColumnComment
            @:item.@(parameterName) = _controlParsing.GetOrganizeName(@(column.IsMultiple.ToString().ToLower()),item.@(parameterName));
@:
break;
case "currOrganize":

            @:// @column.ColumnComment
            @:item.@(parameterName) = _controlParsing.GetCurrOrganizeName("@(column.ShowLevel)",item.@(parameterName));
break;
case "depSelect":
            @:// @column.ColumnComment
            @:item.@(parameterName) = _controlParsing.GetDepartmentName(@(column.IsMultiple.ToString().ToLower()),item.@(parameterName));
@:
break;
case "posSelect":
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<PositionEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
            @:}
@:
break;
case "userSelect":
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<UserEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.RealName).ToListAsync());
            @:}
@:
break;
case "roleSelect":
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<RoleEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
            @:}
@:
break;
case "groupSelect":
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<GroupEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
            @:}
@:
break;
case "treeSelect":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
case "dictionary":
@if(column.IsMultiple)
{
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
            @:}
}
break;
case "dynamic":
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
}
@:
break;
case "areaSelect":
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join("/", await _repository.AsSugarClient().Queryable<ProvinceEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
                    @:@(parameterName)Excessive.Add(string.Join("/", await _repository.AsSugarClient().Queryable<ProvinceEntity>().Where(it => @(parameterName).Contains(it.Id)).Select(it => it.FullName).ToListAsync()));
                @:}
@:
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
@:
break;
case "popupTableSelect":
            @:// @column.ColumnComment
@if(column.IsLinkage)
{
            @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
            @:linkageParameters.Add(new DataInterfaceParameter
            @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
            @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
@:
break;
}
}
}
        @:});
@:
}
@if(Model.ParsBpmKeyConstListDetails.Count > 0)
{
        @:var resData = data.ToJsonStringOld().ToObjectOld<List<Dictionary<string, object>>>();
        @:List<FieldsModel> fieldList = new List<FieldsModel>();
        @:fieldList.AddRange(ExportImportDataHelper.GetDataConversionTemplateParsing<@(Model.ClassName)Entity>(new @(Model.ClassName)Entity()));
@:
@foreach (var table in Model.AuxiliayTableRelations)
{
@if(table.IsControlParsing)
{
        @:fieldList.AddRange(ExportImportDataHelper.GetDataConversionTemplateParsing<@(table.ClassName)Entity>(new @(table.ClassName)Entity(), "@(table.OriginalTableName)"));
@:
}
}
@:
@foreach (var bpmKeyConst in Model.ParsBpmKeyConstListDetails)
{
        @:resData = await _controlParsing.GetParsDataList(resData, "@(bpmKeyConst[1])", "@(bpmKeyConst[0])", _userManager.TenantId, fieldList);
}
@:
        @:return resData.FirstOrDefault();
}else{
        @:return data.FirstOrDefault();
}
    @:}
break;
}
}
}
@{
@*获取列表数据转换模板*@
    void GetListDataConversionTemplate()
    {
@foreach (var column in Model.TableField)
{
@{var dataCount = column.StaticData != null ? column.StaticData.Count : 0;}
@*参数名*@
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@if(column.IsConversion && column.IsShow)
{
@switch(column.bpmKey)
{
case "location":
            @:if(item.@(parameterName).IsNotEmptyOrNull()) item.@(parameterName) = item.@(parameterName).ToObject<Dictionary<string, string>>()["fullAddress"];
@:
break;
case "uploadFile":
case "uploadImg":
            @:if(item.@(parameterName) != null)
            @:{
                @:item.@(parameterName) = item.@(parameterName).ToString().ToObject<List<FileControlsModel>>();
            @:}
            @:else
            @:{
                @:item.@(parameterName) = new List<FileControlsModel>();
            @:}
@:
break;
case "select":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => item.@(parameterName).Equals(it.id))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
case "dictionary":
@if(column.IsMultiple)
{
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
            @:}
}
break;
case "dynamic":
            @:if(item.@(parameterName) != null)
            @:{
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
                @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", ""@(column.IsLinkage ? ", linkageParameters" : ""));
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
}
@:
break;
case "checkbox":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
            @:}
break;
case "dictionary":
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
            @:}
break;
case "dynamic":
            @:if(item.@(parameterName) != null)
            @:{
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
                @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", ""@(column.IsLinkage ? ", linkageParameters" : ""));
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
            @:}
break;
}
@:
break;
case "radio":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => item.@(parameterName).Equals(it.id))?.fullName;
            @:}
break;
case "dynamic":
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName);
            @:}
break;
}
@:
break;
case "cascader":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
        @if(column.ShowAllLevels){
                @:item.@(parameterName) = string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
        }else{
                @:item.@(parameterName) = @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName).FirstOrDefault();
        }
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
        @if(column.ShowAllLevels){
                    @:@(parameterName)Excessive.Add(string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName)));
        }else{
                    @:@(parameterName)Excessive.Add(@(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName).FirstOrDefault());
        }
                @:}
@:
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
break;
case "dictionary":
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
        @if(column.ShowAllLevels){
                @:item.@(parameterName) = string.Join("@(column.Separator)", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
        }else{
                @:item.@(parameterName) = await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).FirstOrDefault();
        }
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
        @if(column.ShowAllLevels){
                    @:@(parameterName)Excessive.Add(string.Join("@(column.Separator)", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(parameterName).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync()));
        }else{
                    @:@(parameterName)Excessive.Add(await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(parameterName).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).FirstOrDefault());
        }
                @:}
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
break;
case "dynamic":
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
        @if(column.ShowAllLevels){
                @:item.@(parameterName) = string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
        }else{
                @:item.@(parameterName) = @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName).FirstOrDefault();
        }
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
        @if(column.ShowAllLevels){
                    @:@(parameterName)Excessive.Add(string.Join("@(column.Separator)", @(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName)));
        }else{
                    @:@(parameterName)Excessive.Add(@(parameterName)Data.FindAll(it => @(parameterName).Contains(it.id)).Select(it => it.fullName).FirstOrDefault());
        }
                @:}
@:
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
break;
}
@:
break;
case "organizeSelect":
            @:// @column.ColumnComment
            @:item.@(parameterName) = _controlParsing.GetOrganizeName(@(column.IsMultiple.ToString().ToLower()),item.@(parameterName));
@:
break;
case "currOrganize":
            @:// @column.ColumnComment
            @:item.@(parameterName) = _controlParsing.GetCurrOrganizeName("@(column.ShowLevel)",item.@(parameterName));
@:
break;
case "depSelect":
            @:// @column.ColumnComment
            @:item.@(parameterName) = _controlParsing.GetDepartmentName(@(column.IsMultiple.ToString().ToLower()),item.@(parameterName));
@:
break;
case "posSelect":
@if(column.IsMultiple)
{
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<PositionEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
            @:}
@:
}
break;
case "userSelect":
@if(column.IsMultiple)
{
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<UserEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.RealName).ToListAsync());
            @:}
@:
}
break;
case "roleSelect":
@if(column.IsMultiple)
{
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<RoleEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
            @:}
@:
}
break;
case "groupSelect":
@if(column.IsMultiple)
{
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<GroupEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
            @:}
@:
}
break;
case "treeSelect":
            @:// @column.ColumnComment
@switch(column.ControlsDataType)
{
case "static":
            @:var @(parameterName)Data = "[@foreach(var data in column.StaticData){@("{\\\"id\\\":\\\"" + @data.id + "\\\",\\\"fullName\\\":\\\""+ @data.fullName + "\\\"}")@(dataCount == 1 ? "" : ",")dataCount--;}]".ToObject<List<StaticDataModel>>();
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
case "dictionary":
@if(column.IsMultiple)
{
            @:if(item.@(parameterName) != null)
            @:{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.@(column.Value == "id" ? "Id" : "EnCode")) && it.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(it => it.FullName).ToListAsync());
            @:}
}
break;
case "dynamic":
@if(column.IsLinkage)
{
                @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
                @:linkageParameters.Add(new DataInterfaceParameter
                @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
                @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
break;
}
@:
break;
case "areaSelect":
            @:// @column.ColumnComment
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join("/", await _repository.AsSugarClient().Queryable<ProvinceEntity>().Where(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.Id)).Select(it => it.FullName).ToListAsync());
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<List<string>>>();
                @:var @(parameterName)Excessive = new List<string>();
                @:foreach (var @(parameterName) in @(column.LowerColumnName + @column.upperBpmKey))
                @:{
                    @:@(parameterName)Excessive.Add(string.Join("/", await _repository.AsSugarClient().Queryable<ProvinceEntity>().Where(it => @(parameterName).Contains(it.Id)).Select(it => it.FullName).ToListAsync()));
                @:}
@:
                @:item.@(parameterName) = string.Join(",", @(parameterName)Excessive);
}
            @:}
@:
break;
case "popupTableSelect":
            @:// @column.ColumnComment
@if(column.IsLinkage)
{
            @:linkageParameters = new List<DataInterfaceParameter>();
@foreach(var linkage in column.LinkageConfig)
{
            @:linkageParameters.Add(new DataInterfaceParameter
            @:{
                    @:field = "@(linkage.field)",
                    @:relationField = @(linkage.sourceType==1 && linkage.relationField!="string.Empty" ? "item."+linkage.relationField : linkage.relationField),
                    @:isSubTable = @(linkage.isSubTable.ToString().ToLower()),
                    @:dataType = "@(linkage.dataType)",
                    @:defaultValue = "@(linkage.defaultValue)",
                    @:fieldName = "@(linkage.fieldName)",
                    @:sourceType = @(linkage.sourceType)
            @:});
}
}
            @:var @(parameterName)Data = await _dataInterfaceService.GetDynamicList("@(parameterName)",  "@(column.propsUrl)", "@(column.Value)", "@(column.Label)", "@(column.Children)"@(column.IsLinkage ? ", linkageParameters" : ""));
            @:if(item.@(parameterName) != null)
            @:{
@if(!column.IsMultiple)
{
                @:item.@(parameterName) = @(parameterName)Data.Find(it => it.id.Equals(item.@(parameterName)))?.fullName;
}else{
                @:var @(column.LowerColumnName + @column.upperBpmKey) = item.@(parameterName).ToObject<List<string>>();
                @:item.@(parameterName) = string.Join(",", @(parameterName)Data.FindAll(it => @(column.LowerColumnName + @column.upperBpmKey).Contains(it.id)).Select(it => it.fullName));
}
            @:}
@:
break;
}
}
}
    }
    void GetTheListDisplayFieldTemplate()
    {
@*循环展示字段*@
@foreach (var column in Model.TableField){
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@if (column.PrimaryKey){
                @:@(column.LowerColumnName) = <EMAIL>,
}else if(column.IsShow){
@switch(column.bpmKey)
{
case "datePicker":
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName).Value.ToString("@(column.Format)"),
break;
case "createTime":
case "modifyTime":
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName).Value.ToString("yyyy-MM-dd HH:mm:ss"),
break;
case "switch":
                @:@(parameterName) = SqlFunc.IIF(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName) == 1, "@(column.ActiveTxt)", "@(column.InactiveTxt)"),
break;
case "modifyUser":
case "createUser":
                @:@(parameterName) = SqlFunc.Subqueryable<UserEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_USER\")" : "").Where(u => u.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(u => SqlFunc.MergeString(u.RealName, "/", u.Account)),
break;
case "currPosition":
                @:@(parameterName) = SqlFunc.Subqueryable<PositionEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_POSITION\")" : "").Where(p => p.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(p => p.FullName),
break;
case "userSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<UserEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_USER\")" : "").Where(u => u.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(u => u.RealName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
case "posSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<PositionEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_POSITION\")" : "").Where(p => p.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(p => p.FullName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
case "depSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<OrganizeEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_ORGANIZE\")" : "").Where(o => o.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(o => o.FullName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
case "roleSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<RoleEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_ROLE\")" : "").Where(r => r.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(r => r.FullName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
case "groupSelect":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<GroupEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_GROUP\")" : "").Where(g => g.Id.Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName))).Select(g => g.FullName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
case "radio":
@switch(column.ControlsDataType)
{
case "dictionary":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<DictionaryDataEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_DICTIONARYDATA\")" : "").Where(dic => dic.@(column.Value == "id" ? "Id" : "EnCode").Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName)) && dic.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(dic => dic.FullName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
default:
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName)@(column.NetType.Contains("int") ? ".ToString()" : ""),
break;
}
break;
case "select":
case "treeSelect":
@switch(column.ControlsDataType)
{
case "dictionary":
@if(!column.IsMultiple)
{
                @:@(parameterName) = SqlFunc.Subqueryable<DictionaryDataEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.BASE_DICTIONARYDATA\")" : "").Where(dic => dic.@(column.Value == "id" ? "Id" : "EnCode").Equals(it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName)) && dic.DictionaryTypeId.Equals("@(column.propsUrl)")).Select(dic => dic.FullName),
}else{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
default:
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName)@(column.NetType.Contains("int") ? ".ToString()" : ""),
break;
}
break;
default:
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName)@(column.NetType.Contains("int") ? ".ToString()" : ""),
break;
}
}
}
@foreach (var column in Model.TableField)
{
@{var parameterName = string.Empty;}
@switch(column.IsAuxiliary)
{
case true:
@{parameterName = "bpm_" + @column.TableName + "_bpm_" + @column.LowerColumnName;}
break;
default:
@{parameterName = @column.LowerColumnName;}
break;
}
@switch(column.bpmKey)
{
case "treeSelect":
@if(column.IsTreeParentField && Model.TableType == 5)
{
@if(!column.IsShow)
{
                @:@(parameterName) = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
                @:@(parameterName)_pid = it@(column.IsAuxiliary ? "." + column.ClassName : "").@(column.ColumnName),
}
break;
}
}
@if(Model.EnableFlow)
{
                @:flowState = SqlFunc.Subqueryable<FlowTaskEntity>()@(Model.DbLinkId != "0" ? ".AS(\"db_"+ Model.ConfigId +"."+ Model.DBName +".dbo.FLOW_TASK\")" : "").Where(f => f.Id.Equals(it.@(Model.PrimaryKeyPolicy == 1 ? Model.PrimaryKey : "FlowTaskId"))).Select(f => f.Status),
                @:flowId = it.FlowId,
}
@if(Model.EnableFlow && Model.PrimaryKeyPolicy == 2)
{
                @:flowTaskId = it.FlowTaskId
}
    }
    void GetTheListQueryCriteriaTemplate()
    {
            @:.WhereIF(selectIds!=null && selectIds.Any() && input.dataType.Equals(2), it => selectIds.Contains(it.@(Model.PrimaryKey)))
@if(Model.PcKeywordSearchColumn!="")
{
            @:.WhereIF(_userManager.UserOrigin.Equals("pc") && input.bpmKeyword.IsNotEmptyOrNull(), it => @(Model.PcKeywordSearchColumn))
}
@if(Model.AppKeywordSearchColumn!="")
{
            @:.WhereIF(!_userManager.UserOrigin.Equals("pc") && input.bpmKeyword.IsNotEmptyOrNull(), it => @(Model.AppKeywordSearchColumn))
}
@foreach(var column in Model.TableField)
{
@if(column.QueryWhether && !column.IsAuxiliary)
{
@*查询方式*@
@switch(column.QueryType)
{
case 1:
@*多选控件*@
@if(column.QueryMultiple)
{
@switch(column.bpmKey)
{
case "organizeSelect":
            @:.Where(@(column.LowerColumnName)ConditionalModel)
break;
case "usersSelect":
            @:.Where(@(column.LowerColumnName)UsersSelectWhere)
break;
default:
            @:.Where(_controlParsing.GenerateMultipleSelectionCriteriaForQuerying(@(column.LowerColumnName)DbColumnName, input.@(column.LowerColumnName)))
break;
}
}else{
@switch(column.bpmKey)
{
case "currOrganize":
            @:.WhereIF(!string.IsNullOrEmpty(input.@(column.LowerColumnName)?.ToString()), it => it.@(column.ColumnName).Equals(input.@(column.LowerColumnName).ToJsonString()))
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
            @:.WhereIF(!string.IsNullOrEmpty(input.@(column.LowerColumnName)?.ToString()), it => it.@(column.ColumnName).Contains(@(column.LowerColumnName)))
break;
case "checkbox":
            @:.WhereIF(!string.IsNullOrEmpty(input.@(column.LowerColumnName)), it => it.@(column.ColumnName).Contains(input.@(column.LowerColumnName)))
break;
case "usersSelect":
            @:.Where(@(column.LowerColumnName)UsersSelectWhere)
break;
default:
@if(column.IsMultiple)
{
            @:.WhereIF(!string.IsNullOrEmpty(input.@(column.LowerColumnName)), it => it.@(column.ColumnName).Contains(input.@(column.LowerColumnName)))
}else{
            @:.WhereIF(!string.IsNullOrEmpty(input.@(column.LowerColumnName)), it => it.@(column.ColumnName).Equals(input.@(column.LowerColumnName)))
}
break;
}
}
break;
@*查询类型为模糊查询*@
case 2:
            @:.WhereIF(!string.IsNullOrEmpty(input.@(column.LowerColumnName)), it => it.@(column.ColumnName).Contains(input.@(column.LowerColumnName)))
break;
@*查询类型为范围查询*@
case 3:
@switch(column.bpmKey)
{
case "timePicker":
            @:.WhereIF(input.@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ColumnName), input.@(column.LowerColumnName).First(), input.@(column.LowerColumnName).Last()))
break;
case "inputNumber":
case "calculate":
case "rate":
case "slider":
            @:.WhereIF(input.@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ColumnName), start@(column.ColumnName), end@(column.ColumnName)))
break;
case "createTime":
case "modifyTime":
            @:.WhereIF(input.@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ColumnName), input.@(column.LowerColumnName).First().ParseToDateTime("yyyy-MM-dd HH:mm:ss"), input.@(column.LowerColumnName).Last().ParseToDateTime("yyyy-MM-dd HH:mm:ss")))
break;
default:
@if(column.IsDateTime)
{
            @:.WhereIF(input.@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ColumnName), input.@(column.LowerColumnName).First().ParseToDateTime("yyyy-MM-dd HH:mm:ss"), input.@(column.LowerColumnName).Last().ParseToDateTime("yyyy-MM-dd HH:mm:ss")))
}else{
            @:.WhereIF(input.@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ColumnName), input.@(column.LowerColumnName).First().ParseToDateTime("yyyy-MM-dd 00:00:00"), input.@(column.LowerColumnName).Last().ParseToDateTime("yyyy-MM-dd 23:59:59")))
}
break;
}
break;
}
}
}
@foreach(var column in Model.TableField)
{
@if(column.QueryWhether && column.IsAuxiliary)
{
@*查询方式*@
@switch(column.QueryType)
{
@*查询方式为等于*@
case 1:
@*多选控件*@
@if(column.QueryMultiple)
{
@switch(column.bpmKey)
{
case "organizeSelect":
            @:.WhereIF(auxiliary@(column.ColumnName)_@(column.TableNo)?.Count() > 0, it => SqlFunc.Exists(it.@(column.ClassName).@(column.ColumnName), auxiliary@(column.ColumnName)_@(column.TableNo)ConditionalModel))
break;
case "usersSelect":
            @:.WhereIF(auxiliary@(column.ColumnName)_@(column.TableNo)Where.Count > 0, it => SqlFunc.Exists(it.@(column.ClassName).@(column.ColumnName), auxiliary@(column.ColumnName)_@(column.TableNo)Where))
break;
default:
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Exists(it.@(column.ClassName).@(column.ColumnName), auxiliary@(column.ColumnName)_@(column.TableNo)ConditionalModel))
break;
}
}else{
@switch(column.bpmKey)
{
case "currOrganize":
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => it.@(column.ClassName).@(column.ColumnName).Equals(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).ToJsonString()))
break;
case "cascader":
case "areaSelect":
case "organizeSelect":
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => it.@(column.ClassName).@(column.ColumnName).Contains(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).Last()))
break;
case "checkbox":
            @:.WhereIF(!string.IsNullOrEmpty(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)), it => it.@(column.ClassName).@(column.ColumnName).Contains(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)))
break;
case "usersSelect":
            @:.WhereIF(auxiliary@(column.ColumnName)_@(column.TableNo)Where.Count > 0, it => SqlFunc.Exists(it.@(column.ClassName).@(column.ColumnName), auxiliary@(column.ColumnName)_@(column.TableNo)Where))
break;
default:
@if(column.IsMultiple)
{
            @:.WhereIF(!string.IsNullOrEmpty(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)), it => it.@(column.ClassName).@(column.ColumnName).Contains(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)))
}else{
            @:.WhereIF(!string.IsNullOrEmpty(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)), it => it.@(column.ClassName).@(column.ColumnName).Equals(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)))
}
break;
}
}
break;
@*查询类型为模糊查询*@
case 2:
            @:.WhereIF(!string.IsNullOrEmpty(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)), it => it.@(column.ClassName).@(column.ColumnName).Contains(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)))
break;
@*查询类型为范围查询*@
case 3:
@switch(column.bpmKey)
{
case "timePicker":
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ClassName).@(column.ColumnName), input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).First(), input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).Last()))
break;
case "inputNumber":
case "calculate":
case "rate":
case "slider":
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ClassName).@(column.ColumnName), startAuxiliary@(column.ColumnName)_@(column.TableNo), endAuxiliary@(column.ColumnName)_@(column.TableNo)))
break;
default:
@if(column.IsDateTime)
{
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ClassName).@(column.ColumnName), input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).First().ParseToDateTime("yyyy-MM-dd HH:mm:ss"), input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).Last().ParseToDateTime("yyyy-MM-dd HH:mm:ss")))
}else{
            @:.WhereIF(input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName)?.Count() > 0, it => SqlFunc.Between(it.@(column.ClassName).@(column.ColumnName), input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).First().ParseToDateTime("yyyy-MM-dd 00:00:00"), input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName).Last().ParseToDateTime("yyyy-MM-dd 23:59:59")))
}
break;
}
break;
}
}
}
    }
    void GetListQueryFieldTemplate()
    {
        @:var selectIds = input.selectIds?.Split(",").ToList();
@*循环列表查询字段*@
@if(Model.IsSearchMultiple)
{
        @:entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(Model.ClassName)Entity>();
}
@foreach(var column in Model.TableField)
{
@if(!column.IsAuxiliary && column.QueryWhether)
{
@switch(column.QueryType)
{
case 1:
@switch(column.bpmKey)
{
case "select":
case "depSelect":
case "roleSelect":
case "userSelect":
case "posSelect":
case "groupSelect":
@if(column.QueryMultiple)
{
        @:var @(column.LowerColumnName)DbColumnName = entityInfo.Columns.Find(it => it.PropertyName == "@(column.ColumnName)").DbColumnName;
}
break;
case "usersSelect":
        @:var @(column.LowerColumnName)UsersSelectWhere = _controlParsing.GetUsersSelectQueryWhere("@(column.OriginalColumnName)", input.@(column.LowerColumnName));
break;
case "organizeSelect":
@if(column.QueryMultiple)
{
        @:var @(column.LowerColumnName) = input.@(column.LowerColumnName).ParseToNestedList();
        @:var @(column.LowerColumnName)ConditionalModel = _controlParsing.GenerateMultipleSelectionCriteriaForQuerying(entityInfo.Columns.Find(it => it.PropertyName == "@(column.ColumnName)").DbColumnName, @(column.LowerColumnName));
}else{
        @:var @(column.LowerColumnName) = input.@(column.LowerColumnName)?.Last();
}
break;
case "cascader":
case "areaSelect":
        @:var @(column.LowerColumnName) = input.@(column.LowerColumnName)?.Last();
break;
default:
@if(column.IsMultiple)
{
        @:var @(column.LowerColumnName) = input.@(column.LowerColumnName)?.Last();
}
break;
}
break;
case 3:
@switch(column.bpmKey)
{
case "inputNumber":
case "calculate":
case "rate":
case "slider":
        @:var start@(column.ColumnName) = input.@(column.LowerColumnName)?.FirstOrDefault()?.ParseToDecimal() == null ? decimal.MinValue : input.@(column.LowerColumnName)?.First();
        @:var end@(column.ColumnName) = input.@(column.LowerColumnName)?.LastOrDefault()?.ParseToDecimal() == null ? decimal.MaxValue : input.@(column.LowerColumnName)?.Last();
break;
}
break;
}
}
}
@*循环子表查询条件*@
@foreach(var table in Model.AuxiliayTableRelations)
{
@if(table.IsSearchMultiple)
{
@:
        @:entityInfo = _repository.AsSugarClient().EntityMaintenance.GetEntityInfo<@(table.ClassName)Entity>();
}
@foreach(var column in table.ChilderColumnConfigList)
{
@if(column.QueryWhether)
{
@switch(column.QueryType)
{
case 1:
@switch(column.bpmKey)
{
case "select":
case "depSelect":
case "roleSelect":
case "userSelect":
case "posSelect":
case "groupSelect":
@if(column.QueryMultiple)
{
        @:var auxiliary@(column.ColumnName)_@(column.TableNo)ConditionalModel = _controlParsing.GenerateMultipleSelectionCriteriaForQuerying(entityInfo.Columns.Find(it => it.PropertyName == "@(column.ColumnName)").DbColumnName, input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName));
}
break;
case "usersSelect":
        @:var auxiliary@(column.ColumnName)_@(column.TableNo)Where = _controlParsing.GetUsersSelectQueryWhere("@(column.OriginalColumnName)", input.bpm_@(column.TableName)_bpm_@(column.LowerColumnName));
break;
case "organizeSelect":
@if(column.QueryMultiple)
{
        @:var auxiliary@(column.ColumnName)_@(column.TableNo) = input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName).ParseToNestedList();
        @:var auxiliary@(column.ColumnName)_@(column.TableNo)ConditionalModel = _controlParsing.GenerateMultipleSelectionCriteriaForQuerying(entityInfo.Columns.Find(it => it.PropertyName == "@(column.ColumnName)").DbColumnName, auxiliary@(column.ColumnName)_@(column.TableNo));
}else{
        @:var auxiliary@(column.ColumnName)_@(column.TableNo) = input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.Last();
}
break;
case "cascader":
case "areaSelect":
        @:var auxiliary@(column.ColumnName)_@(column.TableNo) = input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.Last();
break;
default:
@if(column.IsMultiple)
{
        @:var auxiliary@(column.ColumnName)_@(column.TableNo) = input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.Last();
}
break;
}
break;
case 3:
@switch(column.bpmKey)
{
case "inputNumber":
case "calculate":
case "rate":
case "slider":
        @:var startAuxiliary@(column.ColumnName)_@(column.TableNo) = input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.FirstOrDefault()?.ParseToDecimal() == null ? decimal.MinValue : input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.First();
        @:var endAuxiliary@(column.ColumnName)_@(column.TableNo) = input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.LastOrDefault()?.ParseToDecimal() == null ? decimal.MaxValue : input.bpm_@(table.OriginalTableName)_bpm_@(column.LowerColumnName)?.Last();
break;
}
break;
}
}
}
}
    }
    void GetTheNewDataMethodTemplate()
    {
@foreach(var column in Model.TableField)
{
@if(!column.IsAuxiliary)
{
@switch(column.bpmKey)
{
case "createTime":
        @:<EMAIL> = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now).ParseToDateTime();
break;
case "createUser":
        @:<EMAIL> = _userManager.UserId;
break;
case "currPosition":
        @:<EMAIL> = _userManager.User.PositionId.IsNullOrEmpty() ? null : _userManager.User.PositionId;
break;
case "currOrganize":
        @:<EMAIL> = _repository.AsSugarClient().Queryable<OrganizeEntity>().Where(it => _userManager.User.OrganizeId.Equals(it.Id)).Select(it => it.OrganizeIdTree)?.First()?.Split(",").ToJsonString();
break;
case "billRule":
        @:<EMAIL> = string.IsNullOrEmpty(<EMAIL>) ? await _billRullService.GetBillNumber("@(column.Rule)") : <EMAIL>;
break;
}
}
}
@if(Model.IsUnique)
{
@foreach(var column in Model.TableField)
{
@if(column.IsUnique && !column.IsAuxiliary)
{
@:
        @:if(await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<"+ Model.ClassName +"Entity>()." : "_repository.Is")AnyAsync(it => it.@(column.ColumnName).Equals(input.@(column.LowerColumnName))@(Model.IsLogicalDelete ? "  && it.DeleteMark == null" : "")))
            @:throw Oops.Bah(ErrorCode.COM1023, "@(column.ControlLabel)");
}
}
}
@*副表新增参数填充开始*@
@foreach(var table in Model.AuxiliayTableRelations)
{
@:
@if(table.FieldCount>0)
{
        @:var auxiliay@(table.ClassName) = input.Adapt<@(table.ClassName)CrInput>().Adapt<@(table.ClassName)Entity>();
}else{
        @:var auxiliay@(table.ClassName) = new @(table.ClassName)Entity();
}
@if(Model.PrimaryKeyPolicy == 1)
{
        @:auxiliay@(table.ClassName).@(table.PrimaryKey) = SnowflakeIdHelper.NextId();
}
@foreach(var column in Model.TableField)
{
@if(column.IsAuxiliary && column.TableName == table.OriginalTableName)
{
@switch(column.bpmKey)
{
case "createTime":
        @:auxiliay@(table.ClassName).@column.ColumnName = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now).ParseToDateTime();
break;
case "createUser":
        @:auxiliay@(table.ClassName).@column.ColumnName = _userManager.UserId;
break;
case "currPosition":
        @:auxiliay@(table.ClassName).@column.ColumnName = _userManager.User.PositionId.IsNullOrEmpty() ? null : _userManager.User.PositionId;
break;
case "currOrganize":
        @:auxiliay@(table.ClassName).@column.ColumnName = _repository.AsSugarClient().Queryable<OrganizeEntity>().Where(it => _userManager.User.OrganizeId.Equals(it.Id)).Select(it => it.OrganizeIdTree)?.First()?.Split(",").ToJsonString();
break;
case "billRule":
        @:auxiliay@(table.ClassName).@column.ColumnName = string.IsNullOrEmpty(auxiliay@(table.ClassName).@column.ColumnName) ? await _billRullService.GetBillNumber("@(column.Rule)") : auxiliay@(table.ClassName).@column.ColumnName;
break;
}
}
}
@*文本框唯一值判断开始*@
@foreach(var column in Model.TableField)
{
@if(column.IsAuxiliary && column.TableName == table.OriginalTableName && column.IsUnique)
{
@:
        @:if(await @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").Queryable<@(table.ClassName)Entity>().AnyAsync(it => it.@(column.ColumnName).Equals(auxiliay@(table.ClassName).@(column.ColumnName))@(Model.IsLogicalDelete ? "  && it.DeleteMark == null" : "")))
            @:throw Oops.Bah(ErrorCode.COM1023, "@(column.ControlLabel)");
}
}
@*文本框唯一值判断结束*@
@:
        @:entity.@(table.ClassName) = auxiliay@(table.ClassName);
}
@*副表新增参数填充结束*@
@:
        @:await @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").InsertNav(entity)
@foreach(var table in Model.AuxiliayTableRelations)
{
            @:.Include(it => it.@(table.ClassName))
}
            @:.ExecuteCommandAsync();
    }
    void GetAndModifyDataMethodTemplate()
    {
@foreach(var column in Model.TableField)
{
@if(!column.IsAuxiliary)
{
@switch(column.bpmKey)
{
case "modifyTime":
        @:<EMAIL> = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now).ParseToDateTime();
break;
case "modifyUser":
        @:<EMAIL> = _userManager.UserId;
break;
}
}
}
@foreach(var table in Model.AuxiliayTableRelations)
{
@:
@if(table.FieldCount>0)
{
        @:var auxiliay@(table.ClassName) = input.Adapt<@(table.ClassName)CrInput>().Adapt<@(table.ClassName)Entity>();
}else{
        @:var auxiliay@(table.ClassName) = new @(table.ClassName)Entity();
}
        @:auxiliay@(table.ClassName).@(table.TableField) = entity.@(table.RelationField);
@foreach(var column in Model.TableField)
{
@if(column.IsAuxiliary && column.TableName == table.OriginalTableName)
{
@switch(column.bpmKey)
{
case "modifyTime":
        @:auxiliay@(table.ClassName).@column.ColumnName = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now).ParseToDateTime();
break;
case "modifyUser":
        @:auxiliay@(table.ClassName).@column.ColumnName = _userManager.UserId;
break;
}
}
}
}
@if(Model.IsUnique)
{
@foreach(var column in Model.TableField)
{
@if(!column.IsAuxiliary && column.IsUnique)
{
@:
        @:if (await @(Model.DbLinkId != "0" ? "_sqlSugarClient.Queryable<" + Model.ClassName + "Entity>()." : "_repository.Is")AnyAsync(it => it.@(column.ColumnName).Equals(input.@(column.LowerColumnName))@(Model.IsLogicalDelete ? "  && it.DeleteMark == null" : "") && !it.@(Model.PrimaryKey).Equals(id)))
            @:throw Oops.Bah(ErrorCode.COM1023, "@(column.ControlLabel)");
}
}
}
        @:await @(Model.DbLinkId != "0" ? "_sqlSugarClient." : "_repository.As")Updateable(entity)
@*主表是否存在系统自动添加字段*@
@if(Model.IsUpdate || Model.ConcurrencyLock)
{
            @:.UpdateColumns(it => new {
@foreach(var column in Model.TableField)
{
@if(!column.IsAuxiliary && column.IsUpdate)
{
                @:it.@(column.ColumnName),
}
}
@if(Model.ConcurrencyLock)
{
                @:it.Version,
}
            @:})
}
@if(Model.ConcurrencyLock)
{
            @:.ExecuteCommandWithOptLockAsync(true);
}else{
            @:.ExecuteCommandAsync();
}
@foreach(var table in Model.AuxiliayTableRelations)
{
@:
@foreach(var column in Model.TableField)
{
@if(column.IsAuxiliary && column.IsUnique && column.TableName == table.OriginalTableName)
{
        @:if (await @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").Queryable<@(Model.ClassName)Entity>().AnyAsync(it => it.@(column.ColumnName).Equals(auxiliay@(table.ClassName).@(column.ColumnName))@(Model.IsLogicalDelete ? "  && it.DeleteMark == null" : "") && !it.@(table.TableField).Equals(entity.@(table.RelationField))))
            @:throw Oops.Bah(ErrorCode.COM1023, "@(column.ControlLabel)");
@:
}
}
        @:await @(Model.DbLinkId != "0" ? "_sqlSugarClient" : "_repository.AsSugarClient()").Updateable(auxiliay@(table.ClassName))
@if(table.IsUpdate)
{
            @:.UpdateColumns(it => new {
@foreach(var column in Model.TableField)
{
@if(column.IsAuxiliary && column.TableName == table.OriginalTableName && column.IsUpdate)
{
                @:it.@(column.ColumnName),
}
}
            @:})
}
            @:.Where(it => it.@(table.TableField) == entity.@(table.RelationField)).ExecuteCommandAsync();
}
    }
}