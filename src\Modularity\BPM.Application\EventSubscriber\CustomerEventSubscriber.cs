﻿using BPM.Application.Services;
using BPM.Common.Cache;
using BPM.Common.Core.Manager.Tenant;
using BPM.Common.Manager;
using BPM.Domain.Dto.customer;
using BPM.Domain.Entity.customer;
using BPM.Domain.Entity.shop;
using BPM.Domain.Requests.customer;
using BPM.Domain.Requests.subscriber;
using BPM.EventBus;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Text.RegularExpressions;
using static BPM.Application.SubscriberService;

namespace BPM.Application.EventSubscriber;

/// <summary>
/// 客户事件订阅.
/// </summary>
public class CustomerEventSubscriber : IEventSubscriber, IScoped, IDisposable
{
    /// <summary>
    ///  服务提供.
    /// </summary>
    private readonly SqlSugarProvider _repository;

    /// <summary>
    /// 缓存管理器
    /// </summary>
    private readonly ICacheManager _cache;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    ///  服务提供.
    /// </summary>
    private readonly ShopCacheService _shopCacheService;

    /// <summary>
    ///  服务提供.
    /// </summary>
    private readonly ISqlSugarClient _context;

    /// <summary>
    /// 缓存过期时间(秒)
    /// </summary>
    private const int CACHE_EXPIRE_SECONDS = 300;

    /// <summary>
    /// 构造函数.
    /// </summary>
    public CustomerEventSubscriber(ISqlSugarClient context, ITenantManager tenantManager, ICacheManager cache, IYouzanService youzanService, ShopCacheService shopCacheService)
    {
        _context = context;
        _repository = context.AsTenant().GetConnectionWithAttr<customerEntity>();
        _cache = cache;
        _youzanService = youzanService;
        _shopCacheService = shopCacheService;
    }

    /// <summary>
    /// 客户添加处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:Add")]
    public async Task AddCustomer(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var customer = source.Payload.ToString().ToObject<customerEntity>();
        await _repository.Insertable(customer).ExecuteCommandAsync();
    }

    /// <summary>
    /// 客户更新处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:Update")]
    public async Task UpdateCustomer(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var customer = source.Payload.ToString().ToObject<customerEntity>();
        await _repository.Updateable(customer).UpdateColumns(u =>
                    new {
                        u.nick_name,
                        u.birth_day,
                        u.gender,
                        u.state,
                        u.yzVersion,
                        u.request_id,
                        u.open_id,
                        u.modify_date
                    }).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 客户更新处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:UpdateId")]
    public async Task UpdateId(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var customer = source.Payload.ToString().ToObject<customerEntity>();
        await _repository.Updateable(customer).UpdateColumns(u =>
                    new {
                        u.tag_status,
                        u.request_id,
                        u.open_id,
                        u.modify_date
                    }).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 客户更新列表处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:UpdateCustomerList")]
    public async Task UpdateCustomerList(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var info = source.Payload.ToString().ToObject<customerInfoRequest>();

        var result = await _repository.Ado.UseTranAsync(async () =>
        {
            // 更新客户表
            await _repository.Updateable(info.Customers)
                .UpdateColumns(it => new { it.open_id, it.tag_mall_status, it.modify_date })
                .WhereColumns(it => new { it.phone })
                .ExecuteCommandAsync();

            // 更新权益表
            await _repository.Updateable(info.Equitys)
                .UpdateColumns(it => new { it.yz_open_id, it.tag_mall_status, it.modify_date })
                .WhereColumns(it => new { it.phone })
                .ExecuteCommandAsync();

            return true;
        });

        if (!result.IsSuccess)
        {
            throw new Exception($"批量更新失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 客户更新处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("CustomerEquity:UpdateTag")]
    public async Task UpdateTag(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var customer_equity = source.Payload.ToString().ToObject<customerEquityEntity>();

        // 检查数据是否已经在数据库中更新
        var existingEntity = await _repository.Queryable<customerEquityEntity>()
            .With(SqlWith.RowLock)
            .Where(x => x.phone == customer_equity.phone && x.equity_card_alias_id == customer_equity.equity_card_alias_id)
            .Select(p => new { p.modify_date })
            .FirstAsync();
        // 只有当现有实体不存在或者修改时间早于事件中的实体时才更新
        if (existingEntity == null || existingEntity.modify_date < customer_equity.modify_date)
        {
            await _repository.Updateable(customer_equity).UpdateColumns(u => new {
                u.tag_status,
                u.equity_card_no,
                u.status,
                u.tag_body,
                u.modify_date
            }).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 客户添加处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:Push")]
    public async Task PushCustomer(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var pushRequest = source.Payload.ToString().ToObject<BPM.Domain.Requests.subscriber.pushRequest>();
        await CustomerEvent(pushRequest);
    }

    /// <summary>
    /// 客户添加处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:PushUpdate")]
    public async Task PushUpdaterCustomer(EventHandlerExecutingContext context)
    {
        var source = (ChannelEventSource)context.Source;
        var pushRequest = source.Payload.ToString().ToObject<BPM.Domain.Requests.subscriber.pushRequest>();
        await CustomerEvent(pushRequest);
    }

    /// <summary>
    /// 客户信息同步处理.
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe("Customer:Sync")]
    public async Task SyncCustomer(EventHandlerExecutingContext context)
    {
        var customerId = context.Source.ToString();
        Log.Information($"开始处理客户信息同步事件: ID={customerId}");

        try
        {
            var customerService = App.GetService<CustomerService>();
            if (customerService != null)
            {
                await customerService.resyncCustomerInfo();
                Log.Information($"客户信息同步完成: ID={customerId}");

                // 执行存储过程syncCRM2AOS
                await _repository.Ado.UseStoredProcedure().ExecuteCommandAsync("syncCRM2AOS");
                Log.Information($"客户信息同步AOS完成: ID={customerId}");
            }
            else
            {
                Log.Warning($"未能获取CustomerService服务: ID={customerId}");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"客户信息同步失败: ID={customerId}, 错误={ex.Message}");
            // 更新同步状态为错误
            try
            {
                await _repository.Updateable<customerEntity>()
                    .SetColumns(it => new customerEntity
                    {
                        tag_status = "sync-error",
                        modify_date = DateTime.Now
                    })
                    .Where(it => it.id == customerId)
                    .ExecuteCommandAsync();
            }
            catch (Exception updateEx)
            {
                Log.Error($"更新同步状态失败: ID={customerId}, 错误={updateEx.Message}");
            }
        }
    }

    #region 客户事件
    /// <summary>
    /// 客户事件处理方法.
    /// </summary>
    /// <param name="pushRequest">推送请求参数，包含客户相关的事件信息</param>
    /// <returns>处理结果消息，成功返回空字符串，失败返回错误信息</returns>
    /// <remarks>
    /// 该方法主要处理以下功能：
    /// 1. 解析推送的客户信息
    /// 2. 根据客户ID或手机号查找已存在的客户记录
    /// 3. 处理客户创建和更新的逻辑
    /// 4. 同步客户信息到数据库
    /// 5. 发布相应的事件通知
    /// </remarks>
    public async Task<string> CustomerEvent(pushRequest pushRequest)
    {
        // 1. 解析客户信息
        var custPush = HttpUtility.UrlDecode(pushRequest.msg).ToObject<customerPushDto>();
        var originalAccountId = custPush.account_id;
        customerEntity customer = null;
        customerDto cust = null;

        // 2. 查找客户信息
        customer = await FindCustomerAsync(pushRequest, custPush);
        if (customer != null)
        {
            Log.Information($"[CUSTOMER_FOUND] 找到客户: yz_open_id={pushRequest.yz_open_id}, 客户ID={customer.id}, 手机号={customer.phone}");
        }

        // 3. 版本检查
        if (!await ValidateCustomerVersionAsync(customer, pushRequest))
        {
            return "";
        }

        // 4. 获取最新客户信息
        cust = await GetCustomerInfoAsync(pushRequest, originalAccountId);
        if (cust == null || string.IsNullOrEmpty(cust.mobile))
        {
            Log.Information($"[SKIP_PROCESS] 客户信息手机号为空，忽略处理: yz_open_id={pushRequest.yz_open_id}");
            return "";
        }

        // if (cust.cards == null || !cust.cards.Any())
        // {
        //     Log.Information($"[SKIP_PROCESS] 客户无权益卡信息，忽略处理: yz_open_id={pushRequest.yz_open_id}");
        //     return "";
        // }

        // 5. 获取店铺信息
        var store = await GetStoreInfoAsync(cust, pushRequest);
        string store_id = store?.id ?? pushRequest.kdt_id.ToString();

        // 6. 构造客户信息
        var customerInfo = await BuildCustomerInfoAsync(customer, cust, pushRequest, custPush, store_id);

        // 7. 保存数据
        var saveResult = await SaveCustomerDataAsync(customerInfo, customer, pushRequest);
        if (!string.IsNullOrEmpty(saveResult))
        {
            return saveResult;
        }

        // 8. 验证数据
        var verifyResult = await VerifyCustomerDataAsync(customerInfo, pushRequest);
        if (!string.IsNullOrEmpty(verifyResult))
        {
            Log.Error($"[VERIFY_FAIL] 数据验证失败: {verifyResult}");
            return verifyResult;
        }

        // 9. 清理缓存
        await _cache.DelAsync($"youzan:customer:{pushRequest.yz_open_id}");
        Log.Information($"[CACHE_REMOVE] 已删除客户缓存: yz_open_id={pushRequest.yz_open_id}");

        return "";
    }

    /// <summary>
    /// 查找客户信息的异步方法
    /// </summary>
    /// <param name="pushRequest">推送请求参数，包含有赞OpenID等信息</param>
    /// <param name="custPush">客户推送数据，包含账户ID和手机号等信息</param>
    /// <returns>返回查找到的客户实体，如果未找到则返回null</returns>
    /// <remarks>
    /// 该方法按以下优先级查找客户：
    /// 1. 首先通过有赞OpenID查找
    /// 2. 如果未找到，则通过账户ID查找
    /// 3. 最后通过手机号查找
    /// 所有查询都使用行锁确保数据一致性
    /// </remarks>
    private async Task<customerEntity> FindCustomerAsync(pushRequest pushRequest, customerPushDto custPush)
    {
        customerEntity customer = null;

        // 按优先级查找客户
        if (!string.IsNullOrEmpty(pushRequest.yz_open_id))
        {
            customer = await _repository.Queryable<customerEntity>()
                .With(SqlWith.RowLock)
                .FirstAsync(x => x.open_id.Equals(pushRequest.yz_open_id));
        }

        if (customer == null && !string.IsNullOrEmpty(custPush.account_id))
        {
            customer = await _repository.Queryable<customerEntity>()
                .With(SqlWith.RowLock)
                .FirstAsync(x => x.id.Equals(custPush.account_id));
        }

        if (customer == null && !string.IsNullOrEmpty(custPush.mobile))
        {
            customer = await _repository.Queryable<customerEntity>()
                .With(SqlWith.RowLock)
                .FirstAsync(x => x.phone.Equals(custPush.mobile));
        }

        return customer;
    }

    /// <summary>
    /// 验证客户版本信息的异步方法
    /// </summary>
    /// <param name="customer">客户实体对象</param>
    /// <param name="pushRequest">推送请求参数</param>
    /// <returns>返回验证结果：true表示验证通过，false表示验证失败</returns>
    /// <remarks>
    /// 该方法主要验证：
    /// 1. 如果是创建事件但客户已存在，则跳过处理
    /// 2. 如果推送版本号小于等于当前版本号，则跳过处理
    /// 用于避免重复处理和保证数据版本一致性
    /// </remarks>
    private async Task<bool> ValidateCustomerVersionAsync(customerEntity customer, pushRequest pushRequest)
    {
        if (customer == null) return true;

        if (pushRequest.status == "CUSTOMER_CREATED")
        {
            Log.Information($"[EVENT_SKIP] 客户已存在，忽略CUSTOMER_CREATED事件: yz_open_id={pushRequest.yz_open_id}");
            return false;
        }

        if (pushRequest.version <= customer.yzVersion && customer.yzVersion != null)
        {
            Log.Information($"[VERSION_SKIP] 忽略旧版本推送: yz_open_id={pushRequest.yz_open_id}, 当前版本={customer.yzVersion}, 推送版本={pushRequest.version}");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取客户详细信息的异步方法
    /// </summary>
    /// <param name="pushRequest">推送请求参数，包含有赞开放平台的相关信息</param>
    /// <param name="originalAccountId">原始账户ID，用于保持客户账户标识的一致性</param>
    /// <returns>返回客户详细信息DTO对象，如果获取失败则返回null</returns>
    private async Task<customerDto> GetCustomerInfoAsync(pushRequest pushRequest, string originalAccountId)
    {
        // 构建缓存键，使用有赞开放平台ID作为唯一标识
        string cacheKey = $"youzan:customer:{pushRequest.yz_open_id}";

        // 首先尝试从缓存中获取客户信息
        var cust = await _cache.GetAsync<customerDto>(cacheKey);

        // 如果缓存中没有找到客户信息
        if (cust == null)
        {
            try
            {
                // 获取有赞平台的访问令牌
                var token = await _youzanService.GetTokenAsync(pushRequest.kdt_id.ToString());

                // 构建调用有赞API的参数
                var param = new YouzanParameter
                {
                    url = "youzan.scrm.customer.detail.get/1.0.1",  // API接口地址
                    method = "POST",                                 // 请求方法
                    body = new {
                        fields = "user_base,benefit_cards",         // 需要获取的字段：基本信息和权益卡信息
                        yz_open_id = pushRequest.yz_open_id,        // 有赞开放平台用户ID
                        account_info = new {
                            account_id = "",                        // 账户ID（此处为空）
                            account_type = 5                        // 账户类型
                        }
                    }.ToJsonString()
                };

                // 调用有赞API获取客户详细信息
                var res = await _youzanService.GetData(param);

                // 如果API调用成功
                if (res.success)
                {
                    // 将返回的数据转换为客户DTO对象
                    cust = res.data.ToObject<customerDto>();
                    // 设置原始账户ID
                    cust.account_id = originalAccountId;
                    // 将客户信息存入缓存，设置300秒的过期时间
                    await _cache.SetAsync(cacheKey, cust, TimeSpan.FromSeconds(CACHE_EXPIRE_SECONDS));
                }
                else
                {
                    // 如果API调用失败，记录错误日志
                    Log.Error($"[API_ERROR] 获取客户详情失败: yz_open_id={pushRequest.yz_open_id}, 错误={res.message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                // 捕获并记录任何可能发生的异常
                Log.Error($"[API_ERROR] 获取客户详情异常: yz_open_id={pushRequest.yz_open_id}, 错误={ex.Message}");
                return null;
            }
        }
        else
        {
            // 如果从缓存中获取到了客户信息，更新账户ID
            cust.account_id = originalAccountId;
        }

        // 返回客户信息
        return cust;
    }

    /// <summary>
    /// 获取店铺信息
    /// </summary>
    /// <param name="cust">客户信息对象，包含归属店铺ID</param>
    /// <param name="pushRequest">推送请求对象，包含店铺ID</param>
    /// <returns>店铺实体对象，如果找不到返回null</returns>
    /// <remarks>
    /// 该方法通过以下步骤获取店铺信息：
    /// 1. 优先使用客户的归属店铺ID(ascription_kdt_id)
    /// 2. 如果客户没有归属店铺，则使用推送请求中的店铺ID(kdt_id)
    /// 3. 根据确定的店铺ID查询数据库获取完整的店铺信息
    /// </remarks>
    private async Task<shopEntity> GetStoreInfoAsync(customerDto cust, pushRequest pushRequest)
    {
        long kdt_id = cust.ascription_kdt_id == 0 ? Convert.ToInt64(pushRequest.kdt_id) : cust.ascription_kdt_id;
        return await _shopCacheService.GetSourceShopFromCache(kdt_id.ToString());
    }

    /// <summary>
    /// 构建客户信息实体的异步方法
    /// </summary>
    /// <param name="existingCustomer">已存在的客户实体，对于新客户为null</param>
    /// <param name="cust">从有赞API获取的客户信息数据</param>
    /// <param name="pushRequest">原始推送请求对象，包含版本号等信息</param>
    /// <param name="custPush">推送消息解析后的客户信息对象</param>
    /// <param name="store_id">关联的店铺ID</param>
    /// <returns>构建好的客户实体对象</returns>
    /// <remarks>
    /// 该方法根据以下规则构建客户实体：
    /// 1. 对于已存在的客户，保留原有的ID、创建时间、来源等信息
    /// 2. 对昵称和真实姓名进行特殊字符过滤，只保留中文、英文和数字
    /// 3. 限制头像URL长度不超过200字符，防止过长字段
    /// 4. 根据是否为新客户设置不同的标签状态(add/edit)
    /// 5. 记录有赞版本号，用于后续更新判断和乐观锁控制
    /// </remarks>
    private async Task<customerEntity> BuildCustomerInfoAsync(customerEntity existingCustomer, customerDto cust, pushRequest pushRequest, customerPushDto custPush, string store_id)
    {
        // 时间戳转换
        DateTime createdDate = existingCustomer?.created_date ?? DateTimeOffset.FromUnixTimeSeconds(cust.created_at).DateTime;

        return new customerEntity
        {
            id = pushRequest.id ?? existingCustomer?.id ?? custPush.account_id,
            customer_sn = cust.mobile,
            open_id = pushRequest.yz_open_id,
            nick_name = Regex.Replace((cust.latest_nickname ?? cust.wx_nickname ?? ""), @"[^\u4e00-\u9fa5a-zA-Z0-9]", ""),
            real_name = Regex.Replace((cust.show_name ?? cust.name ?? ""), @"[^\u4e00-\u9fa5a-zA-Z0-9]", ""),
            avatar = (cust.latest_avatar ?? cust.wx_avatar ?? "").Substring(0, Math.Min(200, (cust.latest_avatar ?? cust.wx_avatar ?? "").Length)),
            phone = cust.mobile,
            gender = cust.gender,
            store_id = store_id,
            created_date = createdDate,
            modify_date = DateTime.Now,
            source = existingCustomer?.source ?? "YZmall",
            state = custPush.is_log_off ? 0 : 1,
            org_phone = existingCustomer?.org_phone ?? cust.mobile,
            card_no = custPush.account_id,
            request_id = custPush.account_id,
            tag_mall_status = (existingCustomer.IsNullOrEmpty() ? "add" : "edit"),
            yzVersion = pushRequest.version
        };
    }

    /// <summary>
    /// 保存客户数据到数据库的异步方法
    /// </summary>
    /// <param name="customerInfo">要保存的客户实体对象</param>
    /// <param name="existingCustomer">已存在的客户实体，对于新客户为null</param>
    /// <param name="pushRequest">原始推送请求对象，包含版本号等信息</param>
    /// <returns>错误信息字符串，成功返回空字符串</returns>
    /// <remarks>
    /// 该方法负责将客户数据保存到数据库，包含以下关键处理：
    /// 1. 启动数据库事务确保数据一致性
    /// 2. 对于已存在的客户，首先更新版本号防止并发冲突
    /// 3. 根据客户是否存在采用不同的保存策略：
    ///    - 新客户：使用MERGE语句处理潜在的并发插入冲突
    ///    - 已有客户：更新客户信息，保留原有关键字段
    /// 4. 事务执行成功则提交，失败则回滚并返回错误信息
    /// 5. 特别处理数据库死锁情况，抛出明确异常供上层函数进行重试
    /// </remarks>
    private async Task<string> SaveCustomerDataAsync(customerEntity customerInfo, customerEntity existingCustomer, pushRequest pushRequest)
    {
        if (existingCustomer == null)
        {
            await SaveNewCustomerAsync(customerInfo);
        }
        else
        {
            await UpdateExistingCustomerAsync(customerInfo, existingCustomer, pushRequest.version);
        }

        return "";
    }

    /// <summary>
    /// 保存新客户数据的异步方法
    /// </summary>
    /// <param name="customerInfo">要保存的新客户实体对象</param>
    /// <remarks>
    /// 该方法使用MERGE语句处理新客户数据：
    /// 1. 首先检查是否存在相同ID、手机号或OpenID的记录
    /// 2. 如果不存在，则插入新记录
    /// 3. 使用参数化查询防止SQL注入
    /// 4. 如果MERGE失败，则尝试直接插入
    /// </remarks>
    private async Task SaveNewCustomerAsync(customerEntity customerInfo)
    {
        var sql = @"
                                MERGE INTO CUSTOMER AS target
                                USING (SELECT @Id as id, @Phone as phone, @OpenId as open_id) AS source
                                ON (target.id = source.id OR target.phone = source.phone OR 
                                   (target.open_id = source.open_id AND source.open_id IS NOT NULL AND source.open_id != ''))
                                WHEN NOT MATCHED THEN
                                    INSERT (id, customer_sn, open_id, union_id, nick_name, avatar, phone, gender, real_name, 
                                           store_id, created_date, modify_date, source, state, org_phone, card_no, request_id, 
                                           tag_mall_status, yzVersion)
                                    VALUES (@Id, @CustomerSn, @OpenId, @UnionId, @NickName, @Avatar, @Phone, @Gender, 
                                           @RealName, @StoreId, @CreatedDate, @ModifyDate, @Source, @State, @OrgPhone, 
                                           @CardNo, @RequestId, @TagMallStatus, @YzVersion);";

        var parameters = new {
            customerInfo.id,
            CustomerSn = customerInfo.customer_sn,
            OpenId = customerInfo.open_id,
            UnionId = customerInfo.union_id,
            NickName = customerInfo.nick_name,
            Avatar = customerInfo.avatar,
            Phone = customerInfo.phone,
            Gender = customerInfo.gender,
            RealName = customerInfo.real_name,
            StoreId = customerInfo.store_id,
            CreatedDate = customerInfo.created_date,
            ModifyDate = customerInfo.modify_date,
            Source = customerInfo.source,
            State = customerInfo.state,
            OrgPhone = customerInfo.org_phone,
            CardNo = customerInfo.card_no,
            RequestId = customerInfo.request_id,
            TagMallStatus = customerInfo.tag_mall_status,
            YzVersion = customerInfo.yzVersion
        };

        var affectedRows = await _repository.Ado.ExecuteCommandAsync(sql, parameters);
        if (affectedRows == 0)
        {
            await _repository.Insertable(customerInfo).ExecuteCommandAsync();
        }

        await _repository.Insertable(customerInfo).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新现有客户数据的异步方法
    /// </summary>
    /// <param name="customerInfo">要更新的客户信息</param>
    /// <param name="existingCustomer">已存在的客户实体</param>
    /// <param name="version">时间戳</param>
    /// <remarks>
    /// 该方法分两步更新客户数据：
    /// 1. 首先尝试更新基本信息（姓名、手机号等）
    /// 2. 如果OpenID不存在或已变更，则更新OpenID
    /// 3. 如果基本信息更新失败，则尝试完整更新
    /// 4. 保留某些字段不被更新（创建时间等）
    /// </remarks>
    private async Task UpdateExistingCustomerAsync(customerEntity customerInfo, customerEntity existingCustomer, long version)
    {
        var updateBuilder = _repository.Updateable<customerEntity>();
        updateBuilder.SetColumns(it => new customerEntity
        {
            request_id = customerInfo.id,
            store_id = customerInfo.store_id,
            phone = customerInfo.phone,
            nick_name = customerInfo.nick_name,
            real_name = customerInfo.real_name,
            avatar = customerInfo.avatar,
            gender = customerInfo.gender,
            modify_date = customerInfo.modify_date,
            yzVersion = version,
        });

        if ((string.IsNullOrEmpty(existingCustomer.open_id) || existingCustomer.open_id != customerInfo.open_id)
                                && !string.IsNullOrEmpty(customerInfo.open_id))
        {
            updateBuilder.SetColumns(it => it.open_id == customerInfo.open_id);
        }

        var updateRows = await updateBuilder
            .Where(x => x.phone == customerInfo.phone)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 验证客户数据完整性的异步方法
    /// </summary>
    /// <param name="customerInfo">要验证的客户信息</param>
    /// <param name="pushRequest">推送请求参数</param>
    /// <returns>验证结果消息，成功返回空字符串</returns>
    /// <remarks>
    /// 该方法进行最终的数据验证：
    /// 1. 最多重试3次验证
    /// 2. 验证客户ID、OpenID或手机号至少有一个匹配
    /// 3. 确保客户状态正常（未删除）
    /// 4. 记录验证过程的日志
    /// 5. 出现异常时进行适当的重试
    /// </remarks>
    private async Task<string> VerifyCustomerDataAsync(customerEntity customerInfo, pushRequest pushRequest)
    {
        const int MAX_VERIFY_RETRIES = 3;
        const int VERIFY_DELAY_MS = 1000;

        for (int verifyRetry = 0; verifyRetry < MAX_VERIFY_RETRIES; verifyRetry++)
        {
            try
            {
                // 获取查询条件
                var query = _repository.Queryable<customerEntity>()
                    .Where(x =>
                        x.id == customerInfo.id ||
                        (x.open_id != null && x.open_id == customerInfo.open_id) ||
                        x.phone == customerInfo.phone);

                var finalVerifyCustomer = await query.FirstAsync();

                if (finalVerifyCustomer != null)
                {
                    Log.Information($"[FINAL_VERIFY] 数据验证成功: yz_open_id={pushRequest.yz_open_id}, 客户ID={customerInfo.id}, 手机号={customerInfo.phone}");
                    return "";
                }

                if (verifyRetry < MAX_VERIFY_RETRIES - 1)
                {
                    Log.Warning($"[FINAL_VERIFY_RETRY] 验证失败，准备重试: yz_open_id={pushRequest.yz_open_id}, 重试次数={verifyRetry + 1}/{MAX_VERIFY_RETRIES}");
                    await Task.Delay(VERIFY_DELAY_MS * (1 << verifyRetry));
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[VERIFY_ERROR] 验证过程发生异常: yz_open_id={pushRequest.yz_open_id}, 重试次数={verifyRetry + 1}, 异常={ex.Message}");
                if (verifyRetry == MAX_VERIFY_RETRIES - 1)
                {
                    return $"验证过程发生异常: {ex.Message}";
                }
            }
        }

        return "数据验证失败，请检查数据完整性";
    }
    #endregion

    /// <summary>
    /// 
    /// </summary>
    public void Dispose()
    {
        _context.Dispose();
    }
}
