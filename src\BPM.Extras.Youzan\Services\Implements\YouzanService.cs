﻿using BPM.Common.Extension;
using BPM.Common.Manager;
using BPM.Common.Security;
using BPM.Extras.Youzan.Dto;
using BPM.Extras.Youzan.Options;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Result;
using BPM.FriendlyException;
using BPM.Logging;
using BPM.RemoteRequest;
using BPM.RemoteRequest.Extensions;
using BPM.UnifyResult;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;

namespace BPM.Extras.Youzan.Services.Implements;

/// <summary>
/// 有赞服务
/// </summary>
public class YouzanService : IYouzanService, ITransient
{
    /// <summary>
    /// 缓存管理.
    /// </summary>
    private readonly ICacheManager _cacheManager;

    /// <summary>
    /// 有赞配置.
    /// </summary>
    private readonly YouzanOptions _youzanOptions;

    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILogger<YouzanService> _logger;

    /// <summary>
    /// 访问令牌.
    /// </summary>
    public string token { get; private set; }

    /// <summary>
    /// 初始化一个<see cref="YouzanService"/>类型的新实例.
    /// </summary>
    /// <param name="cacheManager">缓存服务</param>
    /// <param name="youzanOptions">有赞配置</param>
    /// <param name="logger">日志服务</param>
    public YouzanService(ICacheManager cacheManager, IOptions<YouzanOptions> youzanOptions, ILogger<YouzanService> logger)
    {
        _cacheManager = cacheManager ?? throw new ArgumentNullException(nameof(cacheManager));
        _youzanOptions = youzanOptions.Value ?? throw new ArgumentNullException(nameof(youzanOptions));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 有赞token.
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task<string> GetTokenAsync(string grant_id, bool forceRefresh = false)
    {
        try
        {
            grant_id = grant_id.IsNullOrEmpty() ? _youzanOptions.GrantId : grant_id;
            
            // 如果强制刷新，则跳过缓存检查
            if (!forceRefresh)
            {
                // 获取缓存中的token
                token = await _cacheManager.GetAsync("youzan_" + grant_id);
                
                // 如果token存在，检查是否需要刷新
                if (token.IsNotEmptyOrNull())
                {
                    // 获取token的剩余有效期
                    var tokenInfo = await _cacheManager.GetAsync<TokenInfo>("youzan_token_info_" + grant_id);
                    
                    // 如果剩余有效期大于5分钟，则直接返回token
                    if (tokenInfo != null && (tokenInfo.ExpirationTime - DateTime.Now).TotalMinutes > 5)
                    {
                        return token;
                    }
                    
                    // 如果剩余有效期小于5分钟但大于0，记录日志
                    if (tokenInfo != null && (tokenInfo.ExpirationTime - DateTime.Now).TotalMinutes > 0)
                    {
                        _logger.LogInformation($"Token即将过期，剩余时间：{(tokenInfo.ExpirationTime - DateTime.Now).TotalMinutes:F2}分钟，准备刷新");
                    }
                }
            }
            else
            {
                _logger.LogInformation("强制刷新Token");
            }

            _logger.LogInformation("Token已过期或未找到，准备重新获取");

            // 重新获取token
            var param = new Dictionary<string, object>
            {
                { "client_id", _youzanOptions.ClientId },
                { "client_secret", _youzanOptions.ClientSecret },
                { "authorize_type", _youzanOptions.AuthorizeType },
                { "grant_id", grant_id },
                { "refresh", _youzanOptions.Refresh }
            };
            var res = await _youzanOptions.LoginPath.SetContentType("application/json").SetBody(param)
                .PostAsStringAsync();

            if (res.IsNotEmptyOrNull())
            {
                var result = res.ToObject<RESTfulResult<TokenDto>>();

                if (result.code == 200)
                {
                    token = result.data.access_token;
                    
                    // 设置7天过期时间
                    var expirationTime = DateTime.Now.AddDays(7);
                    
                    // 存储token
                    await _cacheManager.SetAsync("youzan_" + grant_id, token, new TimeSpan(7, 0, 0, 0));
                    
                    // 验证token是否正确写入
                    var cachedToken = await _cacheManager.GetAsync("youzan_" + grant_id);
                    if (string.IsNullOrEmpty(cachedToken) || cachedToken == "false")
                    {
                        _logger.LogError($"Token缓存写入失败");
                        throw Oops.Oh("Token缓存写入失败");
                    }
                    
                    // 存储token信息
                    var tokenInfo = new TokenInfo { Token = token, ExpirationTime = expirationTime };
                    await _cacheManager.SetAsync("youzan_token_info_" + grant_id, tokenInfo, new TimeSpan(7, 0, 0, 0));

                    _logger.LogInformation("Token更新成功，有效期至：" + expirationTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    return token;
                }
                else
                {
                    var errorMessage = $"获取有赞Token失败,失败原因: {result.msg}";
                    _logger.LogError(errorMessage);
                    throw Oops.Oh(errorMessage);
                }
            }
            else
            {
                var errorMessage = "获取有赞Token失败,无法获取到token信息";
                _logger.LogError(errorMessage);
                throw Oops.Oh(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Token过程中发生错误");
            return string.Empty;
        }
    }

    /// <summary>
    /// 获取数据.
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task<YouzanResult<object>> GetData(YouzanParameter param)
    {
        return await GetDataWithRetry(param, false);
    }

    /// <summary>
    /// 获取数据（带重试）
    /// </summary>
    /// <param name="param">请求参数</param>
    /// <param name="isRetry">是否是重试请求</param>
    /// <returns></returns>
    private async Task<YouzanResult<object>> GetDataWithRetry(YouzanParameter param, bool isRetry)
    {
        var grant_id = param.grant_id.IsNullOrEmpty() ? _youzanOptions.GrantId : param.grant_id;
        var query = new Dictionary<string, object>();
        
        // 确保token存在
        if (!token.IsNotEmptyOrNull())
        {
            await GetTokenAsync(grant_id);
        }
        
        // 如果token仍然为空，返回错误
        if (string.IsNullOrEmpty(token))
        {
            _logger.LogError("无法获取有效的Token，请求无法继续");
            return new YouzanResult<object>
            {
                code = "token_error",
                message = "无法获取有效的Token",
                success = false
            };
        }
        
        query.Add("access_token", token);
        var httpRequest = new HttpRequestPart();
        var url = _youzanOptions.BasicPath + "/" + param.url;
        
        // 记录请求URL和请求体
        Log.Information($"有赞接口请求URL: {url}");
        Log.Information($"有赞接口请求体: {param.body}");
        
        switch (param.method.ToUpper())
        {
            case "GET":
                httpRequest = url.SetHttpMethod(HttpMethod.Get).SetQueries(query);
                break;
            case "POST":
                httpRequest = url.SetHttpMethod(HttpMethod.Post).SetQueries(query);
                break;
            case "PUT":
                httpRequest = url.SetHttpMethod(HttpMethod.Put).SetQueries(query);
                break;
            case "DELETE":
                httpRequest = url.SetHttpMethod(HttpMethod.Delete).SetQueries(query);
                break;
        }
        httpRequest = httpRequest.SetContentType("application/json").SetBody(param.body);
        var resultString = await httpRequest.SetRetryPolicy(3, 1000).SendAsStringAsync();
        
        // 记录响应结果
        Log.Information($"有赞接口响应: {resultString}");
        
        // 检查响应是否为空
        if (string.IsNullOrEmpty(resultString))
        {
            _logger.LogError("有赞接口返回空响应");
            return new YouzanResult<object>
            {
                code = "empty_response",
                message = "接口返回空响应",
                success = false
            };
        }
        
        try
        {
            var obj = resultString.ToObject();
            if (obj?["gw_err_resp"]?.HasValues == true)
            {
                var errCode = obj["gw_err_resp"]?["err_code"]?.ToString() ?? "unknown_error";
                var errMsg = obj["gw_err_resp"]?["err_msg"]?.ToString() ?? "未知错误";
                
                // 检查是否是Token不存在错误，如果是且未重试过，则刷新Token并重试
                if (errCode == "4203" && errMsg.Contains("Token 不存在") && !isRetry)
                {
                    _logger.LogWarning("检测到Token不存在错误，尝试刷新Token并重试请求");
                    // 强制刷新Token
                    await GetTokenAsync(grant_id, true);
                    // 重试请求
                    return await GetDataWithRetry(param, true);
                }
                
                var result = new YouzanResult<object>
                {
                    code = errCode,
                    message = obj["gw_err_resp"]?.ToJsonString() ?? "未知错误",
                    success = false
                };
                return result;
            }
            
            try
            {
                return resultString.ToObject<YouzanResult<object>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析有赞响应数据失败");
                return new YouzanResult<object>
                {
                    code = "parse_error",
                    message = $"解析响应数据失败: {ex.Message}",
                    success = false,
                    data = resultString
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理有赞响应时发生错误");
            return new YouzanResult<object>
            {
                code = "process_error",
                message = $"处理响应时发生错误: {ex.Message}",
                success = false
            };
        }
    }

    private class TokenInfo
    {
        public string Token { get; set; }
        public DateTime ExpirationTime { get; set; }
    }
}