﻿using BPM.Common.Const;
using SqlSugar;

namespace BPM.Domain.Entity.shop;

/// <summary>
/// 门店支微信对账单
/// 版 本：V3.2
/// 版 权：中畅源科技开发有限公司（https://www.szclouds.com）
/// 作 者：Aarons
/// 日 期：2023-01-05.
/// </summary>
[SugarTable("SHOP_BILL_WECHAT")]
[Tenant(ClaimConst.TENANTID)]
public class shopWechatBillEntity
{
    /// <summary>
    /// 编号
    /// </summary>
    [SugarColumn(ColumnName = "F_id")]
    public string id { get; set; }

    /// <summary>
    /// 门店编号
    /// </summary>
    [SugarColumn(ColumnName = "F_ShopId")]
    public string shop_id { get; set; }

    /// <summary>
    /// 微信交易单号
    /// </summary>
    [SugarColumn(ColumnName = "F_TransactionId")]
    public string transaction_id { get; set; }

    /// <summary>
    /// 商户订单号
    /// </summary>
    [SugarColumn(ColumnName = "F_OutTradeNo")]
    public string out_trade_no { get; set; }

    /// <summary>
    /// 终端号
    /// </summary>
    [SugarColumn(ColumnName = "F_TerminalId")]
    public string terminal_id { get; set; }

    /// <summary>
    /// 交易时间
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeTime")]
    public DateTime trade_time { get; set; }

    /// <summary>
    /// 交易金额
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeFee")]
    public decimal trade_fee { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeType")]
    public string trade_type { get; set; }

    /// <summary>
    /// 交易状态
    /// </summary>
    [SugarColumn(ColumnName = "F_TradeState")]
    public string trade_state { get; set; }

    /// <summary>
    /// 应用编号
    /// </summary>
    [SugarColumn(ColumnName = "F_AppId")]
    public string app_id { get; set; }

    /// <summary>
    /// 商户编号
    /// </summary>
    [SugarColumn(ColumnName = "F_MchId")]
    public string mch_id { get; set; }

    /// <summary>
    /// 子商户编号
    /// </summary>
    [SugarColumn(ColumnName = "F_SubMchId")]
    public string sub_mch_id { get; set; }

    /// <summary>
    /// 设备号
    /// </summary>
    [SugarColumn(ColumnName = "F_DeviceInfo")]
    public string device_info { get; set; }


    /// <summary>
    /// 退款交易单号
    /// </summary>
    [SugarColumn(ColumnName = "F_RefundId")]
    public string refund_id { get; set; }

    /// <summary>
    /// 退款商户号
    /// </summary>
    [SugarColumn(ColumnName = "F_OutRefundNo")]
    public string out_refund_no { get; set; }

    /// <summary>
    /// 退款交易金额
    /// </summary>
    [SugarColumn(ColumnName = "F_RefundFee")]
    public decimal refund_fee { get; set; }

    /// <summary>
    /// 手续费
    /// </summary>
    [SugarColumn(ColumnName = "F_Charge")]
    public decimal charge { get; set; }

    /// <summary>
    /// 退款商户号
    /// </summary>
    [SugarColumn(ColumnName = "F_RefundState")]
    public string refund_state { get; set; }

    /// 内容
    /// </summary>
    [SugarColumn(ColumnName = "F_Body")]
    public string body { get; set; }

    /// <summary>
    /// 对账日期
    /// </summary>
    [SugarColumn(ColumnName = "F_BillDate")]
    public string bill_date { get; set; }

}

