﻿using BPM.Common.Security;
@*是否需要上传文件引用*@
@if(Model.IsUploading)
{
@:using BPM.Common.Models;
}
using BPM.@(Model.NameSpace).Entitys.Dto.@(Model.ClassName);
using Mapster;
 
namespace BPM.@(Model.NameSpace).<EMAIL>;

public class Mapper : IRegister
{
	public void Register(TypeAdapterConfig config)
	{
@*判断WebType 1-纯表单,2-常规表单*@
@switch(Model.WebType)
{
case 1:
@{GetCrInputToEntity();}
@if(Model.EnableFlow || Model.Type == 3){
GetEntityToInfoOutput();
}
break;
case 2:
@{GetCrInputToEntity(); }
@{GetEntityToListOutput();}
@{GetEntityToInfoOutput();}
@{GetEntityToDetailOutput();}
break;
}
	}
}
@{
	@*新增输入转实体*@
	void GetCrInputToEntity()
	{	
		@:config.ForType<@(Model.ClassName)CrInput, @(Model.ClassName)Entity>()
@*循环表字段*@
@foreach (var column in Model.TableField)
{
@*判断bpmKey不为空*@
@if (column.bpmKey != null)
{
switch(column.bpmKey)
{
@*下拉框控件*@
case "select":
case "userSelect":
case "usersSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
@*是否为多选*@
@if(column.IsMultiple)
{
			@:.Map(dest => dest.@(column.ColumnName), src => src.@(column.LowerColumnName) != null && src.@(column.LowerColumnName).Count > 0 ? src.@(column.LowerColumnName).ToJsonString() : null)
}
break;
@*复选框、级联、省市区、图片上传、文件上传*@
case "checkbox":
case "cascader":
case "organizeSelect":
case "areaSelect":
case "uploadImg":
case "uploadFile":
			@:.Map(dest => dest.@(column.ColumnName), src => src.@(column.LowerColumnName) != null && src.@(column.LowerColumnName).Count > 0 ? src.@(column.LowerColumnName).ToJsonString() : null)
break;
}
}
}
		@:;
	}
	@*新增输入转实体*@
	void GetEntityToListOutput()
	{	
		@:config.ForType<@(Model.ClassName)Entity, @(Model.ClassName)ListOutput>()
@*循环表字段*@
@foreach (var column in Model.TableField)
{
@if (column.bpmKey != null)
{
switch(column.bpmKey)
{
case "datePicker":
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).Value.ToString("@(column.Format)") : null)
break;
}
}
}
		@:;
	}
	@*新增输入转实体*@
	void GetEntityToDetailOutput()
	{	
		@:config.ForType<@(Model.ClassName)Entity, @(Model.ClassName)DetailOutput>()
@*循环表字段*@
@foreach (var column in Model.TableField)
{
@if (column.bpmKey != null)
{
switch(column.bpmKey)
{
case "uploadImg":
case "uploadFile":
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<FileControlsModel>>() : new List<FileControlsModel>())
break;
}
}
}
		@:;
	}
	@*实体转详情输出*@
	void GetEntityToInfoOutput()
	{
		@:config.ForType<@(Model.ClassName)Entity, @(Model.ClassName)InfoOutput>()
@foreach (var column in Model.TableField)
{
@if (column.bpmKey != null)
{
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<string>>() : null)
}
break;
case "cascader":
case "organizeSelect":
case "areaSelect":
@if(column.IsMultiple)
{
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<List<string>>>() : null)
}
else
{
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<string>>() : null)
}
break;
case "checkbox":
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<string>>() : null)
break;
case "uploadImg":
case "uploadFile":
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.ColumnName) != null ? src.@(column.ColumnName).ToObject<List<FileControlsModel>>() : new List<FileControlsModel>())
break;
}
}
}
		@:;
	}
}
@{
	@*列表转行内编辑输出*@
	void GetListOutputToInlineEditorOutput()
	{
		@:config.ForType<@(Model.ClassName)ListOutput, @(Model.ClassName)InlineEditorOutput>()
@foreach (var column in Model.TableField)
{
@if (column.IsShow)
{
switch(column.bpmKey)
{
case "select":
case "userSelect":
case "treeSelect":
case "depSelect":
case "posSelect":
case "popupTableSelect":
case "roleSelect":
case "groupSelect":
case "usersSelect":
@if(column.IsMultiple)
{
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.LowerColumnName) != null ? src.@(column.LowerColumnName).ToObject<List<string>>() : new List<string>())
}
break;
case "cascader":
case "organizeSelect":
case "areaSelect":
@if(column.IsMultiple)
{
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.LowerColumnName) != null ? src.@(column.LowerColumnName).ToObject<List<List<string>>>() : new List<List<string>>())
}
else
{
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.LowerColumnName) != null ? src.@(column.LowerColumnName).ToObject<List<string>>() : new List<string>())
}
break;
case "checkbox":
			@:.Map(dest => dest.@(column.LowerColumnName), src => src.@(column.LowerColumnName) != null ? src.@(column.LowerColumnName).ToObject<List<string>>() : new List<string>())
break;
}
}
}
		@:;
	}
}